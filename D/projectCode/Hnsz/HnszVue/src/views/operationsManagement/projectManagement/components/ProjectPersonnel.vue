<el-form-item label="岗位" prop="position">
  <!-- 删除: <el-input v-model="personnelForm.position" placeholder="请选择" readonly @click="openPositionDialog"></el-input> -->
  <el-input 
    v-model="personnelForm.position" 
    placeholder="请选择"
    readonly
    @click.native="openPositionDialog"
  ></el-input>
</el-form-item>

<script>
export default {
  methods: {
    // 打开岗位选择对话框
    openPositionDialog() {
      this.positionDialogVisible = true;
    },
    // 选择岗位
    selectPosition(selectedPosition) {
      if (selectedPosition.length > 0) {
        this.personnelForm.position = selectedPosition[0].roleName;
      }
      this.positionDialogVisible = false;
    }
  }
}
</script>

