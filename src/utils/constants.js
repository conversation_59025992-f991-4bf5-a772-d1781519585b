export const ROOT = '-1'

export const filterPost  = {
  "d9415b1d-da32-4e68-952a-0fdd63208d38": "监理员",
  "8027113a-2660-4d91-a92d-5b0c0ad86ba0": "总监理工程师",
  "a6e56f69-d06c-49d2-a6fa-62723bf6e78a": "专业监理工程师"
}

// 标段类型 施工
export const construction = 'd1c5565b-c1c0-4cfa-862c-c69544d947a1'
// 标段类型 监理
export const supervisor = 'c502372d-525e-4ce3-b1d5-a30705c1f22b'
// 标段类型 服务
export const service = 'f29a93d1-82b0-4125-9e58-37993cd088f0'
// 报表配置类型 功能
export const functions = 'ffed09d4-54b6-4515-85d6-f355ddd990b0'
// 报表配置类型 报表
export const report = '8c7ab11e-69e0-4f01-8a3f-09edc0e1a8ea'
// 报表配置类型 款项
export const funds = '679c2c4d-a37c-42e7-834b-750ce2708a1c'
// 生效状态
export const effectiveStatus = 'effectiveStatus'
// 客户端标识
export const clientType = 'pc'
// 智慧工地子系统
export const MPAY_SYSTEM = '19b9d85d-1f9a-4082-911a-6725c7e1d872'
// 系统ID
export const SYSTEM_ID = '3f8ad92b-e59e-4bb8-b4cc-407f69cb7b29'
// C类设计变更
export const designChangeTypeC = '0edb8aa0-7911-46ef-bb37-7f3fc8d7cd94'
// B类设计变更
export const designChangeTypeB = '6928b4a6-a161-436f-86d1-0eb806ec5299'
// A类设计变更
export const designChangeTypeA = '8ec51e3d-477c-4a80-b15f-0846244e8b1b'
// 较大设计变更
export const moreDesignChange = '64371a11-173a-4314-b4cc-63744854a095'
// 重大设计变更
export const majorDesignChange = 'cf32229e-213e-40ac-bc19-3c04f2a382e0'

// 变更清单类型  变更立项清单
export const changeListingTypeApproval = 'd0ab3c62-b2c3-47e2-8050-dce2ea886695'
// 变更清单类型  变更文件清单
export const changeListingTypeFile = 'a6938fe8-29d8-4f8f-8fe6-de41fef0a3e0'

// 会议纪要类型 变更立项会议纪要
export const meetingTypeApproval = 'c5d5d10f-70c3-4397-9da0-862646b2b17f'
// 会议纪要类型 变更文件会议纪要
export const meetingTypeFile = '63a3c841-6195-43ca-80b5-83c3cb983e78'

// 巡检方式 ping
export const pingInspectType = 'eef05404-7407-4487-ab8a-bcc49f1ac7a8'
// 巡检方式 telnet
export const telnetInspectType = '164f7658-23b9-47e4-8413-b4c9d0cd1771'
// 巡检方式 inf
export const infInspectType = '56d11102-3343-4e67-8679-32a0d0aa09f8'
// 巡检方式 service
export const serviceInspectType = '50f3e79b-f94d-4c95-8ff8-12ceefb7d0fe'

// 设备类型 水泥拌合站
export const cementDeviceType = '64d809eb-8e6f-40aa-8661-9f60e403eeef'

// 设备类型 压力机
export const pressureDeviceType = 'd3fb20c0-91d5-48a5-822c-dd6a6dd99644'

// 设备类型 万能机
export const universalDeviceType = 'e03b12c0-78b0-4172-87ab-5889b2d3606b'

// 计量配置类型 功能
export const functionsMeteringConfigType = '6d40503b-fb37-4b5c-9c75-44b26db2236a'
// 计量配置类型 款项
export const fundsMeteringConfigType = 'ab4c1988-403c-47fb-88bc-f36434b26357'
// 计量配置类型 报表
export const reportMeteringConfigType = 'f6c0e974-db39-45e0-8bc6-293bd01ad85f'

// 计量配置款项计量方式类型 公式计算
export const scriptFundsCountMode = '2413e7c4-ebbe-4859-81a0-a04b9dd06fea'

export const projectType = {
  mpay: 'cf32229e-213e-40ac-bc19-3c04f2a382e0'
}

// 推送数据类型 -- 项目
export const project = 'c18e9806-d0a5-4df4-9064-b6fb30edf3f6'
// 推送数据类型 --标段
export const bidSection = 'fdd31609-a40c-4c44-aa06-1383d0f96102'
// 标段类型 -隧道
export const tunnelType = 'f4148469-c985-4cce-a399-79eafea3610e'
// 三方类型 - 隧道
export const typeThirdTunnel = '58226d39-7b28-4be2-b965-57a2571d8173'

// 设备大类 - 环境监测
export const deviceMainTypeEnvironmentalMonitoring = '580e69fe-00e1-4168-b150-77c0e4797e93'

// 设备类型 - 气体基站
export const baseStationGas = 'c8a21004-cf46-45cf-8ec3-af1c9e4d4ea5'
// 设备类型 - 粉尘基站
export const baseStationDust = '56607dd5-bfa5-44d6-9a57-dcb088483e9f'
// 设备类型 - 温湿度基站
export const baseStationHumiture = '6938ab4a-e797-4643-b4b8-3bd84a41a359'

// 报表类型 - 项目统计表
export const projectStatistics = '26bf295a-d167-4510-aa23-d5fab1f69594'
// 报表类型 - 合同一览表
export const listOfContracts = 'bd76acbe-3beb-4f0f-8cdc-3442b7697100'
// 报表类型 - 计量变更统计表
export const meteringChangeReport = '498ec5f3-04cc-40ba-8f48-3949f35518de'
// 报表类型 - 计量支付统计表
export const meteringReport = '3c557010-ceaf-442f-80c3-f27d4ff7523f'
// 报表类型 - 施工计量支付一览表
export const constructionMeteringReport = '1b703cab-61aa-4e60-90aa-42c5ef76f8f7'
// 报表类型 - 监理计量支付一览表
export const supervisorMetering = '4d9e32b6-8d5c-4753-aa5e-920840199817'
// 报表类型 - 服务类计量支付一览表
export const serviceMetering = '0896aaba-22d7-4c62-845e-9f1419158249'
// 报表类型 - 变更一览表
export const changeFileReport = 'a6b8f3a7-73aa-4700-b62a-dd01d86eee06'
// 报表类型 - 合同管理
export const contract = '240f61e7-bc2c-4091-b29e-e380a5756631'
// 报表类型 - 计量申请
export const fundApply = 'cb05c978-5db3-4aaa-9ed2-8e03e42ad07b'
// 报表类型 - 计量支付
export const fundsManagement = '32c151fa-a5d0-4f99-a6ee-85118d7239b4'
// 报表类型 - 项目月报
export const monthlyProgressReport = '4b100ebf-5530-4f1c-85d1-f130317c2bae'
// 报表类型 - 实际施工进度计划
export const realitySchedule = 'cffafb2a-b905-4f66-842e-8a8f54491487'
// 文件访问地址
export const fileAddr = 'https://file.jxth.com.cn/'
// 文件存储文件夹名称
export const folderName = 'hnsz'

//报表的地址
export const reportAddr = 'https://report.jxth.com.cn'

export const webSocketUrl = 'wss://hnsz.jxth.com.cn/'
 //export const webSocketUrl = 'ws://127.0.0.1:8120/'
// 报表类型 - 项目信息
export const projectBasicInfo = 'dcd229be-2aa9-467e-8b04-1cb263dee8b1'
// 报表类型 -  项目立项
export const projectApprovalReport = '837f20a6-35b7-4b38-8b6a-60b578ffc404'
// 报表类型 -  招标信息
export const bidInfo = '7e375a52-e384-4616-8729-51b348e52ef5'

export const byUser = '539a6d62-f328-489c-88a1-84e7e1b51c0b'
export const byDepart = '9ac22d73-4df1-434c-8ae4-d1ca7390e3a7'
export const byRole = 'c305ab54-31d6-4169-8b20-b7d6cceda660'

export const permissionProject = 'd1d9948c-c603-4470-a7f1-6ca037df75f0'
// 立项项目权限
export const projectApproval = '1e2ddfde-9444-4665-8345-b29fb4db3a55'
// 招标项目权限
export const bidPower = '3ae1ed76-6b67-4db4-9271-edf32c2768c9'

export const allYearSchedule = '35c54ea8-c0f2-4f90-8f17-af1e3397c5df'
export const yearSchedule = 'fbb74bc8-d202-4b99-89cc-7456579cb4d6'
export const monthSchedule = '6b69100c-57ca-4a51-9aa6-7b76b49080ef'

export const startUserId = 'c5714700-51a0-485e-871b-1e6f13f106b8'

// 计量支付类型 计量
export const fundsManagementTypeApply = '1'

// 计量支付类型 支付
export const fundsManagementTypePay = '2'

/**
 * 招标范围
 * BIDSCOPE_EPC EPC
 * BIDSCOPE_DESIGN 设计
 * BIDSCOPE_CONSTRUCTION 施工
 * BIDSCOPE_MANAGE  监理
 */
export const BIDSCOPE_EPC = '0c5e884f-c51c-4e3d-8715-7cad4590d8d3'
export const BIDSCOPE_DESIGN = 'fb0cd28f-ae88-4a83-aba3-959bcffb4cb3'
export const BIDSCOPE_CONSTRUCTION = '113d5bf5-4617-4a2c-829f-ffc2ee40c1af'
export const BIDSCOPE_MANAGE = '5901bf37-b98e-45d7-be36-55bd9e711f5e'

// 是否重大合同 否
export const IS_IMPORTANCE_CONTRACT_YES = 'e6de2447-79ed-49b4-8f21-29ae57d82cfa'

// 是否重大合同 是
export const IS_IMPORTANCE_CONTRACT_NO = 'f544eb23-c841-40d6-b458-54975b063b86'

// 默认字体大小
export const DEFAULT_FONTSIZE = 16

// 合同类型
export const INVESTSTYLE = '75d84a2f-8c5f-49c4-8d4a-4fd86c1e2c65'

// 系统id
export const CITY_SYSTEM = '19b9d85d-1f9a-4082-911a-6725c7e1d872'

// 文件访问地址
export const appFileAddr = 'https://file.jxth.com.cn/'

// 权限关联类型-按用户
export const BY_USER = "539a6d62-f328-489c-88a1-84e7e1b51c0b"

// 权限关联类型-按角色
export const BY_ROLE = "c305ab54-31d6-4169-8b20-b7d6cceda660"

// 权限关联类型-按部门
export const BY_DEPART = "9ac22d73-4df1-434c-8ae4-d1ca7390e3a7"

/**
 * 用户的社交平台的类型枚举
 */
export const SystemUserSocialTypeEnum = {
  // DINGTALK: {
  //   title: "钉钉",
  //   type: 20,
  //   source: "dingtalk",
  //   img: "https://s1.ax1x.com/2022/05/22/OzMDRs.png",
  // },
  // WECHAT_ENTERPRISE: {
  //   title: "企业微信",
  //   type: 30,
  //   source: "wechat_enterprise",
  //   img: "https://s1.ax1x.com/2022/05/22/OzMrzn.png",
  // },
  WECHAT_MP: {
    title: "微信",
    type: 32,
    source: "wechat_mp",
    img: "img/wechat.png",
  }
}
export const defaultAppMenu = [
    {
        "groupName": "企业管理",
        "remark": "",
        "userWorkbenchMenus": [
            {
                "aliasName": "投标管理",
                "path": "/enterpriseManagement/operationsManagement/bidManagementOne",
                "code": "bidManagementOne",
                "menuId": "c064aef3-9c81-4fb9-9541-39934c4b4c2d",
                "remark": "",
                "menuName": "投标管理",
                "id": "f272fe70-f8c5-40b7-835e-190e38998c58",
                "sort": 0,
                "type": "1",
                "workbenchMenuId": "913ee28c-4799-4d78-a479-7d5e1e1fc2fd",
                "urlPath": "/group1/M00/01/AA/wKgKZ2aLqf2AaMfZAAAlr17Y0Pw700.png"
            },
            {
                "aliasName": "项目信息",
                "path": "/enterpriseManagement/projectManagement/projectInitiation",
                "code": "projectInitiation",
                "menuId": "e73cf68c-fde4-413f-aa54-09c446ffa8f5",
                "remark": "",
                "menuName": "项目信息",
                "id": "daa84426-14ec-4ca5-96bc-bf1598bd2e5e",
                "sort": 1,
                "type": "1",
                "workbenchMenuId": "44dd1819-9999-40ca-ab90-d0c8ff20b660",
                "urlPath": "/group1/M00/01/A9/wKgKZ2aLowWASxQsAAAx49OQD5A348.png"
            },
            {
                "aliasName": "客户管理",
                "path": "/enterpriseManagement/operationsManagement/cooperativePartner/customerManagement",
                "code": "customerManagement",
                "menuId": "5e90e5ed-a64c-46f9-8192-d4b49bbd38d3",
                "remark": "",
                "menuName": "客户管理",
                "id": "65f2dbf9-aed3-4b53-a643-12bfcbeb87cf",
                "sort": 2,
                "type": "1",
                "workbenchMenuId": "6f500ba4-6851-4961-b7e2-3a0fcd2bf0be",
                "urlPath": "/group1/M00/01/AA/wKgKZ2aLqf2AehxAAAAyWqc4RRo280.png"
            },
            {
                "aliasName": "供应商管理",
                "path": "/enterpriseManagement/operationsManagement/cooperativePartner/supplierManagement",
                "code": "supplierManagement",
                "menuId": "e300534b-1b59-48ba-894a-dd8a1b06a687",
                "remark": "",
                "menuName": "供应商管理",
                "id": "8cd6f432-3f73-4d65-a02e-816ba5f9ce66",
                "sort": 3,
                "type": "1",
                "workbenchMenuId": "ee8d35df-c871-4ecc-84e2-9fa906ebc164",
                "urlPath": "/group1/M00/01/AA/wKgKZ2aLqf2ARc07AAAuQuYC4y0786.png"
            },
            {
                "aliasName": "收入合同",
                "path": "/enterpriseManagement/contractManagement/revenueContract",
                "code": "revenueContract",
                "menuId": "1ba1090a-9680-4f7f-89bd-3819dab4764c",
                "remark": "",
                "menuName": "收入合同",
                "id": "56d76585-f55e-44c0-9e7a-4b7c7bf6fccf",
                "sort": 4,
                "type": "1",
                "workbenchMenuId": "547e0b70-6080-46e0-861e-08cd3518b003",
                "urlPath": "/group1/M00/01/AA/wKgKZ2aLqf2AOU9DAAAt5xOxQZg447.png"
            },
            {
                "aliasName": "人员资质",
                "path": "/enterpriseManagement/certificateManagement/personnelCertificateNew",
                "code": "personnelCertificateNew",
                "menuId": "94528c95-808d-4ba3-87b9-b2c14d32adb4",
                "remark": "",
                "menuName": "人员资质",
                "id": "e4054fd9-b1c3-4e95-8590-007527a19680",
                "sort": 5,
                "type": "1",
                "workbenchMenuId": "c27af6d3-3e58-4a47-ad4c-686cafc961b4",
                "urlPath": "/group1/M00/01/A9/wKgKZ2aLqXWACszpAAAyLVeITJE879.png"
            },
            {
                "aliasName": "花名册",
                "path": "/enterpriseManagement/administrativeManagement/personnelManagement/roster",
                "code": "roster",
                "menuId": "a1437fad-2460-41de-8a01-5296092bf58f",
                "remark": "",
                "menuName": "花名册",
                "id": "369062de-a078-49bd-8558-00355ea628a9",
                "sort": 6,
                "type": "1",
                "workbenchMenuId": "34fc48a2-ffa0-4a24-bba6-c2fe15102816",
                "urlPath": "/group1/M00/01/A9/wKgKZ2aLqZyALLyeAAAy-f6G5WQ496.png"
            },
            {
                "aliasName": "学历信息",
                "path": "/enterpriseManagement/administrativeManagement/personnelManagement/schoolInformationList",
                "code": "schoolInformationList",
                "menuId": "8e13e7af-fd6a-42cc-be73-47fb00c756fd",
                "remark": "",
                "menuName": "学历信息",
                "id": "35a5c85a-d953-4d5c-9118-19e3f1327296",
                "sort": 7,
                "type": "1",
                "workbenchMenuId": "9d989363-b094-4a0a-b458-c9819bd091d1",
                "urlPath": "/group1/M00/01/A9/wKgKZ2aLqZyAJ-ecAAAjAGVf9fo500.png"
            },
            {
                "aliasName": "员工合同",
                "path": "/enterpriseManagement/administrativeManagement/personnelManagement/contractInformationList",
                "code": "contractInformationList",
                "menuId": "1f0f5560-04cf-423c-b2c6-03cdda52a53e",
                "remark": "",
                "menuName": "合同信息",
                "id": "59f6d2d7-b1ae-4874-8cfd-fe70cc0e0204",
                "sort": 8,
                "type": "1",
                "workbenchMenuId": "6f19cb9d-85c9-4f03-8957-ae61e94d1543",
                "urlPath": "/group1/M00/01/AA/wKgKZ2aLqf2AOU9DAAAt5xOxQZg447.png"
            },
            {
                "aliasName": "员工证件",
                "path": "/enterpriseManagement/administrativeManagement/personnelManagement/personnelCertificate",
                "code": "personnelCertificate",
                "menuId": "82dfba26-091d-43e3-8193-3b30f84884be",
                "remark": "",
                "menuName": "人员证件",
                "id": "f87c4661-3ca9-4798-917d-3dd67baad313",
                "sort": 9,
                "type": "1",
                "workbenchMenuId": "e951e691-9c60-41fc-857b-2ba7c0036bcb",
                "urlPath": "/group1/M00/01/A9/wKgKZ2aLqZyALLyeAAAy-f6G5WQ496.png"
            },
            {
                "aliasName": "考勤配置",
                "path": "/enterpriseManagement/administrativeManagement/attendance/attendanceManagement",
                "code": "attendanceManagement",
                "menuId": "119d36f2-db96-423c-ad6d-4dfc314f0049",
                "remark": "",
                "menuName": "考勤配置",
                "id": "55991c6e-4fdd-4d92-8189-b7bb7f2db518",
                "sort": 10,
                "type": "1",
                "workbenchMenuId": "28d261f3-f372-4fb7-8822-54f79085307d",
                "urlPath": "/group1/M00/01/A9/wKgKZ2aLqZyAOo7rAAAook-6tmk432.png"
            },
            {
                "aliasName": "打卡记录",
                "path": "/enterpriseManagement/administrativeManagement/attendance/punchInRecordList",
                "code": "punchInRecordList",
                "menuId": "69dba064-3510-4b5b-97ca-62f61b4d45ea",
                "remark": "",
                "menuName": "打卡记录",
                "id": "8ba26016-ceed-4c29-8de9-83e0308fad5c",
                "sort": 11,
                "type": "1",
                "workbenchMenuId": "f2f9d0c1-a5af-4cde-812f-6a6d3c4a2b39",
                "urlPath": "/group1/M00/01/A9/wKgKZ2aLqZyAbP30AAAgYyTwps4234.png"
            },
            {
                "aliasName": "考勤报表",
                "path": "/enterpriseManagement/administrativeManagement/attendance/attendanceReport",
                "code": "attendanceReport",
                "menuId": "8319b222-c06d-420e-8af2-830001bfb560",
                "remark": "",
                "menuName": "考勤报表",
                "id": "a2c56f00-9142-4381-8b07-cd0538908f5d",
                "sort": 12,
                "type": "1",
                "workbenchMenuId": "d83fd428-897c-4624-8e49-0b61ee34bf5b",
                "urlPath": "/group1/M00/01/A9/wKgKZ2aLqZyAS6A5AAAmy9TQqg8515.png"
            },
            {
                "aliasName": "收文管理",
                "path": "/enterpriseManagement/administrativeManagement/receivingAndSendingDocuments/receivedDocuments",
                "code": "receivedDocuments",
                "menuId": "ba70707b-a0af-4546-9bd1-e0e7e0fe14eb",
                "remark": "",
                "menuName": "收文管理",
                "id": "e1efc98a-2d14-4855-88fe-fa8f2ed709ff",
                "sort": 13,
                "type": "1",
                "workbenchMenuId": "a8649de3-8f26-4ee3-8032-194c3f51e5ed",
                "urlPath": "/group1/M00/01/A9/wKgKZ2aLqZyAHJsaAAAhcrdg0ts511.png"
            },
            {
                "aliasName": "发文管理",
                "path": "/enterpriseManagement/administrativeManagement/receivingAndSendingDocuments/sendingDocuments",
                "code": "sendingDocuments",
                "menuId": "d87bd996-01fa-4292-8416-ab42a636c01a",
                "remark": "",
                "menuName": "发文管理",
                "id": "4b62ad24-a86b-4097-984c-d59bb8facf46",
                "sort": 14,
                "type": "1",
                "workbenchMenuId": "9d4890a1-db3e-40fd-853c-3e04ea9a8996",
                "urlPath": "/group1/M00/01/A9/wKgKZ2aLqXWAHQvGAAAlp83V-pE151.png"
            },
            {
                "aliasName": "新闻动态",
                "path": "/enterpriseManagement/informationManagement/newsManagement/newsInformation",
                "code": "newsInformation",
                "menuId": "6c93efe1-23cc-48fc-8256-9544cab1cdc6",
                "remark": "",
                "menuName": "新闻动态",
                "id": "dddf9efa-1d50-45aa-8024-f29e8e56d816",
                "sort": 15,
                "type": "1",
                "workbenchMenuId": "3ea4929c-039a-4c2e-804c-f4ab5a74f902",
                "urlPath": "/group1/M00/01/A9/wKgKZ2aLowWAPET8AAAwIUiLK1g168.png"
            },
            {
                "aliasName": "产品管理",
                "path": "/enterpriseManagement/informationManagement/newsManagement/productManagement",
                "code": "productManagement",
                "menuId": "a5e4dd68-cc02-43cd-8b03-cd688a5ddad3",
                "remark": "",
                "menuName": "产品管理",
                "id": "630a76da-8499-4b61-9123-fd387087acd6",
                "sort": 16,
                "type": "1",
                "workbenchMenuId": "ac5e1824-b317-46cd-91c8-4d47fe48baaf",
                "urlPath": "/group1/M00/01/A9/wKgKZ2aLowSAFlpxAAApZld5UlM354.png"
            }
        ],
        "id": "ae97a88e-10d9-4ce7-8406-903cda35b15a",
        "sort": 1,
        "status": "1"
    },
    {
        "groupName": "现场管理",
        "remark": "",
        "userWorkbenchMenus": [
            {
                "aliasName": "分项列表",
                "path": "/onSiteManagement/measurementManagement/bidSectionItemized",
                "code": "bidSectionItemized",
                "menuId": "b3102c6c-e573-4f64-83bf-9bd588664814",
                "remark": "",
                "menuName": "分项列表",
                "id": "0b5e05b6-4b24-4818-834a-aa0d3efb7d97",
                "sort": 0,
                "type": "1",
                "workbenchMenuId": "fc3a6590-7a39-4f16-86e4-3e51b71d977c",
                "urlPath": "/group1/M00/01/A9/wKgKZ2aLqU2AUOgTAAAjA1Cqimw070.png"
            },
            {
                "aliasName": "监理指令",
                "path": "/onSiteManagement/superiorInstruct/supervisionInstruction",
                "code": "supervisionInstruction",
                "menuId": "1fa196dc-c164-4967-82dd-0c08f9a450ec",
                "remark": "",
                "menuName": "监理指令",
                "id": "b17c3d72-e336-42fa-94c4-6db16099d620",
                "sort": 1,
                "type": "1",
                "workbenchMenuId": "255add45-c806-4273-858d-36657634a439",
                "urlPath": "/group1/M00/01/A9/wKgKZ2aLowWAHcUSAAAwMMSqkjI110.png"
            },
            {
                "aliasName": "无人机巡查",
                "path": "/onSiteManagement/skydrone/device-management",
                "code": "device-management",
                "menuId": "82a682b5-784d-4b8b-84ac-172149c4fd03",
                "remark": "",
                "menuName": "设备管理",
                "id": "48beceab-70a3-4f1d-887e-5d9d88c0e914",
                "sort": 2,
                "type": "1",
                "workbenchMenuId": "1044ad4e-5c87-4f28-920e-d16ae5530258",
                "urlPath": "/group1/M00/01/A9/wKgKZ2aLqZyAOo7rAAAook-6tmk432.png"
            },
            {
                "aliasName": "安全帽通话",
                "path": "/onSiteManagement/safetyHelmetManagement/safetyHelmetManagemenTest",
                "code": "safetyHelmetManagemenTest",
                "menuId": "ab9403b3-6f87-4193-8640-ae9afbdc5009",
                "remark": "",
                "menuName": "安全帽通话",
                "id": "7be9d476-9a8b-44c5-8a6e-065b4d586c2e",
                "sort": 3,
                "type": "1",
                "workbenchMenuId": "900cd846-6f57-4cd7-b1e6-0c151b370dc9",
                "urlPath": "/group1/M00/01/A9/wKgKZ2aLqU6AWIxgAAAmU2pzzS4219.png"
            },
            {
                "aliasName": "巡视记录",
                "path": "/onSiteManagement/jobLog/inspectionRecord",
                "code": "inspectionRecord",
                "menuId": "cbe0f685-49bc-42ad-bcbf-ab8cf6ffd8ca",
                "remark": "",
                "menuName": "巡视记录",
                "id": "7b64465f-f20a-4f02-8348-98c785c5fcf2",
                "sort": 4,
                "type": "1",
                "workbenchMenuId": "51e1f271-feaa-4ed8-884a-247235f02cb9",
                "urlPath": "/group1/M00/01/A9/wKgKZ2aLowWAT-6jAAAtHvzxWYI529.png"
            },
            {
                "aliasName": "旁站记录",
                "path": "/onSiteManagement/jobLog/sideStationRecord",
                "code": "sideStationRecord",
                "menuId": "7d2d7ee8-42a8-499f-81e9-0c13b15cbd8a",
                "remark": "",
                "menuName": "旁站记录",
                "id": "780af04e-c267-44c0-8c5d-87af86a01754",
                "sort": 5,
                "type": "1",
                "workbenchMenuId": "87c459ca-ec80-4c37-8662-cf96f1fef1d6",
                "urlPath": "/group1/M00/01/AA/wKgKZ2aLq1GAEydbAAA0Hqb1CSs891.png"
            },
            {
                "aliasName": "监理日志",
                "path": "/onSiteManagement/jobLog/superviseLog",
                "code": "superviseLog",
                "menuId": "216cc190-0b42-45f9-8d90-bffb28531f5c",
                "remark": "",
                "menuName": "监理日志",
                "id": "15032cf9-88bd-421f-8278-39d1b984be16",
                "sort": 6,
                "type": "1",
                "workbenchMenuId": "1fa05167-4a6e-4e93-852d-99cb66bbba84",
                "urlPath": "/group1/M00/01/AA/wKgKZ2aLqf2AarizAAAj-Waem6c770.png"
            }
        ],
        "id": "a0e281f0-29fb-4865-8740-9d32293d9fb4",
        "sort": 2,
        "status": "1"
    },
    {
        "groupName": "平台管理",
        "remark": "",
        "userWorkbenchMenus": [
            {
                "aliasName": "用户管理",
                "path": "/backend/user",
                "code": "user",
                "menuId": "e36ea5d7-9929-4ea0-b403-84d212edfd39",
                "remark": "",
                "menuName": "用户管理",
                "id": "9828dff9-3d1c-4000-813d-88b049083bfd",
                "sort": 0,
                "type": "1",
                "workbenchMenuId": "d0bfebe6-83f1-4505-81a2-db4fbf6f87d5",
                "urlPath": "/group1/M00/01/A9/wKgKZ2aLqXWACszpAAAyLVeITJE879.png"
            },
            {
                "aliasName": "角色管理",
                "path": "/backend/role",
                "code": "role",
                "menuId": "c6807cea-3ad9-4a27-9c50-7c608eee3019",
                "remark": "",
                "menuName": "角色管理",
                "id": "dddeb5f7-8d92-435d-b428-acefe649b866",
                "sort": 1,
                "type": "1",
                "workbenchMenuId": "5e29bf01-91a5-468c-8983-6b4ded69dae3",
                "urlPath": "/group1/M00/01/A9/wKgKZ2aLqU6AQT6wAAAwjcuEzV4006.png"
            },
            {
                "aliasName": "权限管理",
                "path": "/backend/permissions",
                "code": "permissions",
                "menuId": "65a1ca77-251c-43b4-b87e-63989fe32079",
                "remark": "",
                "menuName": "权限管理",
                "id": "547a8040-b60d-4256-8f86-99147e661faf",
                "sort": 2,
                "type": "1",
                "workbenchMenuId": "1a911a6b-b44d-4a07-9b91-aef8e9b37f15",
                "urlPath": "/group1/M00/01/A9/wKgKZ2aLqU6AJG05AAAnAmOVAXg600.png"
            },
            {
                "aliasName": "机构管理",
                "path": "/backend/depart",
                "code": "depart",
                "menuId": "d352830e-2fc6-4ac7-80b5-09d2af78a589",
                "remark": "",
                "menuName": "机构管理",
                "id": "d1cbf2e7-296e-47d0-8b1a-b9f4a0ec43ca",
                "sort": 3,
                "type": "1",
                "workbenchMenuId": "f9f19264-5df2-44b3-89b1-e82b0c102e35",
                "urlPath": "/group1/M00/01/A9/wKgKZ2aLqXWAPrtdAAApTjW_seA555.png"
            },
            {
                "aliasName": "部门流程定义",
                "path": "/backend/processManage/processDefine",
                "code": "processDefine",
                "menuId": "f53c5c24-6c70-47ee-81fe-7128b1efe713",
                "remark": "",
                "menuName": "部门流程定义",
                "id": "8b6f0042-9d2e-4650-8b69-6ad6f4cb491e",
                "sort": 4,
                "type": "1",
                "workbenchMenuId": "8001a326-2a05-402c-8046-3629a33a22c1",
                "urlPath": "/group1/M00/01/A9/wKgKZ2aLqU6AQR_KAAAjfl0SQ50047.png"
            },
            {
                "aliasName": "应用管理",
                "path": "/backend/application",
                "code": "application",
                "menuId": "f26d2358-8e28-4f0c-8e65-9554b9e73a27",
                "remark": "",
                "menuName": "应用管理",
                "id": "7e9f9f99-e2f7-4c91-a348-6afa986e7319",
                "sort": 5,
                "type": "1",
                "workbenchMenuId": "96258a7d-c998-4323-8ad6-051509358ac3",
                "urlPath": "/group1/M00/01/A9/wKgKZ2aLqXWAC9EdAAAiyLs-LLE635.png"
            },
            {
                "aliasName": "PC端菜单",
                "path": "/backend/menus/menus",
                "code": "menus",
                "menuId": "e36ea5d7-9929-4ea0-b403-84d212edfd30",
                "remark": "",
                "menuName": "PC端菜单",
                "id": "719ad51d-eb40-483a-b703-a65ed85a77d1",
                "sort": 6,
                "type": "1",
                "workbenchMenuId": "d0bea338-88ce-40e5-a516-cd0fb9d74679",
                "urlPath": "/group1/M00/01/A9/wKgKZ2aLqXWADOlXAAAkk2Hbp8g860.png"
            }
        ],
        "id": "e2e896e5-6f58-4791-844f-6086f0e4b2d0",
        "sort": 3,
        "status": "1"
    }
]
