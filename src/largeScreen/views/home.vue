<template>
  <div class="page-container">
    <div class="page-content">
      <!-- 左侧内容 -->
      <div class="left-area">
        <div class="about">
          <CardTitle>企业介绍</CardTitle>
          <div class="about-box">
           {{companyProText}}
          </div>
        </div>
        <div class="business">
          <CardTitle>业务范围</CardTitle>
          <BusinessBox></BusinessBox>
        </div>
        <div class="qualification">
          <CardTitle @click.native="showQualificationTable">企业资质</CardTitle>
          <QualificationBox></QualificationBox>
        </div>

      </div>
      <!-- 中间内容 -->
      <div class="center-area">
        <div class="left-border">
          <img
            class="bg"
            :src="require('@/largeScreen/iconBg/center-left-bg.png')"
          />
          <img
            class="bar"
            :src="require('@/largeScreen/iconBg/center-left-bar.png')"
          />
        </div>
        <div class="right-border">
          <img
            class="bg"
            :src="require('@/largeScreen/iconBg/center-right-bg.png')"
          />
          <img
            class="bar"
            :src="require('@/largeScreen/iconBg/center-right-bar.png')"
          />
        </div>
        <!-- 内容盒子 -->
        <div class="center-content">
          <div class="point">
            <ul>
              <li><span>精</span>专业</li>
              <li><span>善</span>管理</li>
              <li><span>强</span>服务</li>
              <li><span>能</span>创新</li>
            </ul>
          </div>
          <div style="width: 100%; height: 50%;position: relative;">
            <Map />
          </div>
          <div class="honor" style="margin-top: 30px">
            <CardTitlelang>荣誉证书</CardTitlelang>
            <div class="honor-box">
              <div class="container" @click="handleClick($event)">
                <vue-seamless-scroll
                  class="warp"
                  :class-option="classOption"
                  :data="urlPathListData"
                >
                  <ul class="imglist">
                    <!-- 在这里插入图片 -->
                    <li v-for="(item, index) in urlPathListData" :key="index">
                      <img alt="" class="image" :src="item.urlPath" />
                      <div class="name-mask">{{ item.fileName }}</div>
                    </li>
                  </ul>
                </vue-seamless-scroll>
                <!--    图片预览    -->
                <el-image-viewer
                  v-if="previewVisible"
                  :initial-index="currentIndex"
                  :on-close="() => (previewVisible = false)"
                  :url-list="urlPathList"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 右侧内容 -->
      <div class="right-area">
        <div class="people">
          <CardTitle @click.native="showPeopleTable">人员情况</CardTitle>
          <PeopleBox @showCertified="showCertifiedTable"/>
        </div>
        <div class="project-classification">
          <CardTitle @click.native="showProjectInfoTable">项目分类</CardTitle>
          <ClassificationBox/>
        </div>

      </div>
    </div>
    <TableDialog ref="tableListRef" />
  </div>
</template>

<script>
  import { parseTime } from '@/utils'
  import CardTitle from '../components/card-head.vue'
  import CardTitlelang from '../components/card-headlang.vue'
  import BusinessBox from './companyOverview/business-box.vue'
  import QualificationBox from './companyOverview/qualification-box.vue'
  import Map from '../components/map.vue'
  import { getUploadFileList } from '@/api/system/uploadFile-api'
  import VueSeamlessScroll from 'vue-seamless-scroll'
  import elImageViewer from 'element-ui/packages/image/src/image-viewer'
  import { getSysConfig } from '@/api/system/sysConfig-api'
  import PeopleBox from "@/largeScreen/views/companyOverview/people-box.vue";
  import ClassificationBox from "@/largeScreen/views/companyOverview/classification-box.vue";
  import TableDialog from '@/largeScreen/components/TableDialog.vue'
  import { getDataListByPage as getQualificationTable} from '@/api/certificateManagement/enterpriseCertificate'
  import { getDataListByPage as getProjectInfoTable } from '@/api/project'
  import { getDataListByPage as getPeopleTable} from '@/api/staffManagement/rosterApi'
  import { getDataListByPage as getCertifiedTable } from '@/api/certificateManagement/personnelCertificate'
  export default {
    components: {
      ClassificationBox,
      PeopleBox,CardTitle,BusinessBox,QualificationBox, VueSeamlessScroll,elImageViewer,CardTitlelang,
      Map,
      TableDialog,
    },
    data() {
      return {
        classOption: {
          limitMoveNum: 4,
          direction: 2,
          step: 0.3,
        },
        urlPathList: [],
        urlPathListData: [],
        previewVisible: false,
        currentIndex: 0,
        bizType: 'companyProfile',
        companyProText: '',
      }
    },
    created() {
      this.fetchData()
      this.getUploadFileList()
    },
    methods: {
      // 获取荣誉证书图片
      async getUploadFileList() {
        const params = {
          bizId: '765640a7-45b1-11eb-9e74-0894ef72d9c4',
          bizCode: 'app-banner'
        }
        const data = await getUploadFileList(params)
        this.urlPathListData = data.result.map(item => {
          this.urlPathList.push(item.urlPath)
          return {
            fileName: item.fileName,
            urlPath: item.urlPath,
          }
        })
      },
      handleClick(e) {
        if (e.target.tagName === 'IMG' && e.target.src) {
          this.currentIndex = this.urlPathList.indexOf(e.target.src)
          this.previewVisible = true
        }
      },
      // 获取公司简介
      async fetchData() {
       // console.log('getSysConfig 类型:', typeof getSysConfig); // 应输出 'function'
        const res = await getSysConfig({
          bizType: this.bizType,
        })
        this.companyProText = res.result.value1
      },
      dateFormat(date) {
        if (!date) {
          return '~'
        }
        return parseTime(new Date(date), '{y}-{m}-{d}')
      },
      showQualificationTable() {
        const _this = this
        const columns = [
          { label: '证书名称', prop: 'documentTypeName'},
          { label: '证书编号', prop: 'documentCode'},
          { label: '发证机关', prop: 'issuingAuthorityName'},
          { label: '颁发日期', prop: 'recordDate', formatter: function(row) {
            return parseTime(row['recordDate'], '{y}-{m}-{d}')
          }},
          { label: '有效开始时间', prop: 'effectiveStartTime', formatter: function(row) {
            return parseTime(row['effectiveStartTime'], '{y}-{m}-{d}')
          } },
          { label: '有效截止时间', prop: 'effectiveEndTime', formatter: function(row) {
            return parseTime(row['effectiveEndTime'], '{y}-{m}-{d}')
          } },
          { label: '证书状态', prop: 'documentStatus'},
        ]
        this.$refs.tableListRef.showTable(columns, getQualificationTable)
      },
      showCertifiedTable() {
        const _this = this
        const columns = [
          { label: '证书名称', prop: 'documentByName'},
          { label: '姓名', prop: 'personnelByName'},
          { label: '证书编号', prop: 'documentCode'},
          { label: '注册专业', prop: 'registerMajor'},
          { label: '发证机关', prop: 'issuingAuthorityName'},
          { label: '颁发日期', prop: 'recordDate', formatter: function(row) {
            return parseTime(row['recordDate'], '{y}-{m}-{d}')
          } },
          { label: '有效开始时间', prop: 'effectiveStartTime', formatter: function(row) {
            return parseTime(row['effectiveStartTime'], '{y}-{m}-{d}')
          } },
          { label: '有效截止时间', prop: 'effectiveEndTime', formatter: function(row) {
            return row['effectiveEndTime'] === '永久' ? '永久' : _this.dateFormat(row['effectiveEndTime'])
          } },
          { label: '证书状态', prop: 'documentStatus'},
        ]
        this.$refs.tableListRef.showTable(columns, getCertifiedTable)
      },
      showProjectInfoTable() {
        const _this = this
        const columns = [
          { label: '单号', prop: 'serialNumber',},
          { label: '项目名称', prop: 'projectName' },
          { label: '所属部门', prop: 'departName' },
          { label: '业务类型', prop: 'bizTypeName' },
          { label: '项目所在地', prop: 'siteStart' },
          { label: '计划工期', prop: 'planTime', formatter: function(row) {
            const text = _this.dateFormat(row.estimatedStartTime) + '至' + _this.dateFormat(row.estimatedEndTime)
            return text
          } },
          { label: '登记人', prop: 'createByName' },
          { label: '登记部门', prop: 'createDepartName' },
          { label: '登记日期', prop: 'recordDate' },
          { label: '状态', prop: 'state', formatter: function(row) {
            const text = row.state === '0' ? '未生效' : row.state === '1' ? '生效' : ''
            return text
          } }
        ]
        this.$refs.tableListRef.showTable(columns, getProjectInfoTable)
      },
      showPeopleTable() {
        const _this = this
        const columns = [
          { label: '员工状态', prop: 'employeeStatusName' },
          { label: '姓名', prop: 'userName' },
          { label: '身份证号', prop: 'idCard'},
          { label: '年龄', prop: 'age', formatter: function(row) {
            return _this.getAge(row.idCard)
          } },
          { label: '性别', prop: 'genderName', formatter: function(row) {
            return _this.getGenderFromIdCard(row.idCard)
          } },
          { label: '部门', prop: 'departName' },
          { label: '岗位或职级', prop: 'rolesAndPositionsName' },
          { label: '学历', prop: 'eduBackgroundName' },
          { label: '入职时间', prop: 'onBoardTime', formatter: function(row) {
            return parseTime(row['onBoardTime'], '{y}-{m}-{d}')
          } },
          { label: '司龄', prop: 'companyAge', formatter: function(row) {
            return _this.getSurname(row.onBoardTime)
          } },
          { label: '手机号', prop: 'telephone' },
        ]
        this.$refs.tableListRef.showTable(columns, getPeopleTable)
      },
      // 计算年龄
      getAge(idCard) {
        if (idCard === '') return
        // 计算年龄
        // 提取出生日期
        const birthYear = parseInt(idCard.substring(6, 10), 10)
        const birthMonth = parseInt(idCard.substring(10, 12), 10) - 1 // 月份从0开始
        const birthDay = parseInt(idCard.substring(12, 14), 10)
        const birthDate = new Date(birthYear, birthMonth, birthDay)
        // 获取当前日期
        const today = new Date()
        return today.getFullYear() - birthDate.getFullYear()
      },
      getGenderFromIdCard(idCard) {
        if (!idCard || idCard.length !== 18) return '未知';
        const genderCode = idCard.charAt(16); // 第17位，索引从0开始
        return parseInt(genderCode) % 2 === 0 ? '女' : '男';
      },
      getSurname(onBoardTime) {
        if (onBoardTime) {
          const onBoardDate = new Date(onBoardTime)
          const currentDate = new Date()
          let years = currentDate.getFullYear() - onBoardDate.getFullYear()
          let months = currentDate.getMonth() - onBoardDate.getMonth()
          let days = currentDate.getDate() - onBoardDate.getDate()

          if (days < 0) {
            months -= 1
            days += new Date(
              currentDate.getFullYear(),
              currentDate.getMonth(),
              0
            ).getDate()
          }
          if (months < 0) {
            years -= 1
            months += 12
          }

          let result = ''
          if (years > 0) {
            result += `${years}年`
          }
          if (months > 0) {
            result += `${months}个月`
          }
          if (days > 0) {
            result += `${days}天`
          }

          // 如果所有值都为0，则返回0天
          if (result === '') {
            result = '0天'
          }

          return result
        } else {
          return ''
        }
      },
    },
  }
</script>
<style>
.el-image-viewer__canvas{
  background: rgba(0,0,0,.7);
}
</style>

<style lang="scss" scoped>
  $width: 360px;
  .page-container {
    width: 100%;
    height: 100%;
    padding: 30px 50px 180px;
    box-sizing: border-box;
    .page-content {
      position: relative;
      width: 100%;
      height: 100%;
      .left-area {
        width: $width;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        .about{
          margin-bottom: 20px;
          .about-box{
            color: #fff;
            padding: 0 0 0 4%;
            line-height: 1.5;
            text-indent: 2em;
          }
        }
      }
      .right-area {
        width: $width;
        height: 100%;
        position: absolute;
        right: 0;
        top: 0;
      }
      .center-area {
        width: calc(100% - $width * 2 - 20px);
        height: 100%;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        box-sizing: border-box;
        .center-content {
          position: relative;
          width: 100%;
          height: 100%;
          box-sizing: border-box;
          padding: 0 100px;
          overflow: hidden;
          .point{
            padding: 20px 0 10px 0;
            ul{
              display: flex;
              justify-content: space-around;
              li{
                background: url('../iconBg/dz_1.png') no-repeat center bottom;
                position: relative;
                text-align: center;
                color: #fff;
                list-style: none;
                width: 25%;
                height: 100px;
                &::before {
                  content: "";
                  width: 110px;
                  height: 100px;
                  position: absolute;
                  left: 50%;
                  transform: translateX(-50%);
                  bottom: 0px;
                  background-image: url('../iconBg/dz_3.png');
                  background-size: 100% 100%;
                }
                &::after {
                  content: "";
                  width: 110px;
                  height: 100px;
                  position: absolute;
                  left: 50%;
                  transform: translateX(-50%);
                  bottom: 0;
                  background-image: url('../iconBg/dz_4.png');
                  background-size: 100% 100%;
                }
                span{
                  display: block;
                  font-size: 64px;
                  line-height: 54px;
                  background: linear-gradient(to bottom, #28d9ff 20%, #fff 60%);
                  -webkit-background-clip: text;
                  background-clip: text;
                  color: transparent;
                  font-family: 'PangMenZhengDao';
                  font-weight: 400;

                }
              }
            }
          }
          .honor-box{
            width: 100%;
            height: 240px;
            overflow: hidden;
            .container {
              display: flex;
              align-items: center;
              width: 100%;
              padding: 5px; /* 可根据需要调整 */
              .warp {
                height: 200px;
                width: 100%;
                overflow: hidden;
                .imglist {
                  width: 100%;
                  list-style: none;
                  padding: 0;
                  margin: 0;
                  display: flex;
                  li {
                    position: relative;
                    width: 200px;
                    height: 140px;
                    margin-right: 10px;
                    border: 2px solid #162a39;
                    border-radius: 10px;
                    overflow: hidden;
                    img {
                      width: 100%;
                      height: 100%;
                      cursor: pointer;
                    }
                    .name-mask {
                      position: absolute;
                      bottom: 0;
                      left: 0;
                      right: 0;
                      width: 100%;
                      height: 32px;
                      font-size: 14px;
                      line-height: 32px;
                      text-align: center;
                      box-sizing: border-box;
                      padding: 0 15px;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                      overflow: hidden;
                      background-color: rgba(0, 0, 0, .6);
                      color: #efefef;
                    }
                  }
                }
              }
              /* flex: 1; 图片部分占据剩余宽度，即30% */
              overflow: hidden;

              .text {
                flex: 3; /* 文字部分占据70%的宽度 */
                padding-left: 20px; /* 文字与图片之间的间距 */
                font-size: 18px;
                color: #000;
                line-height: 30px;
              }
            }
          }
        }
        .left-border {
          position: absolute;
          height: 100%;
          left: 0;
          animation: leftMove 8s ease-in-out infinite alternate;
          .bg,
          .bar {
            height: 100%;
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
          }
        }
        .right-border {
          position: absolute;
          height: 100%;
          right: 0;
          animation: rightMove 8s ease-in-out infinite alternate;
          .bg,
          .bar {
            height: 100%;
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
          }
        }
      }
    }
  }
  @keyframes leftMove {
    0% {
      transform: translateX(0) scale(1);
    }
    30% {
      transform: translateX(-5px) scale(1.02);
    }
    80% {
      transform: translateX(5px) scale(0.99);
    }
    100% {
      transform: translateX(0px) scale(1);
    }
  }
  @keyframes rightMove {
    0% {
      transform: translateX(0) scale(1);
    }
    30% {
      transform: translateX(5px) scale(1.02);
    }
    80% {
      transform: translateX(-5px) scale(0.99);
    }
    100% {
      transform: translateX(0px) scale(1);
    }
  }
</style>
