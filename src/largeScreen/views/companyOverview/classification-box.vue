<template>
  <div class="chart-container">
    <div class="chart-wrapper">
      <vab-chart
        ref="chartRef"
        :option="chartOption"
        :auto-resize="true"
        style="width: 100%; height: 530px;"
      ></vab-chart>
    </div>
  </div>
</template>

<script>
import { getSysConfig } from '@/api/system/sysConfig-api'
import VabChart from '@/extra/VabChart/index.vue'

export default {
  components: {
    VabChart
  },
  data() {
    return {
      chartData: [],
      bizType: 'projectClassification',
      chartOption: {},
      chartInstance: null,
      labelMap: [
        { key: 'value1', label: '公路工程监理' },
        { key: 'value2', label: '高速公路养护监理' },
        { key: 'value3', label: '交通机电监理' },
        { key: 'value4', label: '新能源监理' },
        { key: 'value5', label: '水运监理' },
        { key: 'value6', label: '房建监理' },
        { key: 'value7', label: '实验监测' },
        { key: 'value8', label: '造价咨询' }
      ],
      colorList: ['#88D9FF', '#0092FF', '#81EDD2', '#B0FA93', '#63F2FF', '#9999FE', '#FFD700', '#FF6347']
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.chartInstance = this.$refs.chartRef?.chart
      this.fetchData()
    })
  },
  watch: {
    chartData: {
      deep: true,
      handler() {
        this.updateChartData()
      }
    }
  },
  methods: {
    async fetchData() {
      try {
        const res = await getSysConfig({
          bizType: this.bizType
        })
        this.processApiData(res.result)
      } catch (error) {
        console.error('获取数据失败:', error)
      }
    },
    processApiData(apiData) {
      if (!apiData) return

      // 原始数据处理（可能产生重复name）
      this.chartData = this.labelMap
        .map(item => ({
          name: item.label,
          value: parseInt(apiData[item.key]) || 0
        }))
        .filter(item => item.value > 0)
    },
    updateChartData() {
      if (!this.chartData || this.chartData.length === 0) return;

      const chartData = [...this.chartData];
      // 关键：对 chartData 按 name 去重（避免图例重复）
      const uniqueChartData = [...new Map(chartData.map(item => [item.name, item])).values()];
      const sum = chartData.reduce((per, cur) => per + cur.value, 0);
      const gap = (1 * sum) / 100;
      const pieData1 = [];
      const pieData2 = [];
      const gapData = {
        name: '',
        value: gap,
        itemStyle: {
          color: 'transparent'
        }
      };

      const itemCount = uniqueChartData.length;
      const total = uniqueChartData.reduce((per, cur) => per + cur.value, 0); // 使用去重后的数据计算总和


      // 生成唯一的图例数据
      const legendData = uniqueChartData.map((item, i) => {
        // 第一圈和第二圈数据添加（保持原有逻辑）
        pieData1.push({ ...item, itemStyle: { borderRadius: 10 } });
        pieData1.push(gapData);
        pieData2.push({ ...item, itemStyle: { color: this.colorList[i % this.colorList.length], opacity: 0.21 } });
        pieData2.push(gapData);
      });
      this.chartOption = {
        backgroundColor: 'transparent',
        title: {
          text: '项目总数',
          subtext: total.toString(),
          x: '48.5%',
          y: '17%',
          itemGap: 15,
          textStyle: {
            color: '#9bb9dd',
            fontSize: 12,
          },
          subtextStyle: {
            color: '#dfecfb',
            fontSize: 26,
            fontWeight: 'bold'
          },
          textAlign: 'center'
        },
        tooltip: {
          show: true,
          backgroundColor: 'rgba(0, 0, 0,.8)',
          textStyle: {
            color: '#fff'
          },
          formatter: (params) => {
            if (params.name) {
              // 查找时使用去重后的数据
              const item = uniqueChartData.find(item => item.name === params.name)
              if (item) {
                return `${item.name}: ${item.value}`
              }
            }
            return params.name || ''
          }
        },

        // legend配置部分：
        legend: {
          show: true,
          data: uniqueChartData.map(item => item.name),
          orient: 'horizontal',
          top: 230,
          left: '10%',
          icon: 'roundRect',
          itemGap: 20,
          itemWidth: 10,
          itemHeight: 10, // 图标高度固定为10px
          //verticalAlign: 'middle', // 图例整体以顶部为对齐基准
          textStyle: {
            rich: {
              name: {
                color: '#9bb9dd',
                fontSize: 14,
                lineHeight: 10,
              },
              value: {
                color: '#dfecfb',
                fontSize: 16,
                fontWeight: 'bold',
                lineHeight: 10,
                padding: [0, 0, 0, 7], // 文字的水平间距（左间距）
              }
            },
            padding: [0, 0, 0, 5] // 图标与文字的水平间距（左间距）
          },
          formatter: (name) => {
            const item = uniqueChartData.find(d => d.name === name);
            return `{name|${name}}{value|${item?.value || 0}}`;
          }
        },
        grid: {
          top: 0,
        },
        color: this.colorList,
        series: [
          {
            name: '',
            type: 'pie',
            roundCap: true,
            radius: ['45%', '50%'],
            center: ['50%', '22%'],
            label: {
              show: false
            },
            labelLine: {
              show: false
            },
            data: pieData1
          },
          {
            type: 'pie',
            radius: ['45%', '40%'],
            center: ['50%', '22%'],
            gap: 1.71,
            label: {
              show: false
            },
            labelLine: {
              show: false
            },
            silent: true,
            data: pieData2
          },
          {
            type: 'gauge',
            zlevel: 2,
            splitNumber: 90,
            radius: '40%',
            center: ['50%', '22%'],
            startAngle: 90,
            endAngle: -269.9999,
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              show: false
            },
            splitLine: {
              show: true,
              length: 7,
              lineStyle: {
                width: 4,
                color: 'rgb(33,85,130)'
              }
            },
            pointer: {
              show: 0
            },
            detail: {
              show: 0
            }
          },
          {
            type: 'pie',
            center: ['50%', '22%'],
            radius: [0, '30%'],
            label: {
              show: false
            },
            labelLine: {
              show: false
            },
            itemStyle: {
              color: 'rgba(75, 126, 203,.1)'
            },
            silent: true,
            data: [
              {
                value: 100,
                name: ''
              }
            ]
          }
        ]
      }
    }
  }
}
</script>

<style scoped>
.chart-container {
  width: 96%;
  margin-left: 4%;
  height: 450px;
  overflow: hidden;
}

.chart-wrapper {
  width: 100%;
  height: 100%;

}
</style>
