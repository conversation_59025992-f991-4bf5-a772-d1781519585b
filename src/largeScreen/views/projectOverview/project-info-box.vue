<template>
  <div class="project-info-container">
    <!-- 项目信息面板 -->
    <div class="project-info-panel" v-if="selectedProject">
      <div class="info-background">
        <img :src="require('@/largeScreen/iconBg/projectProfile/infor_bg.png')" class="panel-bg" />

        <!-- 项目基本信息 -->
        <div class="info-grid">
          <!-- 开工日期 -->
          <div class="info-item">
            <div class="info-icon">
              <img :src="require('@/largeScreen/iconBg/projectProfile/infor_iconbg.png')" class="icon-bg" />
              <img :src="require('@/largeScreen/iconBg/projectProfile/infor_iocn01.png')" class="icon" />
            </div>
            <div class="info-content">
              <div class="info-label">开工日期</div>
              <div class="info-value" style="font-size: 14px">
                {{ (selectedProject.actualStartTime || selectedProject.estimatedStartTime) | dateformat('YYYY-MM-DD')  || '待定' }}
              </div>
            </div>
          </div>

          <!-- 工期(月) -->
          <div class="info-item">
            <div class="info-icon">
              <img :src="require('@/largeScreen/iconBg/projectProfile/infor_iconbg.png')" class="icon-bg" />
              <img :src="require('@/largeScreen/iconBg/projectProfile/infor_iocn02.png')" class="icon" />
            </div>
            <div class="info-content">
              <div class="info-label">工期(月)</div>
              <div class="info-value">{{ constructionPeriod }}</div>
            </div>
          </div>

          <!-- 剩余天数 -->
          <div class="info-item">
            <div class="info-icon">
              <img :src="require('@/largeScreen/iconBg/projectProfile/infor_iconbg.png')" class="icon-bg" />
              <img :src="require('@/largeScreen/iconBg/projectProfile/infor_iocn03.png')" class="icon" />
            </div>
            <div class="info-content">
              <div class="info-label">剩余天数</div>
              <div class="info-value">{{ remainingDays }}</div>
            </div>
          </div>

          <!-- 项目阶段 -->
          <div class="info-item">
            <div class="info-icon">
              <img :src="require('@/largeScreen/iconBg/projectProfile/infor_iconbg.png')" class="icon-bg" />
              <img :src="require('@/largeScreen/iconBg/projectProfile/infor_iocn04.png')" class="icon" />
            </div>
            <div class="info-content">
              <div class="info-label">项目阶段</div>
              <div class="info-value">{{ selectedProject.projectStatusName || '待定' }}</div>
            </div>
          </div>
        </div>

        <!-- 项目单位信息 -->
        <div class="company-info">
          <!-- 建设单位 -->
          <div class="company-item">
            <div class="company-icon">
              <img :src="require('@/largeScreen/iconBg/projectProfile/company_icon.png')" class="icon" />
            </div>
            <div class="company-content">
              <div class="company-label">建设单位</div>
              <div class="company-name">{{ constructionUnitName || '某某建设单位名称' }}</div>
            </div>
          </div>

          <!-- 监理单位 -->
          <div class="company-item">
            <div class="company-icon">
              <img :src="require('@/largeScreen/iconBg/projectProfile/company_icon.png')" class="icon" />
            </div>
            <div class="company-content">
              <div class="company-label">监理单位</div>
              <div class="company-name">河南交投工程管理咨询有限公司</div>
            </div>
          </div>

          <!-- 承建单位 -->
          <div class="company-item">
            <div class="company-icon">
              <img :src="require('@/largeScreen/iconBg/projectProfile/company_icon.png')" class="icon" />
            </div>
            <div class="company-content">
              <div class="company-label">承建单位</div>
              <div class="company-name">{{ constructionName || '某某施工单位名称' }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getDataList as getConstructionNameData,
} from '@/api/project/projectSectionConfiguration'
import {
  getData as getConstructionUnitName,
} from '@/api/bidManagement/bidManagement'
export default {
  name: 'ProjectInfoBox',
  props: {
    // 从父组件接收选中的项目
    selectedProject: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      // 移除本地的 selectedProject，使用 props
      timer: null,
      constructionName: '',
      constructionNameData: '', //承建单位
      constructionUnitName: '', //建设单位
    }
  },
  computed: {
    remainingDays() {
      if (!this.selectedProject || !(this.selectedProject.actualEndTime || this.selectedProject.estimatedEndTime)) {
        return '待定'
      }

      // try {
      //   const startDate = new Date(this.selectedProject.startTime)
      //   const durationMonths = parseInt(this.selectedProject.duration) || 12
      //   const endDate = new Date(startDate)
      //   endDate.setMonth(endDate.getMonth() + durationMonths)

      //   const today = new Date()
      //   const diffTime = endDate - today
      //   const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

      //   return diffDays > 0 ? diffDays : 0
      // } catch (error) {
      //   return '待定'
      // }
      const today = new Date().getTime()
      const endTime = this.selectedProject.actualEndTime || this.selectedProject.estimatedEndTime
      const endDate = new Date(endTime).getTime()
      if(today >= endDate) return 0
      const diffTime = endDate - today
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      return diffDays
    },
    // 计算工期
    constructionPeriod() {
      const flag = !this.selectedProject ||
                   !(this.selectedProject.actualStartTime || this.selectedProject.estimatedStartTime) ||
                   !(this.selectedProject.actualEndTime || this.selectedProject.estimatedEndTime)
      if (flag) {
        return '待定'
      }
      const startDate = this.selectedProject.actualStartTime || this.selectedProject.estimatedStartTime
      const endDate = this.selectedProject.actualEndTime || this.selectedProject.estimatedEndTime
      const start = new Date(startDate)
      const end = new Date(endDate)
      const durationInDays = (end - start) / (1000 * 60 * 60 * 24)
      return parseFloat((durationInDays / 30).toFixed(2))
    },
  },
  watch: {
    // 监听项目变更
    selectedProject: {
      async handler(newProject, oldProject) {
        if (newProject !== oldProject) {
          // console.log('项目信息组件 - 项目已变更:', newProject)
          // 可以在这里添加项目变更后的业务逻辑
          clearInterval(this.timer)
          this.constructionName = ''
          this.constructionNameData = ''
          this.constructionUnitName = ''
          await this.getUnitInfoById(newProject)
        }
      },
      immediate: true
    }
  },
  beforeDestroy() {
    clearInterval(this.timer)
  },
  methods: {
    // 获取当前选中的项目（供外部调用）
    getCurrentProject() {
      return this.selectedProject
    },
    async getUnitInfoById(project) {
      if(!project) return
      const projectId = project.id
      const bidProjectId = project.bidProjectId
      const {result: dataList=[]} = await getConstructionNameData({projectId})
      const {result: {constructionUnitName = ''}} = await getConstructionUnitName({id: bidProjectId})
      this.constructionNameData = dataList
      this.constructionUnitName = constructionUnitName
      this.getConstructionName()
      if(dataList.length > 1) {
        let i = 0;
        this.timer = setInterval(() => {
          if(i >= dataList.length) {
            i = 0
          }
          this.getConstructionName(i)
          i++
        }, 3000)
      }
    },
    getConstructionName(i = 0) {
      this.constructionName = this.constructionNameData[i]?.constructionName || ''
    }
  }
}
</script>

<style scoped lang="scss">
.project-info-container {
  width: 100%;
  height: 100%;
  position: relative;
}

// 项目信息面板
.project-info-panel {
  position: relative;
  width: 100%;
  height: 100%;

  .info-background {
    position: relative;
    width: 100%;
    height: 100%;

    .panel-bg {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}

// 项目基本信息网格
.info-grid {
  position: absolute;
  top: 30px;
  left: 20px;
  right: 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px 15px;

  .info-item {
    display: flex;
    align-items: center;
    gap: 10px;

    .info-icon {
      position: relative;
      width: 50px;
      height: 50px;
      flex-shrink: 0;

      .icon-bg {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
      }

      .icon {
        width: 24px;
        height: 24px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }

    .info-content {
      flex: 1;

      .info-label {
        color: #87ceeb;
        font-size: 12px;
        margin-bottom: 5px;
        text-shadow: 0 0 3px rgba(135, 206, 235, 0.5);
      }

      .info-value {
        color: #ffffff;
        font-size: 16px;
        font-weight: bold;
        text-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
      }
    }
  }
}

// 公司信息
.company-info {
  position: absolute;
  bottom: 30px;
  left: 20px;
  right: 20px;

  .company-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;

    &:last-child {
      margin-bottom: 0;
    }

    .company-icon {
      width: 40px;
      height: 40px;
      flex-shrink: 0;

      .icon {
        width: 100%;
        height: 100%;
      }
    }

    .company-content {
      flex: 1;

      .company-label {
        color: #87ceeb;
        font-size: 12px;
        margin-bottom: 3px;
        text-shadow: 0 0 3px rgba(135, 206, 235, 0.5);
      }

      .company-name {
        color: #00d4ff;
        font-size: 14px;
        font-weight: bold;
        text-shadow: 0 0 8px rgba(0, 212, 255, 0.8);
      }
    }
  }
}
</style>
