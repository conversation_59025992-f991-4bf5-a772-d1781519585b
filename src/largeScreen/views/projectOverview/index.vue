<template>
  <div class="page-container">
    <!-- 项目选择器 -->
    <div class="page-content">
      <!-- 左侧内容 -->
      <div class="left-area">
        <!-- <ProjectSelector
          @project-change="onProjectChange"
          :auto-select-first="true"
          ref="projectSelector"
          default-project-id="ade1a8cd-8818-4b96-8003-98b8b5fc5dde"
        /> -->
        <ProjectSelected
          @project-change="onProjectChange"
          :auto-select-first="true"
          ref="projectSelected"
          default-project-id="ade1a8cd-8818-4b96-8003-98b8b5fc5dde"
        />
        <!-- 项目信息板块 -->
        <div class="project-info">
          <ProjectInfoBox :selected-project="selectedProject"></ProjectInfoBox>
        </div>

        <div class="qualification">
          <CardTitle>现场实况</CardTitle>
          <LiveScene :selected-project="selectedProject" />
        </div>

      </div>
      <!-- 中间内容 -->
      <div class="center-area">
        <div class="left-border">
          <img
            class="bg"
            :src="require('@/largeScreen/iconBg/center-left-bg.png')"
          />
          <img
            class="bar"
            :src="require('@/largeScreen/iconBg/center-left-bar.png')"
          />
        </div>
        <div class="right-border">
          <img
            class="bg"
            :src="require('@/largeScreen/iconBg/center-right-bg.png')"
          />
          <img
            class="bar"
            :src="require('@/largeScreen/iconBg/center-right-bar.png')"
          />
        </div>
        <!-- 内容盒子 -->
        <div class="center-content">
          <div class="project_info_num">
            <div class="project-item" v-for="(item, index) in projectInfoNum" :key="index">
              <div class="project-type">{{item.type}}</div>
              <div class="num" v-if="item.num > 0">
                <span v-for="(n, i) in getNum(item.num)" :key="i">{{n}}</span>
              </div>
              <div class="num" v-else>
                <span>0</span>
              </div>
            </div>
          </div>
          <!-- 人员定位标题 -->
          <CardTitlelang>人员定位</CardTitlelang>
          <div style="width: 100%; height: 70%;position: relative;">
<!--            <Map :selected-project="selectedProject" ref="mapComponent" />-->
            <Map/>
          </div>

        </div>
      </div>
      <!-- 右侧内容 -->
      <div class="right-area">
        <div class="people">
          <CardTitle>今日到岗</CardTitle>
          <TodayArriveWork ref="todayArriveWorkRef" />
        </div>
        <div class="project-classification">
          <CardTitle>考勤记录</CardTitle>
          <AttendanceRecord :selected-project="selectedProject" />
        </div>

      </div>
    </div>
  </div>
</template>

<script>
import CardTitle from '@/largeScreen/components/card-head.vue'
import CardTitlelang from '@/largeScreen/components/card-headlang.vue'
import Map from '@/largeScreen/components/map.vue'
import ProjectInfoBox from "./project-info-box.vue"
import LiveScene from "./live-scene.vue"
import TodayArriveWork from "./today-arrive-work.vue"
import AttendanceRecord from "./attendance-record.vue"
import ProjectSelector from '@/largeScreen/components/project-selector.vue'
import ProjectSelected from '@/largeScreen/components/projectSelected.vue'
import { getSysConfig } from '@/api/system/sysConfig-api'
export default {
  components: {
    CardTitle,
    CardTitlelang,
    Map,
    ProjectInfoBox,
    ProjectSelector,
    TodayArriveWork,
    AttendanceRecord,
    LiveScene,
    ProjectSelected,
  },
  data() {
    return {
      selectedProject: null, // 当前选中的项目
      projectInfoNum: [
        {type: '日志', num: 0},
        {type: '巡查', num: 0},
        {type: '监理指令', num: 0},
        {type: '旁站', num: 0},
      ],
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    // 处理项目选择变更
    onProjectChange(selectedProject) {
      this.selectedProject = selectedProject
      // console.log('项目概览页面 - 选中项目:', selectedProject)

      // 这里可以通知其他组件项目已变更
      this.notifyProjectChange(selectedProject)
    },

    // 通知其他组件项目变更
    notifyProjectChange(project) {
      // 可以通过事件总线或其他方式通知其他组件
      this.$nextTick(() => {
        // 通知地图组件更新
        if (this.$refs.mapComponent) {
          this.$refs.mapComponent.updateProject(project)
        }

        // 通知其他需要项目信息的组件
        // ...
      })
    },

    // 获取当前选中项目（供子组件调用）
    getCurrentProject() {
      return this.selectedProject
    },
    getNum(num) {
      return String(num).split('')
    },
    async fetchData() {
      const { result } = await getSysConfig({
        bizType: 'jobLog'
      })
      this.projectInfoNum = this.projectInfoNum.map((item, index) => {
        const fieldsName = 'value' + (index + 1)
        return {
          ...item,
          num: result[fieldsName]
        }
      })
    },
  },
}
</script>
<style lang="scss" scoped>
$width: 360px;
.page-container {
  width: 100%;
  height: 100%;
  padding: 30px 50px 180px;
  box-sizing: border-box;

  // 项目选择器容器
  .project-selector-container {
    position: absolute;
    top: 30px;
    left: 50%;
    transform: translateX(-50%);
    width: 300px;
    z-index: 1000;
  }
  .page-content {
    position: relative;
    width: 100%;
    height: 100%;
    .left-area {
      width: $width;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      .project-info {
        margin-bottom: 20px;
        margin-left: 4%;
      }

      .qualification {
        height: 50%; // 占左侧区域的50%高度
      }

      .about{
        margin-bottom: 20px;
        .about-box{
          color: #fff;
          padding: 0 0 0 4%;
          line-height: 1.5;
          text-indent: 2em;
        }
      }
    }
    .right-area {
      width: $width;
      height: 100%;
      position: absolute;
      right: 0;
      top: 0;
    }
    .center-area {
      width: calc(100% - $width * 2 - 20px);
      height: 100%;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      box-sizing: border-box;
      .center-content {
        position: relative;
        width: 100%;
        height: 100%;
        box-sizing: border-box;
        padding: 0 100px;
        overflow: hidden;

        .project_info_num {
          position: relative;
          box-sizing: border-box;
          pointer-events: none;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #21eeff;
          margin-top: 5%;
          margin-bottom: 5%;
          .project-item {
            width: 23%;
            display: flex;
            &:nth-child(2n) {
              .num {
                span {
                  color: #21eeff;
                }
              }
            }
            .project-type {
              width: 75px;
              height: 40px;
              line-height: 40px;
              text-align: center;
              box-sizing: border-box;
              font-size: 18px;
              font-weight: bold;
              color: #b1d4ff;
              font-family: "Microsoft YaHei";
              background-image: url('../../iconBg/map/lable_name_bg.png');
              background-size: 100% 100%;
            }
            .num {
              padding-left: 5px;
              white-space: nowrap;
              span {
                display: inline-block;
                width: 35px;
                height: 41px;
                color: #51ff12;
                font-size: 28px;
                font-weight: bolder;
                text-align: center;
                line-height: 41px;
                background-image: url('../../iconBg/map/num_bg.png');
                background-size: 100% 100%;
              }
            }
          }
        }
      }
      .left-border {
        position: absolute;
        height: 100%;
        left: 0;
        animation: leftMove 8s ease-in-out infinite alternate;
        .bg,
        .bar {
          height: 100%;
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
        }
      }
      .right-border {
        position: absolute;
        height: 100%;
        right: 0;
        animation: rightMove 8s ease-in-out infinite alternate;
        .bg,
        .bar {
          height: 100%;
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
        }
      }
    }
  }
}
@keyframes leftMove {
  0% {
    transform: translateX(0) scale(1);
  }
  30% {
    transform: translateX(-5px) scale(1.02);
  }
  80% {
    transform: translateX(5px) scale(0.99);
  }
  100% {
    transform: translateX(0px) scale(1);
  }
}
@keyframes rightMove {
  0% {
    transform: translateX(0) scale(1);
  }
  30% {
    transform: translateX(5px) scale(1.02);
  }
  80% {
    transform: translateX(-5px) scale(0.99);
  }
  100% {
    transform: translateX(0px) scale(1);
  }
}

</style>
