<template>
  <div v-if="dialogVisible">
    <!-- <div class="modal-header">
      <div class="live-status">
        <el-tag v-if="isLoading" effect="dark" size="medium" type="info">
          <i class="el-icon-loading" />
          加载中
        </el-tag>
        <el-tag
          v-else-if="streamError"
          effect="dark"
          size="medium"
          type="danger"
        >
          <i class="el-icon-warning" />
          连接断开
        </el-tag>
        <el-tag
          v-else-if="isReconnecting"
          effect="dark"
          size="medium"
          type="warning"
        >
          <i class="el-icon-loading" />
          重新连接中
        </el-tag>
        <el-tag
          v-else-if="isUpgrading && isPlaying && streamConnected"
          effect="dark"
          size="medium"
          type="primary"
        >
          <i class="el-icon-upload" />
          检测ai流中
        </el-tag>
        <el-tag
          v-else-if="isPlaying"
          effect="dark"
          size="medium"
          type="success"
        >
          <i class="el-icon-video-play" />
          直播中
        </el-tag>
        <el-tag v-else effect="dark" size="medium" type="info">
          <i class="el-icon-video-pause" />
          等待连接
        </el-tag>
      </div>
    </div> -->

    <div class="player-content largescreen-live-player">
      <!-- EasyPlayer播放器容器 -->
      <div v-show="showPlayer" class="player-wrapper">
        <div :id="playerId" class="easyplayer-container"></div>
      </div>
    </div>

    <!-- <div class="dialog-footer">
      <el-button icon="el-icon-close" type="primary" @click="handleClose">
        关闭
      </el-button>
    </div> -->
  </div>
</template>

<script>
  import { queryStream } from '@/api/skydrone/skydrone-api'

  export default {
    name: 'EasyPlayerLiveViewer',
    props: {
      visible: {
        type: Boolean,
        default: false,
      },
      device: {
        type: Object,
        default: () => ({}),
      },
      maxRetries: {
        type: Number,
        default: 10,
      },
      retryInterval: {
        type: Number,
        default: 3000,
      },
    },
    data() {
      return {
        playerInfo: null,
        isLoading: false,
        retryCount: 0,
        retryTimer: null,
        checkingUrl: false,
        streamConnected: false,
        checkingAiUrl: false,
        aiStreamConnected: false,
        currentStreamUrl: null,
        isDestroyed: false,
        showPlayer: true,
        streamError: false,
        isReconnecting: false,
        isPlaying: false,
        playerId: 'easyplayer-' + Date.now() + '-' + Math.random().toString(36),
        lastPlayedUrl: null, // 记录上次播放的流地址
        isUpgrading: false, // 是否正在升级到更好的流
        playerInitialized: false, // 播放器是否已初始化
        aiStreamFailedAt: null, // AI流失败的时间戳
        aiStreamCooldown: 3000, // AI流冷却时间（30秒）
      }
    },
    computed: {
      dialogVisible: {
        get() {
          return this.visible
        },
        set(val) {
          this.$emit('update:visible', val)
        },
      },
      deviceName() {
        if (!this.device) return '设备直播'
        return (
          this.device.nickname ||
          (this.device.sn ? `设备 ${this.device.sn} 直播流` : '设备直播')
        )
      },

      // 判断当前播放的流是否为AI流
      isCurrentStreamAI() {
        if (!this.currentStreamUrl) return false

        // 基于URL路径判断是否为AI流
        const isAiByUrl = this.currentStreamUrl.includes('/ai/') ||
          this.currentStreamUrl.includes('live/ai')

        // 结合状态标志判断
        const isAiByState = this.aiStreamConnected

        const finalResult = isAiByUrl || isAiByState

        // 只在结果变化时输出日志，避免过多日志
        if (this._lastStreamTypeResult !== finalResult) {
          console.log('🔍 流类型判断:', {
            currentStreamUrl: this.currentStreamUrl,
            isAiByUrl,
            isAiByState,
            finalResult
          })
          this._lastStreamTypeResult = finalResult
        }

        // 优先使用URL判断，因为它更准确
        return finalResult
      }
    },
    watch: {
      visible(newVal) {
        if (newVal) {
          console.log('打开EasyPlayer直播窗口，开始流检测')
          this.isDestroyed = false
          // 如果播放器不存在，重置状态；如果存在，保持播放器实例
          if (!this.playerInfo) {
            this.resetPlayerState()
          } else {
            console.log('播放器已存在，复用播放器实例')
            // 只重置流相关状态，保持播放器实例
            this.isLoading = false
            this.streamError = false
            this.isReconnecting = false
            this.isUpgrading = false
            this.streamConnected = false
            this.aiStreamConnected = false
            this.currentStreamUrl = null
            this.lastPlayedUrl = null
          }
          this.$nextTick(() => {
            this.startStreamCheck()
          })
        } else {
          console.log('关闭EasyPlayer直播窗口，停止检测但保持播放器')
          this.isDestroyed = true
          this.stopRetryTimer()
          this.stopPlayer() // 只停止播放，不销毁播放器
        }
      },
    },
    beforeDestroy() {
      console.log('EasyPlayer组件开始销毁')
      this.isDestroyed = true
      this.showPlayer = false
      this.stopRetryTimer()
      this.destroyPlayer()
      console.log('EasyPlayer组件销毁完成')
    },
    methods: {
      resetPlayerState() {
        this.isLoading = false
        this.retryCount = 0
        this.streamConnected = false
        this.aiStreamConnected = false
        this.currentStreamUrl = null
        this.streamError = false
        this.isReconnecting = false
        this.isPlaying = false
        this.showPlayer = true
        this.lastPlayedUrl = null
        this.isUpgrading = false
        this.aiStreamFailedAt = null
        // 注意：不重置 playerInitialized，保持播放器实例
      },
      stopRetryTimer() {
        if (this.retryTimer) {
          clearTimeout(this.retryTimer)
          this.retryTimer = null
        }
      },

      // 强制更新当前流URL（用于调试）
      // forceUpdateStreamUrl(newUrl) {
      //   console.log('🔄 强制更新流URL:', newUrl)
      //   this.currentStreamUrl = newUrl
      //   this.$forceUpdate()
      // },

      destroyPlayer() {
        console.log('销毁EasyPlayer播放器')
        this.showPlayer = false

        if (this.playerInfo) {
          try {
            if (typeof this.playerInfo.destroy === 'function') {
              this.playerInfo.destroy()
            }
          } catch (error) {
            console.warn('销毁EasyPlayer播放器时出错:', error)
          }
          this.playerInfo = null
        }

        // 清除记录的流地址和初始化标志
        this.lastPlayedUrl = null
        this.playerInitialized = false

        console.log('EasyPlayer播放器已完全移除')
      },

      // 停止播放但不销毁播放器
      stopPlayer() {
        if (this.playerInfo) {
          try {
            console.log('停止播放器播放')
            this.playerInfo.pause()
            this.isPlaying = false
          } catch (error) {
            console.warn('停止播放器时出错:', error)
          }
        }
      },

      createOrUpdatePlayer(sourceType = '') {
        if (this.isDestroyed) {
          console.warn('组件已销毁，无法操作播放器')
          return
        }

        // 检查流地址是否与当前播放的一致
        if (
          this.playerInfo &&
          this.lastPlayedUrl === this.currentStreamUrl &&
          this.isPlaying
        ) {
          console.log(
            `流地址未变化且正在播放，跳过操作: ${this.currentStreamUrl}`
          )
          return
        }

        // 如果播放器已存在，直接使用play方法切换流
        if (this.playerInfo) {
          console.log(
            `播放器已存在，使用play方法切换到新流 - ${sourceType}:`,
            this.currentStreamUrl
          )
          this.playStream()
          return
        }

        // 首次创建播放器
        console.log(
          `首次创建EasyPlayer播放器 - ${sourceType}:`,
          this.currentStreamUrl
        )

        try {
          if (typeof window.EasyPlayerPro === 'undefined') {
            console.error('EasyPlayerPro未加载，请检查js文件引入')
            this.handleStreamError()
            return
          }

          // 确保容器显示
          this.showPlayer = true

          this.$nextTick(() => {
            if (this.isDestroyed) return

            // 等待DOM更新后再查找容器
            setTimeout(() => {
              if (this.isDestroyed) return

              const container = document.getElementById(this.playerId)
              if (!container) {
                console.error('播放器容器未找到，playerId:', this.playerId)
                console.error(
                  'DOM中的容器:',
                  document.querySelector('.easyplayer-container')
                )
                this.handleStreamError()
                return
              }

              this.initializePlayer(container)
            }, 100)
          })
        } catch (error) {
          console.error('创建EasyPlayer播放器失败:', error)
          this.handleStreamError()
        }
      },

      initializePlayer(container) {
        try {
          const config = {
            isLive: true,
            hasAudio: true,
            isMute: false,
            stretch: true,
            bufferTime: 1,
            loadTimeOut: 10,
            loadTimeReplay: -1,
            MSE: true,
            WCS: false,
            WASM: false,
            debug: false,
            watermark: {
              text: {
                content: '',
              },
              right: 10,
              top: 10,
            },
          }

          console.log('正在创建EasyPlayer播放器实例...', container)
          this.playerInfo = new window.EasyPlayerPro(container, config)
          this.playerInitialized = true

          this.bindPlayerEvents()

          console.log('播放器创建成功，准备播放流:', this.currentStreamUrl)
          // 创建完成后立即播放当前流
          if (this.currentStreamUrl) {
            this.playStream()
          }
        } catch (error) {
          console.error('初始化EasyPlayer播放器失败:', error)
          this.handleStreamError()
        }
      },

      bindPlayerEvents() {
        if (!this.playerInfo) return

        this.playerInfo.on('play', () => {
          console.log('✅ EasyPlayer播放开始')
          this.isPlaying = true
          this.isLoading = false
          this.streamError = false
          this.isReconnecting = false
          this.retryCount = 0 // 播放成功时重置重试计数

          // 检查当前播放的流类型
          const isAiStream =
            this.currentStreamUrl &&
            (this.currentStreamUrl.includes('/ai/') || this.aiStreamConnected)

          if (isAiStream) {
            console.log('🎯 AI流播放成功，停止所有后台检测')
            console.log('当前播放的AI流地址:', this.currentStreamUrl)
            this.aiStreamConnected = true
            this.stopAllDetection()
          } else {
            console.log('📡 普通流播放成功')
            // 普通流播放成功时，如果还没有AI流，可以继续检测
            if (!this.aiStreamConnected) {
              this.isUpgrading = false // 重置升级状态，等待下次检测
            }
          }
        })

        this.playerInfo.on('error', (error) => {
          console.error(`❌ EasyPlayer播放错误: ${error}`)

          // 检测是否为网络获取错误，需要重新查询流地址
          if (this.isFetchError(error)) {
            console.log('检测到fetchError，流地址可能失效，将重新查询')
            this.handleStreamError(error)
          } else {
            this.handleStreamError(error)
          }
        })

        this.playerInfo.on('fullscreen', (flag) => {
          console.log('全屏状态:', flag)
        })
      },

      async playStream() {
        if (!this.playerInfo || !this.currentStreamUrl || this.isDestroyed) {
          console.warn('无法播放流 - 播放器未创建或流地址为空或组件已销毁')
          return
        }

        try {
          // 如果是相同的流地址，跳过
          if (this.lastPlayedUrl === this.currentStreamUrl && this.isPlaying) {
            console.log('相同流地址且正在播放，跳过:', this.currentStreamUrl)
            return
          }

          console.log('使用play方法播放流:', this.currentStreamUrl)
          console.log('上次播放的流:', this.lastPlayedUrl)
          console.log('AI流状态:', this.aiStreamConnected)

          await this.playerInfo.play(this.currentStreamUrl)
          // 记录成功播放的流地址
          this.lastPlayedUrl = this.currentStreamUrl
          console.log('流播放成功，当前URL:', this.currentStreamUrl)
        } catch (error) {
          console.error('播放流失败:', error)
          this.handleStreamError(error.message || error)
        }
      },

      isFetchError(error) {
        const errorStr = String(error).toLowerCase()
        return (
          errorStr.includes('fetcherror') ||
          errorStr.includes('fetch') ||
          errorStr.includes('network') ||
          errorStr.includes('404') ||
          errorStr.includes('403') ||
          errorStr.includes('500')
        )
      },

      handleStreamError(error = null) {
        this.streamError = true
        this.isLoading = false
        this.isPlaying = false
        this.isReconnecting = false
        this.isUpgrading = false

        // 检查是否是AI流错误
        const wasAIStream = this.aiStreamConnected

        console.log(`流播放错误，错误信息: ${error}`)

        // 停止所有检测
        this.stopAllDetection()

        // 不销毁播放器，只是停止播放
        this.stopPlayer()

        if (wasAIStream) {
          // AI流失败的特殊处理
          this.handleAIStreamFailure(error)
        } else {
          // 普通流失败的处理
          this.handleNormalStreamFailure(error)
        }
      },

      // 处理AI流失败
      handleAIStreamFailure(error) {
        console.log('🚫 AI流播放失败，设置冷却时间并切换到普通流', error)

        // 设置AI流冷却时间
        this.aiStreamFailedAt = Date.now()

        // 清除AI流状态
        this.aiStreamConnected = false
        this.lastPlayedUrl = null
        this.currentStreamUrl = null
        this.streamConnected = false

        // 直接尝试检测并播放普通流
        console.log('🔄 尝试切换到普通流')
        this.checkAndPlayStream()
      },

      // 处理普通流失败
      handleNormalStreamFailure(error) {
        console.log('📡 普通流播放失败，准备重试', error)

        // 清除当前播放记录
        this.lastPlayedUrl = null
        this.currentStreamUrl = null
        this.streamConnected = false
        this.aiStreamConnected = false

        // 进入重试逻辑
        if (!this.isDestroyed && this.retryCount < this.maxRetries) {
          this.scheduleRetry()
        } else if (this.retryCount >= this.maxRetries) {
          console.error(`已达到最大重试次数 ${this.maxRetries}，停止重试`)
        }
      },

      scheduleRetry() {
        if (this.isDestroyed) return

        this.retryCount++
        this.isReconnecting = true

        console.log(
          `准备第${this.retryCount}次重试，${this.retryInterval}ms后执行，将重新查询流地址`
        )

        this.retryTimer = setTimeout(() => {
          if (!this.isDestroyed) {
            // 重新开始流检测，从查询API开始
            this.startStreamCheck()
          }
        }, this.retryInterval)
      },

      startStreamCheck() {
        this.isLoading = true
        this.streamError = false
        this.isUpgrading = false
        // 注意：不重置 retryCount，保持重试计数
        console.log('开始流检测，当前重试次数:', this.retryCount)
        this.checkAndPlayStream()
      },

      // 重构后的流检测方法 - 只检测，不播放
      async detectStreams() {
        if (this.isDestroyed) {
          console.log('🚫 跳过流检测 - 组件已销毁')
          return null
        }

        if (this.checkingUrl) {
          console.log('🚫 跳过流检测 - 正在检测中')
          return null
        }

        this.checkingUrl = true
        console.log('🔍 开始检测流...')

        try {
          const data = { stream: this.device.sn }
          const response = await queryStream(data)

          if (
            !response ||
            !response.result ||
            !Array.isArray(response.result)
          ) {
            console.log('❌ 无效的API响应')
            return null
          }

          const streams = response.result
          const aiStream = streams.find(
            (s) => s.app === 'live/ai' || s.stream.includes('ai/')
          )
          const normalStream = streams.find(
            (s) => s.app === 'live/sk' || s.stream.includes('sk/')
          )

          console.log('📊 流检测结果:', {
            aiStream: !!aiStream,
            normalStream: !!normalStream,
            totalStreams: streams.length,
          })

          return { aiStream, normalStream }
        } catch (error) {
          console.error('❌ 流检测失败:', error)
          return null
        } finally {
          this.checkingUrl = false
        }
      },

      // 播放AI流
      async playAIStream(aiStream) {
        console.log('🎯 播放AI流:', aiStream)

        // 构建AI流URL
        let aiStreamUrl
        if (aiStream.app && aiStream.stream) {
          aiStreamUrl = `https://srs.3366998.xyz/${aiStream.app}/${aiStream.stream}.flv`
        } else if (aiStream.stream) {
          aiStreamUrl = `https://srs.3366998.xyz/live/ai/${aiStream.stream}.flv`
        } else {
          aiStreamUrl = `https://srs.3366998.xyz/live/ai/${this.device.sn}.flv`
        }

        // 清除AI流冷却时间
        this.aiStreamFailedAt = null

        // 设置AI流状态
        this.aiStreamConnected = true
        this.streamConnected = true
        this.currentStreamUrl = aiStreamUrl
        this.isLoading = false
        this.isReconnecting = false
        this.isUpgrading = false

        // 停止所有检测
        this.stopAllDetection()

        console.log('✅ AI流地址:', this.currentStreamUrl)
        this.createOrUpdatePlayer('AI流')
      },

      // 播放普通流
      async playNormalStream(normalStream) {
        console.log('📡 播放普通流:', normalStream)

        // 构建普通流URL
        const normalStreamUrl = `https://srs.3366998.xyz/${normalStream.app}/${normalStream.stream}.flv`

        // 设置普通流状态
        this.aiStreamConnected = false
        this.streamConnected = true
        this.currentStreamUrl = normalStreamUrl
        this.isLoading = false
        this.isReconnecting = false

        console.log('✅ 普通流地址:', this.currentStreamUrl)
        this.createOrUpdatePlayer('普通流')

        // 启动AI流检测
        this.startAIDetection()
      },

      // 检查AI流是否在冷却期
      isAIStreamInCooldown() {
        if (!this.aiStreamFailedAt) return false

        const now = Date.now()
        const timeSinceFailure = now - this.aiStreamFailedAt
        const inCooldown = timeSinceFailure < this.aiStreamCooldown

        if (inCooldown) {
          const remainingTime = Math.ceil(
            (this.aiStreamCooldown - timeSinceFailure) / 1000
          )
          console.log(`⏳ AI流在冷却期，剩余${remainingTime}秒`)
        }

        return inCooldown
      },

      // 主要的流检测和播放逻辑
      async checkAndPlayStream() {
        if (this.isDestroyed) return

        console.log('🚀 开始流检测和播放流程')

        try {
          const result = await this.detectStreams()

          if (!result) {
            console.log('❌ 流检测失败，准备重试')
            this.scheduleRetry()
            return
          }

          const { aiStream, normalStream } = result

          // 检查AI流是否在冷却期
          const aiStreamAvailable = aiStream && !this.isAIStreamInCooldown()

          // 优先检测AI流（如果不在冷却期）
          if (aiStreamAvailable) {
            console.log('🎯 检测到AI流且不在冷却期，直接播放')
            await this.playAIStream(aiStream)
            return
          }

          // AI流在冷却期或不存在，使用普通流
          if (normalStream) {
            if (aiStream && this.isAIStreamInCooldown()) {
              console.log('📡 AI流在冷却期，使用普通流并等待冷却结束')
            } else {
              console.log('📡 检测到普通流，播放并继续检测AI流')
            }
            await this.playNormalStream(normalStream)
            return
          }

          // 没有任何流
          console.log('❌ 未检测到任何流，准备重试')
          this.scheduleRetry()
        } catch (error) {
          console.error('❌ 流检测和播放过程出错:', error)
          this.scheduleRetry()
        }
      },

      // 启动AI流检测（在播放普通流时使用）
      startAIDetection() {
        if (this.isDestroyed || this.aiStreamConnected) {
          return
        }

        console.log('🔍 启动AI流检测定时器')
        this.isUpgrading = true

        this.retryTimer = setTimeout(() => {
          if (this.isDestroyed || this.aiStreamConnected) {
            console.log('🚫 取消AI流检测 - 组件已销毁或AI流已连接')
            this.isUpgrading = false
            return
          }

          console.log('🔍 执行AI流检测')
          this.checkForAIStream()
        }, this.retryInterval)
      },

      // 检测AI流（仅检测AI流，不检测普通流）
      async checkForAIStream() {
        if (this.isDestroyed) {
          this.isUpgrading = false
          return
        }

        // 如果当前已经是AI流且正在播放，跳过检测
        if (this.aiStreamConnected && this.isPlaying) {
          this.isUpgrading = false
          return
        }

        // 检查AI流是否在冷却期
        if (this.isAIStreamInCooldown()) {
          console.log('⏳ AI流在冷却期，跳过检测')
          this.startAIDetection() // 继续等待
          return
        }

        console.log('🔍 检测AI流是否可用...')

        try {
          const result = await this.detectStreams()

          if (!result) {
            console.log('❌ AI流检测失败，继续使用普通流')
            this.handleAIStreamNotAvailable()
            return
          }

          const { aiStream, normalStream } = result

          if (aiStream) {
            console.log('🎯 检测到AI流，切换播放')
            // 清除冷却时间
            this.aiStreamFailedAt = null
            await this.playAIStream(aiStream)
          } else {
            console.log('📡 暂无AI流，检查是否需要切换到普通流')
            this.handleAIStreamNotAvailable(normalStream)
          }
        } catch (error) {
          console.error('❌ AI流检测出错:', error)
          this.handleAIStreamNotAvailable()
        }
      },

      // 处理AI流不可用的情况
      handleAIStreamNotAvailable(normalStream = null) {
        // 如果当前是AI流但检测不到AI流，需要切换到普通流
        if (this.aiStreamConnected) {
          console.log('🔄 AI流不可用，切换到普通流')
          this.aiStreamConnected = false

          if (normalStream) {
            this.playNormalStream(normalStream)
          } else {
            // 重新检测所有流
            this.checkAndPlayStream()
          }
        } else {
          // 当前是普通流，继续检测AI流
          console.log('📡 继续检测AI流')
          this.startAIDetection()
        }
      },

      // 停止所有检测
      stopAllDetection() {
        console.log('🛑 停止所有流检测')
        this.stopRetryTimer()
        this.isUpgrading = false
        this.isReconnecting = false
        this.isLoading = false
        this.checkingUrl = false
      },

      // 获取播放器状态信息（用于调试）
      getPlayerStatus() {
        return {
          hasPlayer: !!this.playerInfo,
          playerInitialized: this.playerInitialized,
          currentUrl: this.currentStreamUrl,
          lastPlayedUrl: this.lastPlayedUrl,
          isPlaying: this.isPlaying,
          isLoading: this.isLoading,
          streamError: this.streamError,
          isReconnecting: this.isReconnecting,
          isUpgrading: this.isUpgrading,
          retryCount: this.retryCount,
          streamConnected: this.streamConnected,
          aiStreamConnected: this.aiStreamConnected,
          isCurrentStreamAI: this.isCurrentStreamAI
        }
      },

      handleClose() {
        console.log('开始关闭EasyPlayer直播窗口')
        this.isDestroyed = true
        this.showPlayer = false
        this.stopRetryTimer()
        this.destroyPlayer()
        this.resetPlayerState()
        this.$emit('close')
        console.log('EasyPlayer直播窗口关闭完成')
      },
    },
  }
</script>

<style lang="scss" scoped>
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    background: white;

    .stream-info {
      h3 {
        margin: 0;
        color: #303133;
        font-size: 18px;
        font-weight: 500;
      }

      .stream-url {
        margin: 5px 0 0 0;
        color: #606266;
        font-size: 12px;
        font-family: monospace;
        word-break: break-all;

        .stream-type {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
            sans-serif;
          font-size: 11px;
          font-weight: bold;
          padding: 2px 6px;
          border-radius: 3px;
          margin-left: 8px;

          &.ai-stream {
            background: #e7f3ff;
            color: #1890ff;
          }

          &.normal-stream {
            background: #f6ffed;
            color: #52c41a;
          }
        }
      }

      .last-played-url {
        margin: 3px 0 0 0;
        color: #909399;
        font-size: 11px;
        font-family: monospace;
        word-break: break-all;
        opacity: 0.8;
      }
    }

    .live-status {
      display: flex;
      align-items: center;
    }
  }

  .player-content {
    position: relative;
    background: #000;
    height: 420px;
  }

  .player-wrapper {
    position: relative;
    width: 100%;
    height: 420px;
    background: #000;

    .easyplayer-container {
      width: 100%;
      height: 100%;
      background: #000;
    }
  }

  .dialog-footer {
    .el-button {
      min-width: 120px;
      border-radius: 6px;
      font-weight: 500;
    }
  }

  @keyframes pulse {
    0% {
      opacity: 0.8;
      transform: scale(1);
    }
    50% {
      opacity: 1;
      transform: scale(1.1);
    }
    100% {
      opacity: 0.8;
      transform: scale(1);
    }
  }
</style>
<style lang="scss">
  .largescreen-live-player  {
    video {
      object-fit: cover !important;
    }
  }
</style>
