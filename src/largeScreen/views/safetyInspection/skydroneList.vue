<template>
  <div class="skydrone-list-box">
    <div class="module-title-box">
      <span>无人机设备管理</span>
    </div>
    <div class="project-list">
      <span>设备列表:</span>
      <el-select v-model="selectedProjectId" filterable placeholder="请选择" class="project-select"
        popper-class="largeScreen-project-selected" @change="onProjectChange">
        <el-option v-for="item in projectList" :key="item.id" :label="item.projectName" :value="item.id">
        </el-option>
      </el-select>
    </div>
    <div class="skydrone-list">
      <div class="skydrone-item" v-for="(device, index) in skydroneList" :key="index">
        <div class="skydrone-name">
          {{device.nickname || (device.device_model && device.device_model.name) || '飞行器'}}
        </div>
        <div class="skydrone-detail">
          <div class="skydrone-image">
            <img src="https://minio.jxth.com.cn/files/hnsz/c0560a7f-ce75-454b-ad86-36520cf19dfb.png" />
          </div>
          <div class="skydrone-info">
            <div class="info-item">
              <span class="info-type">SN:</span>
              <span class="info-val">{{ device.sn }}</span>
            </div>
            <div class="info-block">
              <div class="info-item">
                <span class="info-type">型号:</span>
                <span class="info-val">{{ (device.device_model && device.device_model.name) || '未知' }}</span>
              </div>
              <div class="info-item">
                <span class="info-type">类型:</span>
                <span class="info-val">{{ (device.device_model && device.device_model.class) || '未知' }}</span>
              </div>
            </div>
            <div class="options-btn">
              <div class="btn-block">
                <div class="btn-first" @click="handleEasyPlayerLiveClick(device, 'live')">开始直播</div>
                <div class="btn-second" @click="handleStopStream(device)">停止直播</div>
              </div>
              <div class="btn-block">
                <div class="btn-first" @click="handlePlaybackClick(device, 'video')">开始直播2</div>
                <div class="btn-second" @click="handleCockpitClick(device)">虚拟座舱</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="skydrone-video">
      <div class="module-title-box">
        <span>无人机视频</span>
      </div>
      <!-- <el-select v-model="selectedSkydroneId" filterable placeholder="请选择" class="skydrone-select"
        popper-class="largeScreen-project-selected" @change="onSkydroneChange">
        <el-option v-for="item in skydroneList" :key="item.id" :label="item.nickname" :value="item.sn">
        </el-option>
      </el-select> -->
    </div>
    <div class="skydrone-video-box">
      <!-- <el-table v-loading="listLoading" border :data="skydroneVideoList" height="420" max-height="420"
        size="medium" stripe element-loading-background="rgba(0, 0, 0, 0.5)">
        <el-table-column label="名称" prop="fileName"></el-table-column>
        <el-table-column label="设备" prop="bizId"></el-table-column>
        <el-table-column label="上传人" prop="createByName"></el-table-column>
        <el-table-column label="上传时间" prop="createTime"></el-table-column>
        <el-table-column label="操作" width="90" align="center">
          <template #default={row}>
            <el-button type="primary" @click="playSkydroneVideo(row)">播放</el-button>
          </template>
        </el-table-column>
      </el-table> -->
      <!-- EasyPlayer直播 -->
      <easy-player-live-viewer ref="livePlayerRef" :visible.sync="easyPlayerLiveVisible" :device="selectedDevice" :max-retries="2400"
        @close="handleEasyPlayerLiveClose" class="largeScreen-easy-player" />

      <!-- 视频回放弹窗 -->
      <video-playback-modal ref="videoPlayerRef" :visible.sync="videoPlaybackVisible" :device="selectedDevice" :append-to-body="true"
        @close="handleVideoPlaybackClose" />
    </div>

  </div>
</template>

<script>
  import {
    getProjectList,
    getProjectDevices,
    startLiveStream,
    startStreamingConverters,
    delStreamingConverters,
    queryStream
  } from '@/api/skydrone/skydrone-api'
  import {
    getUploadFilesByPage
  } from '@/api/system/uploadFile-api'
  import VideoPlaybackModal from './VideoPlaybackModal'
  import EasyPlayerLiveViewer from './EasyPlayerLiveViewer'
  export default {
    components: {
      VideoPlaybackModal,
      EasyPlayerLiveViewer,
    },
    data() {
      return {
        selectedProjectId: '',
        selectedSkydroneId: '',
        projectList: [], // 项目列表
        skydroneList: [], // 无人机列表
        skydroneVideoList: [], // 无人机视频
        selectedDevice: {},
        listLoading: false,
        videoPlaybackVisible: false,
        easyPlayerLiveVisible: false,
        // skydroneVideoVisible: false,
        playerType: '',
      }
    },
    created() {
      this.loadProjects()
      // this.getSkydroneVideoList()
    },
    methods: {
      // 加载项目列表
      async loadProjects() {
        try {
          const response = await getProjectList()
          if (response.result.code === 0) {
            const projectList = response.result.data.list || []
            this.projectList = projectList.map(item => {
              return {
                projectName: item.name,
                id: item.uuid,
              }
            })
            this.selectedProjectId = projectList[0].uuid
            await this.loadProjectDevices(this.selectedProjectId)
          } else {
            this.$message.error(response.result.message || '获取项目列表失败')
          }
        } catch (error) {
          this.$message.error('获取项目列表失败')
        }
      },
      onProjectChange() {
        this.loadProjectDevices(this.selectedProjectId)
      },
      // 加载项目下的设备列表
      async loadProjectDevices(projectUuid) {
        if (!projectUuid) return
        try {
          const response = await getProjectDevices(projectUuid)
          if (response.result.code === 0) {
            const skydroneList = response.result.data.list || []
            this.skydroneList = skydroneList.filter(device => device.is_org_device)
          } else {
            this.$message.error(response.result.message || '获取设备列表失败')
          }
        } catch (error) {
          this.$message.error('获取设备列表失败')
        }
      },
      async handleEasyPlayerLiveClick(device, type) {
        this.$refs.livePlayerRef.handleClose()
        this.$refs.videoPlayerRef.handleClose()
        this.playerType = type
        this.selectedDevice = device
        try {
          this.$loading({
            lock: true,
            text: '正在启动直播...',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })

          const queryData = {
            stream: device.sn,
            app: 'live/sk'
          }
          await queryStream(queryData).then(async response => {
            if (
              !response ||
              !response.result ||
              !Array.isArray(response.result) ||
              response.result.length === 0
            ) {
              // 构建直播参数
              const data = {
                "region": "cn",
                "converter_name": device.sn,
                "sn": device.sn,
                "camera": "88-0-0",
                "video": "normal-0",
                "idle_timeout": 30,
                "video_quality": 4,
                "bypass_option": {
                  "url": `rtmp://srs.3366998.xyz/live/sk/${device.sn}?secret=18d44630c086480783f62f33dbb7e09a`
                },
              }
              const response = await startStreamingConverters(data)
              // 检查响应结构
              if (response && response.result) {
                // 处理成功和已在运行的情况
                if (response.result.code === 0 || response.result.code === 213025) {
                  this.currentDeviceSn = this.selectedDevice.sn
                  this.easyPlayerLiveVisible = true
                  this.$loading().close()
                } else {
                  // 处理其他错误情况
                  this.$message.error('启动直播失败')
                }
              } else {
                this.$message.error('API响应格式异常，请检查后端接口')
              }
            } else {
              this.easyPlayerLiveVisible = true
            }
          })
        } catch (error) {
          this.$message.error('启动直播失败')
          this.playerType = ''
        } finally {
          this.$loading().close()
        }
      },
      // 处理EasyPlayer直播弹窗关闭
      handleEasyPlayerLiveClose() {
        this.easyPlayerLiveVisible = false
        this.selectedDevice = null
        this.playerType = ''
      },
      async handleStopStream(device) {
        if(this.playerType === 'live') {
          this.$refs.livePlayerRef.handleClose()
          try {
            this.$loading({
              lock: true,
              text: '正在关闭码流转发...',
              spinner: 'el-icon-loading',
              background: 'rgba(0, 0, 0, 0.7)'
            })

            const data = {
              channel: device.sn + '_88-0-0'
            }
            const response = await delStreamingConverters(data)

            if (response && response.result) {
              this.$message.success('操作成功!')
            } else {
              this.$message.error('关闭码流转发失败')
            }
          } catch (error) {
            this.$message.error('关闭码流转发失败')
          } finally {
            this.$loading().close()
          }
        } else if(this.playerType === 'video') {
          this.$refs.videoPlayerRef.handleClose()
        }
      },
      handlePlaybackClick(device, type) {
        this.$refs.livePlayerRef.handleClose()
        this.$refs.videoPlayerRef.handleClose()
        this.playerType = type
        this.selectedDevice = device
        this.videoPlaybackVisible = true
      },
      // 处理视频回放弹窗关闭
      handleVideoPlaybackClose() {
        this.videoPlaybackVisible = false
        this.selectedDevice = null
        this.playerType = ''
      },
      handleCockpitClick(device) {
        const {
          sn
        } = device
        const rul =
          'https://fh.dji.com/organization/203a1677-bd1a-4b0d-81e8-8254e7b1812a/project/c2c857f3-99de-4dd1-adca-a49805eb82c5?droneSn=' +
          sn + '&gatewaySn=9N9CN2M0012K9H#/cockpit'
        window.open(rul)
      },
      onSkydroneChange() {
        this.getSkydroneVideoList(this.selectedSkydroneId)
      },
      async getSkydroneVideoList(bizId = '') {
        this.listLoading = true
        const queryForm = {
          curPage: 1,
          pageSize: 10,
          bizId,
          bizCode: 'droneVideo'
        }
        const { result: { records, total }} = await getUploadFilesByPage(queryForm)
        this.skydroneVideoList = records
        this.listLoading = false
      },
      playSkydroneVideo(video) {
        console.log(video)
      }
    }
  }
</script>

<style scoped lang="scss">
  .skydrone-list-box {
    width: 100%;
  }

  .project-list {
    display: flex;
    align-items: center;
    margin-top: 14px;

    span {
      color: #22d3ff;
      padding-right: 10px;
    }

    ::v-deep .el-select:hover .el-input__inner {
      border-color: transparent !important;
    }

    ::v-deep .el-select .el-input__inner:focus {
      border-color: transparent !important;
    }

    ::v-deep .el-input {
      height: 100%;
    }

    ::v-deep .el-input__inner {
      background-color: transparent;
      height: 100%;
      color: #ffffff;
      border-color: transparent;

      &:hover {
        border-color: transparent !important;
      }
    }

    .project-select {
      width: 220px;
      height: 32px;
      background: linear-gradient(135deg, rgba(0, 120, 255, 0.8) 0%, rgba(0, 80, 200, 0.6) 100%);
      border: 1px solid rgba(0, 200, 255, 0.5);
      border-radius: 6px;
      color: #ffffff;
      font-size: 14px;
      outline: none;
      cursor: pointer;
      transition: all 0.3s ease;

      // 禁用状态
      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }

  }

  .module-title-box {
    position: relative;
    width: 100%;
    height: 34px;
    background-image: url('../../iconBg/projectProfile/bt_bg01.png');
    background-size: 100% 100%;
    margin-bottom: 10px;

    &::before {
      content: '';
      width: 284px;
      height: 14px;
      background-image: url('../../iconBg/projectProfile/bt_bg02.png');
      background-size: 100% 100%;
      position: absolute;
      left: 0;
      top: 40%;
      margin-top: -7px;

    }

    &::after {
      content: "";
      width: 40px;
      height: 40px;
      background-image: url('../../iconBg/projectProfile/bt_bg03.png');
      background-size: 100% 100%;
      position: absolute;
      left: 0;
      margin-top: -5px;
    }

    span {
      font-size: 18px;
      line-height: 30px;
      padding-left: 35px;
      font-family: 'PangMenZhengDao';
      font-style: italic;
      color: #fff;
    }
  }

  .skydrone-list {
    width: 100%;
    min-height: 265px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .skydrone-item {
      width: 430px;
      height: 265px;
      background-image: url('../../iconBg/droneList_bg.png');
      background-size: 100% 100%;
      box-sizing: border-box;
      padding: 15px 20px 15px 20px;

      .skydrone-name {
        width: 100%;
        height: 35px;
        line-height: 35px;
        font-family: 'PangMenZhengDao';
        font-style: italic;
        font-size: 20px;
        color: #fff;
        box-sizing: border-box;
        padding-left: 20px;
      }

      .skydrone-detail {
        width: 100%;
        height: calc(100% - 35px);
        display: flex;
        align-items: center;
        margin-top: 5px;

        .skydrone-image {
          width: 155px;
          text-align: center;

          img {
            width: 90%;
          }
        }
      }

      .skydrone-info {
        flex-grow: 1;
        flex-shrink: 1;
      }

      .info-block,
      .btn-block {
        display: flex;
        align-items: center;

        .info-item {
          width: 50%;
        }
      }

      .info-item {
        width: 100%;
        display: flex;
        align-items: center;
        font-size: 12px;
        border-bottom: 1px solid #334154;
        padding-bottom: 5px;
        margin-bottom: 15px;

        .info-type {
          color: #799dc7;
          padding-right: 5px;
        }

        .info-val {
          color: #73f2d0;
          letter-spacing: .1em;
        }
      }

      .options-btn {
        text-align: center;
        color: #fff;
        font-size: 12px;

        .btn-block {
          &:first-child {
            margin-bottom: 10px;
          }
        }

        .btn-first {
          width: 52%;
          height: 28px;
          line-height: 28px;
          background-image: url('../../iconBg/start_bg.png');
          background-size: 100% 100%;
          margin-right: 4px;
          cursor: pointer;
          transition: .3s ease;

          &:active {
            filter: opacity(.6);
          }
        }

        .btn-second {
          width: 48%;
          height: 28px;
          line-height: 28px;
          background-image: url('../../iconBg/stop_bg.png');
          background-size: 100% 100%;
          cursor: pointer;
          transition: .3s ease;

          &:active {
            filter: opacity(.6);
          }
        }
      }
    }
  }

  .skydrone-video {
    position: relative;
    margin-top: 50px;

    span {
      color: #22d3ff;
      padding-right: 10px;
    }

    ::v-deep .el-select:hover .el-input__inner {
      border-color: transparent !important;
    }

    ::v-deep .el-select .el-input__inner:focus {
      border-color: transparent !important;
    }

    ::v-deep .el-input {
      height: 100%;
    }

    ::v-deep .el-input__inner {
      background-color: transparent;
      height: 100%;
      color: #ffffff;
      border-color: transparent;

      &:hover {
        border-color: transparent !important;
      }
    }

    ::v-deep .el-input--small .el-input__icon {
      line-height: 28px;
    }

    .skydrone-select {
      position: absolute;
      right: 0;
      bottom: 10px;
      width: 140px;
      height: 28px;
      background: linear-gradient(135deg, rgba(0, 120, 255, 0.8) 0%, rgba(0, 80, 200, 0.6) 100%);
      border: 1px solid rgba(0, 200, 255, 0.5);
      border-radius: 6px;
      color: #ffffff;
      font-size: 14px;
      outline: none;
      cursor: pointer;
      transition: all 0.3s ease;

      // 禁用状态
      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
  }

  .skydrone-video-box {
    width: 100%;
    height: 420px;
    border: 1px solid #333;
    background-color: rgba(0, 0, 0, .4);
  }
</style>
<style lang="scss">
  .largeScreen-project-selected.el-select-dropdown {
    background-color: #333 !important;
    border: 1px solid #333 !important;

    .popper__arrow {
      border-bottom-color: #333 !important;

      &::after {
        border-bottom-color: #333 !important;
      }
    }

    .el-select-dropdown__item.hover,
    .el-select-dropdown__item:hover {
      background-color: #222 !important;
    }
  }

  .largeScreen-easy-player {
    .modal-header {
      background: #25273e !important;
    }

    .el-dialog__footer {
      background: #25273e !important;
    }
  }
</style>
