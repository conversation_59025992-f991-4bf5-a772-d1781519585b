<template>
  <div class="safeHelmetGroupTree">
    <div class="header">视频组</div>
    <div class="left-tree">
      <el-input v-model="filterText" clearable placeholder="输入关键字进行过滤" style="margin-bottom: 10px;" />
      <div id="tree-box" v-loading="treeLoading" element-loading-text="拼命加载中" element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.3)">
        <el-tree class="tree-box" ref="departTree" node-key="id" :data="treeData" :props="defaultProps"
          highlight-current default-expand-all :filter-node-method="filterNode" :expand-on-click-node="false"
          @node-click="departTreeClick">
          <span slot-scope="{node,data}" class="custom-tree-node">
            <span v-if="!data.children" :style="{ color: data.status === '1' ? '#67C23A' : '#F56C6C' }"
              :title="data.name" class="show-ellipsis"> <img src="../../iconBg/people_icon.png"
                style="vertical-align: middle;" />
              {{
              data.safetyHelmetByName ?
              data.safetyHelmetByName : data.name
              }} <i>({{data.status === '1' ? '在线' : '离线' }})</i></span>
            <span v-else-if="data.children" :title="data.name" class="show-ellipsis">
              <img src="../../iconBg/allPeople_icon.png" style="vertical-align: middle;" />
              {{ data.name +':'+ data.roomNumber}} </span>
            <i v-if="!data.children && !data.isCallActive" style="color: #6ee5f9;"
              @click.stop="$emit('call', data)"><img src="../../iconBg/videoTel_start.png"
                style="vertical-align: middle;" /> 通话
            </i>
            <i v-if="!data.children && data.isCallActive" style="color: #ff3b2f;" @click.stop="$emit('gua', data)"><img
                src="../../iconBg/videoTel_stop.png" style="vertical-align: middle;" /> 挂断
            </i>
          </span>
        </el-tree>
      </div>
    </div>
  </div>
</template>

<script>
  import { getTreeDataList } from '@/api/safetyHelmetManagement/safetyHelmetGroup-api'

  export default {
    components: {},
    props: {},
    data() {
      return {
        treeLoading: false,
        treeData: [],
        filterText: '',
        defaultProps: {
          children: 'children',
          label: 'safetyHelmetByName'
        },
      }
    },
    watch: {
      // 根据关键词过滤
      filterText(val) {
        this.$refs.departTree.filter(val)
      }
    },
    created() {
      this.initTree()
    },
    methods: {
      filterNode(value, data) {
        if (!value) return true
        return data.name.indexOf(value) !== -1
      },
      departTreeClick(obj, node, data) {
        if (!obj.children) {
          return
        }
        this.$emit('change', obj)
      },
      async initTree() {
        this.treeLoading = true
        const list = []
        await getTreeDataList({}).then((res) => {
          this.treeData = list.concat(res.result)
        })
        this.treeLoading = false
      },
    }
  }
</script>

<style scoped lang="scss">
  .safeHelmetGroupTree {
    width: 30%;

    ::v-deep .el-tree {
      background: none;
      color: #d0def5;
    }
  }

  .left-tree {
    ::v-deep .el-input__inner {
      background: #172b41;
      border: 1px solid #70a4e0;
      color: #FFFFFF;
    }

    #tree-box {
      width: 100%;
      height: 470px;
    }

    .tree-box {
      background: none;
      height: 100%;
      width: 100%;
      overflow: auto;
    }
  }

  .header {
    font-size: 18px;
    line-height: 30px;
    font-family: "PangMenZhengDao";
    font-style: italic;
    color: #fff;
  }

  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
    width: calc(100% - 48px);
  }

  .show-ellipsis {
    display: block;
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .clearfix {
    line-height: 29px;
  }
</style>
<style lang="scss">
  .tree-box .el-tree-node__content:hover {
    background-color: rgba(20, 36, 77, 0.8);
  }

  .tree-box .el-tree-node:focus>.el-tree-node__content {
    background-color: rgba(20, 36, 77, 0.8);
  }

  .tree-box.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
    background-color: rgba(20, 36, 77, 0.8);
  }

  /* tree结构自定义滚动条样式 */
  #tree-box ::-webkit-scrollbar {
    width: 4px;
  }

  #tree-box ::-webkit-scrollbar-thumb {
    border-radius: 4px;
    height: 40px;
    background: #58caf7;
  }

  #tree-box ::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: #050b21;
  }
</style>