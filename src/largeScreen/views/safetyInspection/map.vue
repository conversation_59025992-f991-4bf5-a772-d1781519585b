<template>
  <div class="map-box">
    <div id="mapGLBox" class="map"></div>
  </div>
</template>

<script>
  import { getSafetyHelmetDataGroupAllList } from '@/api/safetyHelmetManagement/safetyHelmetManagementBase-api.js'
  export default {
    data() {
      return {
        peopleInfoList: [],
        currentPagePath: '', //当前页面路由
        peopleInfoOverlay: null,
      }
    },
    created() {
      this.currentPagePath = this.$route.path
      this.timer = setInterval(async () => {
        await this.getPersonStatistics()
      }, 30000)
    },
    async mounted() {
      await this.getPersonStatistics()
      this.createMap()
    },
    beforeDestroy() {
      if (this.map) {
        this.map.clearOverlays()
      }
      clearInterval(this.timer)
    },
    methods: {
      async getPersonStatistics() {
        await getSafetyHelmetDataGroupAllList({}).then(res => {
          this.peopleInfoList = res.result
          // console.log(this.peopleInfoList)
          if (this.map) {
            this.map.clearOverlays()
            this.createPeoplePositionMaker()
          }
        })
      },
      async createMap() {
        this.map = new BMapGL.Map('mapGLBox', {})
        this.map.setMapType(BMAP_EARTH_MAP);
        let mapPoint = null
        this.map.enableScrollWheelZoom(true)
        if (this.peopleInfoList.length > 0) {
          const peopleInfoPoint = this.peopleInfoList[0]
          mapPoint = new BMapGL.Point(peopleInfoPoint.longitude, peopleInfoPoint.latitude)
        }
        const point = mapPoint || new BMapGL.Point(113.55411115051692, 35.069221418888205)
        this.map.centerAndZoom(point, 14)
        // this.map.setMapStyleV2({
        //   styleId: '9595b1c970298be4075a5f6c42f55dbd'
        // })
        this.createPeoplePositionMaker()
      },
      // 创建人员定位标识
      createPeoplePositionMaker() {
        const len = this.peopleInfoList.length
        for (let i = 0; i < len; i++) {
          const peopleInfo = this.peopleInfoList[i]
          const peopleOverlay = new BMapGL.CustomOverlay(createDOM, {
            point: new BMapGL.Point(peopleInfo.longitude, peopleInfo.latitude),
            offsetY: -35,
            properties: {
              ...peopleInfo,
            }
          });
          this.map.addOverlay(peopleOverlay);
          // this.setMapCenter(peopleInfo.postion)
          peopleOverlay.addEventListener('click', (e) => {
            const peopleInfo = e.target.properties
            this.createPeopleInfoCard(peopleInfo)
          });
        }

        function createDOM() {
          const div = document.createElement('div');
          div.style.zIndex = BMapGL.Overlay.getZIndex();
          div.style.width = '40px'
          div.style.height = '55px'
          div.style.textAlign = 'center'
          div.style.userSelect = 'none'

          //创建maker
          const maker = document.createElement('img')
          maker.style.width = '100%'
          maker.style.height = '100%'
          maker.draggable = false
          maker.src = `${require('@/largeScreen/iconBg/projectProfile/people_icon.png')}`
          div.appendChild(maker)

          //创建文本
          const title = document.createElement('div');
          title.style.position = 'relative'
          title.style.fontSize = '12px'
          title.style.color = '#fff'
          title.style.whiteSpace = 'nowrap'
          title.style.textOverflow = 'ellipsis'
          title.style.overflow = 'hidden'
          title.style.marginTop = '0px'
          div.appendChild(title);
          title.appendChild(document.createTextNode(this.properties.safetyHelmetByName));

          return div
        }
      },
      createPeopleInfoCard(peopleInfo) {
        if (this.peopleInfoOverlay) {
          this.map.removeOverlay(this.peopleInfoOverlay)
          this.peopleInfoOverlay = null
          return
        }
        this.peopleInfoOverlay = new BMapGL.CustomOverlay(createDOM, {
          point: new BMapGL.Point(peopleInfo.longitude, peopleInfo.latitude),
          offsetY: -40,
          offsetX: 150,
          properties: {
            ...peopleInfo,
          }
        });
        this.map.addOverlay(this.peopleInfoOverlay);
        function createDOM() {
          //卡片容器
          const cardBox = document.createElement('div');
          cardBox.style.zIndex = BMapGL.Overlay.getZIndex();
          cardBox.style.position = 'relative'
          cardBox.style.width = '250px'
          cardBox.style.height = '112px'
          cardBox.style.userSelect = 'none'
          cardBox.style.backgroundImage = `url(${require('@/largeScreen/iconBg/projectProfile/people_popupbg.png')})`
          cardBox.style.backgroundSize = '100% 100%'
          cardBox.style.boxSizing = 'border-box'
          cardBox.style.padding = '5px 20px'

          // 姓名
          const peopleName = document.createElement('div');
          peopleName.style.width = '100%'
          peopleName.style.height = '35px'
          peopleName.style.lineHeight = '35px'
          peopleName.style.boxSizing = 'border-box'
          peopleName.style.fontSize = '16px'
          peopleName.style.color = '#fff'
          peopleName.appendChild(document.createTextNode(this.properties.safetyHelmetByName));
          cardBox.appendChild(peopleName);

          // 时间
          const peopleConcat = document.createElement('div');
          peopleConcat.style.width = '100%'
          peopleConcat.style.height = '30px'
          peopleConcat.style.lineHeight = '30px'
          peopleConcat.style.boxSizing = 'border-box'
          peopleConcat.style.fontSize = '14px'
          peopleConcat.style.color = '#21eeff'
          peopleConcat.style.marginTop = '5px'
          peopleConcat.appendChild(document.createTextNode('时间：' + this.properties.time));
          cardBox.appendChild(peopleConcat);

          // 速度
          const peoplePost = document.createElement('div');
          peoplePost.style.width = '100%'
          peoplePost.style.height = '30px'
          peoplePost.style.lineHeight = '30px'
          peoplePost.style.boxSizing = 'border-box'
          peoplePost.style.fontSize = '14px'
          peoplePost.style.color = '#21eeff'
          peoplePost.appendChild(document.createTextNode('速度：' + this.properties.speed + 'km/h'));
          cardBox.appendChild(peoplePost);

          //连线
          const line = document.createElement('div');
          line.style.width = '60px'
          line.style.height = '14px'
          line.style.position = 'absolute'
          line.style.left = '-35px'
          line.style.top = '40px'
          line.style.transform = 'rotate(-45deg)'
          line.style.userSelect = 'none'
          line.style.backgroundImage = `url(${require('@/largeScreen/assets/point_b.png')})`
          line.style.backgroundSize = '100% 100%'
          cardBox.appendChild(line);
          return cardBox
        }
      },
    }
  }
</script>

<style scoped lang="scss">
  .map-box {
    /* position: absolute;
    top: 0px;
    left: 0px;
    right: 0px;
    bottom: 0; */
    height: 230px;
    width: 100%;
    margin-left: 15px;
    box-sizing: border-box;
    /* border: 2px solid #4995cc; */
    /* border-radius: 2px; */

    .project-num {
      position: absolute;
      left: 0;
      top: 15px;
      z-index: 999;
      box-sizing: border-box;
      pointer-events: none;

      .project-item {
        display: flex;
        align-items: center;
        color: #21eeff;
        margin-bottom: 10px;

        &:nth-child(2n) {
          .num {
            span {
              color: #21eeff;
            }
          }
        }

        .project-type {
          width: 75px;
          height: 40px;
          line-height: 40px;
          text-align: center;
          box-sizing: border-box;
          font-size: 16px;
          background-image: url('../../../largeScreen/iconBg/map/lable_name_bg.png');
          background-size: 100% 100%;
        }

        .num {
          padding-left: 5px;

          span {
            display: inline-block;
            width: 35px;
            height: 41px;
            color: #51ff12;
            font-size: 28px;
            font-weight: bolder;
            text-align: center;
            line-height: 41px;
            background-image: url('../../../largeScreen/iconBg/map/num_bg.png');
            background-size: 100% 100%;
          }
        }
      }
    }

    .status-box {
      width: 300px;
      height: 35px;
      box-sizing: border-box;
      padding: 0 15px;
      position: absolute;
      right: 0;
      top: 0;
      z-index: 999;
      background-color: rgba(0, 0, 0, .35);
      display: flex;
      align-items: center;
      justify-content: space-between;
      pointer-events: none;

      .status-item {
        cursor: pointer;

        img {
          width: 16px;
          vertical-align: text-top;
        }

        span {
          font-size: 14px;
          color: #fff;
          padding-left: 5px;
        }
      }
    }

    .map {
      width: 100%;
      height: 100%;
      background: none !important;
    }
  }
</style>