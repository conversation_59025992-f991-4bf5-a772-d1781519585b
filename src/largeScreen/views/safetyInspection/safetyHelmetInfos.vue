<template>
  <div class="attentance-box">
    <div class="attentance-list">
      <div class="list-head">
        <div class="head-item">序号</div>
        <div class="head-item">使用者</div>
        <div class="head-item">设备名称</div>
        <div class="head-item">设备编号</div>
        <div class="head-item">状态</div>
      </div>
      <div class="list-data" v-if="tableList.length > 0">
        <VueSeamlessScroll :data="tableList" :class-option="optionSetting" class="seamless-warp">
          <div class="list-item" v-for="(item, index) in tableList" :key="index">
            <div class="item-col">
              <span v-if="index === 0" class="txt-one txt-over">{{ index+1 }}</span>
              <span v-else-if="index === 1" class="txt-two txt-over">{{ index+1 }}</span>
              <span v-else-if="index === 2" class="txt-three txt-over">{{ index+1 }}</span>
              <span v-else class="txt-over">{{ index+1 }}</span>
            </div>
            <div class="item-col">{{ item.safetyHelmetByName }}</div>
            <div class="item-col" :data-name="item.name" @mouseenter="mouseEnter" @mouseleave="closePopper">
              {{ item.name }}
            </div>
            <div class="item-col">{{ item.deviceId }}</div>
            <div class="item-col">
              <span :class="item.status === '0' ? 'offline' : 'online'">
                {{ item.status === '0' ? '离线' : '在线' }}
              </span>
            </div>
          </div>
        </VueSeamlessScroll>
      </div>
    </div>
    <el-popover placement="top" v-model="popoverVisible" ref="popover" trigger="hover" effect="dark"
      popper-class="largeScreen-tooltip-name">
      <div>{{roleName}}</div>
    </el-popover>
  </div>
</template>

<script>
  import VueSeamlessScroll from 'vue-seamless-scroll'
  import { getDataList } from '@/api/safetyHelmetManagement/safetyHelmetGroupDetail-api'
  export default {
    components: {
      VueSeamlessScroll,
    },
    props: {
      selectedProject: {
        type: Object,
        default() {
          return {}
        }
      }
    },
    data() {
      return {
        tableList: [],
        optionSetting: {
          limitMoveNum: 11,
          step: 0.65,
          hoverStop: true,
          singleHeight: 40,
          waitTime: 2000
        },
        popoverVisible: false,
        roleName: '',
      }
    },
    watch: {

    },
    created() {
      this.getDataList()
    },
    methods: {
      async getDataList(projectId) {
        const { result } = await getDataList({})
        this.tableList = result
      },
      mouseEnter(e) {
        this.roleName = e.target.dataset.name
        this.popoverVisible = true
        let pop = this.$refs['popover']
        pop.updatePopper()
        pop.referenceElm = e.target
        this.$nextTick(() => {
          pop.popperJS._reference = e.target
          pop.popperJS.state.updateBound()
        })
      },
      closePopper() {
        this.popoverVisible = false
      },
    }
  }
</script>

<style scoped lang="scss">
  .attentance-box {
    width: 100%;
    height: 250px;
    box-sizing: border-box;
    /* padding-left: 25px; */
    padding-top: 15px;
    background-image: url('../../iconBg/ListOfHelmets_bg.png');
    background-size: 100% 100%;

    .attentance-list {
      width: 100%;
      height: 100%;

      .list-head {
        width: 100%;
        height: 33px;
        /* background-image: url('../../iconBg/table_title.png'); */
        background-size: 100% 100%;
        /* margin-bottom: 10px; */
        font-size: 0;

        .head-item {
          display: inline-block;
          width: 20%;
          line-height: 33px;
          font-size: 14px;
          color: #afbed8;
          text-align: center;
          font-weight: bold;

          /* &:nth-child(1) {
            width: 25%;
          }

          &:nth-child(3) {
            width: 45%;
          } */
        }
      }

      .list-data {
        width: 100%;
        height: 200px;
        overflow: hidden;

        .list-item {
          position: relative;
          width: 100%;
          height: 37px;
          /* margin-bottom: 6px; */
          background-size: 100% 100%;
          font-size: 0;

          /* &:nth-child(2n) {
            background-image: url('../../iconBg/line1_bg.png');
          }

          &:nth-child(2n + 1) {
            background-image: url('../../iconBg/line_bg.png');
          } */

          &::before {
            content: '';
            width: 5px;
            height: 5px;
            position: absolute;
            top: 3px;
            left: 2px;
            /* background-image: url('../../iconBg/bsj.png'); */
            background-size: 100% 100%;
          }

          .item-col {
            display: inline-block;
            width: 20%;
            box-sizing: border-box;
            padding: 0 5px;
            line-height: 34px;
            text-align: center;
            font-size: 14px;
            color: #b2bbd7;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;

            /* &:nth-child(1) {
              width: 25%;
            }

            &:nth-child(3) {
              width: 45%;
            } */
          }
        }
      }

      .seamless-warp {
        width: 100%;
        height: 100%;
      }
    }

    .offline {
      display: inline-block;
      width: 60px;
      height: 25px;
      background-color: #e4e4e4;
      border-radius: 2px;
      line-height: 25px;
      text-align: center;
      color: #00061a;
      font-size: 12px;
    }

    .online {
      display: inline-block;
      width: 60px;
      height: 25px;
      background-color: #e4e4e4;
      border-radius: 2px;
      line-height: 25px;
      text-align: center;
      color: #13ba45;
      font-size: 12px;
    }

    .txt-over {
      display: inline-block;
      width: 25px;
      height: 25px;
      line-height: 25px;
      text-align: center;
      border-radius: 100%;
      background-color: #697e9e;
      color: #dde5f3;

      &.txt-one {
        background-color: #dfa239;
      }

      &.txt-two {
        background-color: #768cb1;
      }

      &.txt-three {
        background-color: #aa805d;
      }
    }
  }
</style>
<style>
  .largeScreen-tooltip-name.el-popover {
    background: #3d3d51 !important;
    color: #fff !important;
    border-color: #3d3d51 !important;
    padding: 5px 10px !important;
  }

  .largeScreen-tooltip-name.el-popover .popper__arrow,
  .largeScreen-tooltip-name.el-popover .popper__arrow::after {
    border-top-color: #3d3d51 !important;
  }
</style>