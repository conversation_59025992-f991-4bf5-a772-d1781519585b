const router = {
  path: '/largeScreen',
  name: 'largeScreen',
  component: () => import('@/largeScreen/index'),
  redirect: '/largeScreen/home',
  meta: {
    hidden: true,
  },
  children: [
    {
      path: 'home',  //首页概览
      component: () => import('@/largeScreen/views/home.vue')
    },
    {
      path: 'index',  //项目概览
      component: () => import('@/largeScreen/views/projectOverview/index.vue')
    },
    {
      path: 'safetyInspection',  //项目概览
      component: () => import('@/largeScreen/views/safetyInspection/index.vue')
    }
  ],
}
export default router
