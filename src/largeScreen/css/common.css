.page-block-style {
  position: relative;
  box-sizing: border-box;
  border-top: 2px solid #28425b;
  background-image: linear-gradient(to bottom, rgba(4, 34, 62, 1), rgba(8, 12, 21, 1));
}
.page-block-style::before {
  content: '';
  position: absolute;
  top: -2px;
  left: 0;
  width: 19px;
  height: 2px;
  background-color: #5687b0;
}
.page-block-style::after {
  content: '';
  position: absolute;
  top: -2px;
  right: 0;
  width: 19px;
  height: 2px;
  background-color: #5687b0;
}

.page-block-style .page-top {
  position: relative;
  width: 100%;
  height: 50px;
  box-sizing: border-box;
  padding-right: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.page-block-style .page-top .top-title {
  width: 196px;
  height: 100%;
  background-image: url('../assets/title_bg.png');
  background-size: 100% 100%;
  font-size: 18px;
  color: #eaebec;
  line-height: 50px;
  box-sizing: border-box;
  padding-left: 15px;
}
