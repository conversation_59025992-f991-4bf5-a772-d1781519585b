<template>
  <div class="module-title-box">
    <span><slot></slot></span>
  </div>
</template>

<script>
</script>

<style scoped lang="scss">
.module-title-box {
  position: relative;
  width: 100%;
  height: 34px;
  background-image: url('../iconBg/projectProfile/bt_bg01.png');
  background-size: 100% 100%;
  margin-bottom: 10px;

  &::before {
    content: '';
    width: 284px;
    height: 14px;
    background-image: url('../iconBg/projectProfile/bt_bg02.png');
    background-size: 100% 100%;
    position: absolute;
    left: 0;
    top: 40%;
    margin-top: -7px;

  }

  &::after {
    content: "";
    width: 50px;
    height: 50px;
    background-image: url('../iconBg/projectProfile/bt_bg03.png');
    background-size: 100% 100%;
    position: absolute;
    left: -6px;
    margin-top: -14px;
  }

  span {
    line-height: 24px;
    padding-left: 35px;
    font-family: "Microsoft YaHei", "微软雅黑", "PingFang SC", "Hiragino Sans GB", "Helvetica Neue", Arial, sans-serif;
    font-size: 20px;
    font-weight: 700;
    background: linear-gradient(to bottom, #4facfe 20%, #fff 60%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  }
}
</style>
