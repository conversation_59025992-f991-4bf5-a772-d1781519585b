<template>
  <div class="module-title-box">
    <span><slot></slot></span>
  </div>
</template>

<script>
</script>

<style scoped lang="scss">
.module-title-box {
  position: relative;
  width: 100%;
  height: 34px;
  background-image: url('../../iconBg/projectProfile/bt_bg01.png');
  background-size: 100% 100%;
  margin-bottom: 10px;

  &::before {
    content: '';
    width: 284px;
    height: 14px;
    background-image: url('../../iconBg/projectProfile/bt_bg02.png');
    background-size: 100% 100%;
    position: absolute;
    left: 0;
    top: 40%;
    margin-top: -7px;

  }

  &::after {
    content: "";
    width: 40px;
    height: 40px;
    background-image: url('../../iconBg/projectProfile/bt_bg03.png');
    background-size: 100% 100%;
    position: absolute;
    left: 0;
    margin-top: -5px;
  }

  span {
    font-size: 18px;
    line-height: 30px;
    padding-left: 35px;
    font-family: 'PangMenZhengDao';
    font-style: italic;
    color: #fff;
  }
}
</style>
