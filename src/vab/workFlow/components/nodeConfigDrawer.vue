<template>
  <el-drawer
    append-to-body
    destroy-on-close
    direction="rtl"
    :modal="false"
    :modal-append-to-body="false"
    :show-close="true"
    size="500px"
    :visible.sync="showDrawer"
  >
    <!-- <div slot="title">
      <el-input v-model="selectedNode.name" size="medium" v-show="showInput" style="width: 300px"
        @blur="showInput = false" v-focus></el-input>
      <el-link v-show="!showInput" @click="showInput = true" style="font-size: medium">
        <i class="el-icon-edit" style="margin-right: 10px"></i>
        {{selectedNode.name}}
      </el-link>
    </div> -->
    <div class="node-config-content">
      <NodeCofig
        v-if="selectedNode.type === 'userTask'"
        :approval-condition-list="approvalConditionList"
        :selected-node="selectedNode"
      />
      <ConditionNodeConfig
        v-if="selectedNode.type === 'condition'"
        v-bind="$attrs"
        :depart-list="departList"
        :file-type-list="fileTypeList"
        :receptionTypeList="receptionTypeList"
        :rechargeTypeList="rechargeTypeList"
        :role-list="roleList"
        :selected-node="selectedNode"
      />
    </div>
  </el-drawer>
</template>

<script>
  import { mapMutations } from 'vuex'
  import { getDataList as getDictList } from '@/api/system/dict-api'
  import { getDepartList } from '@/api/system/depart-api'
  import { getRoleList } from '@/api/system/role-api'
  import NodeCofig from '../common/nodeConfig.vue'
  import ConditionNodeConfig from '../common/ConditionNodeConfig.vue'
  export default {
    components: {
      NodeCofig,
      ConditionNodeConfig,
    },
    data() {
      return {
        showInput: false,
        approvalConditionList: [],
        departList: [],
        roleList: [],
        fileTypeList: [],
        receptionTypeList: [],
        rechargeTypeList: [
          { id: '0', label: '现金' },
          { id: '1', label: '油卡' },
        ],
      }
    },
    computed: {
      selectedNode() {
        return this.$store.getters['workFlow/selectedNode']
      },
      showDrawer: {
        get() {
          return this.$store.getters['workFlow/showNodeConfigDrawer']
        },
        set() {
          this.setShowNodeConfigDrawer(false)
        },
      },
    },
    created() {
      this.getDepartList()
      this.getRoleList()
      this.getDictListByCodes()
    },
    methods: {
      ...mapMutations({
        setShowNodeConfigDrawer: 'workFlow/setShowNodeConfigDrawer',
      }),
      async getDepartList() {
        const { result } = await getDepartList({})
        this.departList = result.map((item) => {
          return {
            id: item.id,
            label: item.departName,
            departCode: item.departCode,
          }
        })
      },
      async getRoleList() {
        const { result } = await getRoleList({})
        this.roleList = result.map((item) => {
          return {
            id: item.id,
            label: item.roleName,
          }
        })
      },
      async getDictListByCodes() {
        await getDictList({
          dictCode: 'fileType,receptionType,dynamicApproval',
        }).then((res) => {
          res.result.forEach((item) => {
            if (item.dictCode === 'fileType') {
              this.fileTypeList = item.children.map((item) => {
                return {
                  id: item.id,
                  label: item.dictName,
                }
              })
            }
            if (item.dictCode === 'receptionType') {
              this.receptionTypeList = item.children.map((item) => {
                return {
                  id: item.id,
                  label: item.dictName,
                }
              })
            }
            if (item.dictCode === 'dynamicApproval') {
              const data = item.children
              let list = []
              data.reduce((data, item) => {
                if (!data.includes(item.id)) {
                  data.push(item.id)
                  list.push({
                    id: item.id,
                    label: item.dictName,
                    value: item.dictCode,
                  })
                }
                return data
              }, [])
              this.approvalConditionList = list
            }
          })
        })
      },
    },
  }
</script>

<style scoped lang="scss">
  .node-config-content {
    box-sizing: border-box;
    padding: 0 20px 20px;
  }
</style>
