<template>
  <div>
    <el-button size="mini" icon="el-icon-plus" type="primary" @click="selectOrg" round>选择抄送人</el-button>
    <div class="option">
      <el-checkbox label="允许发起人添加抄送人" v-model="config.shouldAdd"></el-checkbox>
    </div>
    <org-items v-model="select" />
  </div>
</template>

<script>
  import OrgItems from "./OrgItems";

  export default {
    name: "CcNodeConfig",
    components: {
      OrgItems
    },
    props: {
      config: {
        type: Object,
        default: () => {
          return {}
        }
      }
    },
    computed: {
      select: {
        get() {
          return this.config.assignedUser || []
        },
        set(val) {
          this.config.assignedUser = val
        }
      }
    },
    data() {
      return {}
    },
    methods: {
      selectOrg() {
        alert("人员选择")
      }
    }
  }
</script>

<style lang="scss" scoped>
  .option {
    color: #606266;
    margin-top: 20px;
    font-size: small;
  }

  .desc {
    font-size: small;
    color: #8c8c8c;
  }

  .org-item {
    margin: 5px;
  }
</style>
