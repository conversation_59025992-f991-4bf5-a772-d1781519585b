<template>
  <div style="padding-top: 10px">
    <el-timeline v-loading="listLoading" style="min-width: 400px">
      <el-timeline-item
        v-for="(item, index) in approvalFlowHistoryData"
        :key="index"
        :color="item.color"
        :hide-timestamp="!item.endTime"
        placement="top"
        :timestamp="item.endTime"
      >
        <template v-if="!item.color" #dot>
          <span class="vab-dot" :class="['vab-dot-' + item.icon]">
            <span></span>
          </span>
        </template>

        <div
          v-if="item.type == 'start'"
          class="vab-info"
          style="line-height: 150%"
        >
          开始审批
        </div>
        <div
          v-else-if="item.type == 'finished'"
          class="vab-info-card"
          :class="['vab-info-card-' + item.icon]"
          style="line-height: 150%"
        >
          <div>节点：{{ item.taskName }}（用时: {{ item.duration }}）</div>
          <div v-if="item.assignee">
            审&nbsp;&nbsp;批&nbsp;&nbsp;人：{{ item.assignee }}（{{
              approvalResultFormat(item)
            }}）
          </div>
          <div v-if="item.comment">审批意见：{{ item.comment }}</div>

          <div
            v-if="item.sendCopyUserNames && item.sendCopyUserNames.length > 0"
            style="margin-top: 5px"
          >
            抄&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;送：{{
              item.sendCopyUserNames.join('  ')
            }}
          </div>
        </div>

        <div
          v-else-if="item.type == 'incomplete'"
          class="vab-info vab-info-card-info"
          style="line-height: 150%"
        >
          <div>节点: {{ item.name }}（{{ item.assigneeName }}）</div>
          <div
            v-if="item.sendCopyUserNames && item.sendCopyUserNames.length > 0"
            style="margin-top: 5px"
          >
            抄送：{{ item.sendCopyUserNames.join('  ') }}
          </div>
        </div>
        <div
          v-else-if="item.type == 'end'"
          :class="globalCc.length > 0 ? 'vab-info-card vab-info-card-success' : 'vab-info vab-info-card-info'"
          style="line-height: 150%"
        >
          <div>结束审批</div>
          <div
            v-if="globalCc.length > 0"
            style="margin-top: 5px"
          >
            抄送：{{ globalCc.join('  ') }}
          </div>
        </div>
      </el-timeline-item>
    </el-timeline>
  </div>
</template>

<script>
  import {
    getTaskProcessListByBizKey,
    getPreview,
  } from '@/api/workflow/workflow-api'
  export default {
    name: 'VabWorkFlowApproveRecDlg',
    components: {},
    data() {
      return {
        approvalFlowHistoryData: [],
        listLoading: false,
        bizKey: '',
        globalCc: []
      }
    },
    watch: {},
    created() {},
    methods: {
      async getTaskProcessListByBizKey(bizKey) {
        this.bizKey = bizKey
        this.approvalFlowHistoryData = []
        if (bizKey == '') return
        this.listLoading = true
        let lastTaskDefKey = ''
        await getTaskProcessListByBizKey({
          bizKey,
        }).then((response) => {
          if (response.result.length > 0) {
            this.approvalFlowHistoryData.push({
              endTime: response.result[0].startTime,
              type: 'start',
              icon: 'success',
            })
            response.result.forEach((item) => {
              this.approvalFlowHistoryData.push({
                ...item,
                type: 'finished',
                icon: item.approvalResult == 1 ? 'success' : 'error',
                sendCopyUserNames: [],
              })
            })
            lastTaskDefKey =
              response.result[response.result.length - 1].taskDefKey
          } else {
            this.approvalFlowHistoryData.push({
              type: 'start',
              icon: 'info',
            })
          }
        })
        let hasEnd = true
        await getPreview({
          bizKey,
        }).then((response) => {
          const dataInfo = response.result.dataInfo.filter(value=>value.hasOwnProperty('assigneeName'))
          const globalCcInfo = response.result.dataInfo.find( value=> !value.hasOwnProperty('assigneeName'))
          if(globalCcInfo){
            this.globalCc = globalCcInfo.sendCopyUserNames
          }
          // console.log(this.globalCc)
          //已完成的节点增加抄送信息
          dataInfo.forEach((item) => {
            if (item.sendCopyUserNames && item.sendCopyUserNames.length > 0) {
              this.approvalFlowHistoryData.forEach((flow) => {
                if (flow.taskDefKey && flow.taskDefKey == item.itemId) {
                  flow.sendCopyUserNames = item.sendCopyUserNames
                }
              })
            }
          })

          if (lastTaskDefKey == '') {
            hasEnd = false
            dataInfo.forEach((item) => {
              this.approvalFlowHistoryData.push({
                ...item,
                type: 'incomplete',
                icon: 'info',
              })
            })
          } else {
            let lastTaskDefKeyIndex = 0
            dataInfo.forEach((item, index) => {
              if (item.itemId == lastTaskDefKey) {
                lastTaskDefKeyIndex = index
              }
            })
            if (lastTaskDefKeyIndex < dataInfo.length - 1) {
              //有未完成
              hasEnd = false
              dataInfo.forEach((item, index) => {
                if (index > lastTaskDefKeyIndex) {
                  this.approvalFlowHistoryData.push({
                    ...item,
                    type: 'incomplete',
                    icon: 'info',
                  })
                }
              })
            }
          }
        })

        this.approvalFlowHistoryData.push({
          type: 'end',
          icon: hasEnd ? 'success' : 'info',
        })
        this.listLoading = false
      },
      approvalResultFormat(row) {
        if (row.approvalResult === '1') {
          return '同意'
        } else if (row.approvalResult === '0') {
          return '不同意'
        }
      },
    },
  }
</script>
<style lang="scss" scoped>
  ::v-deep {
    .el-timeline-item__dot {
      [class*='ri'] {
        width: 12px;
        height: 12px;
        margin-left: -3px;
        background: $base-color-white;
      }

      .vab-dot {
        left: -1px;
        width: 12px;
        height: 12px;
        margin: auto !important;
        &-info {
          background: #e2e2e2;

          span {
            background: #c4c3c3;
          }
        }
      }
    }

    .el-card {
      .el-card__header {
        position: relative;

        .card-header-radio {
          position: absolute;
          top: 20px;
          right: $base-margin;
        }
      }
    }
  }
  .vab-info-card {
    position: relative;
    width: 80%;
    padding: $base-padding;
    background: #e2e2e2;
    border-radius: $base-border-radius + 2;

    &:after {
      position: absolute;
      top: 8px;
      left: -10px;
      width: 0;
      height: 0;
      overflow: hidden;
      content: '';
      border-color: #e2e2e2 transparent transparent;
      border-style: solid dashed dashed;
      border-width: 10px;
    }

    &-success {
      color: $base-color-white;
      background: $base-color-green;

      &:after {
        border-color: $base-color-green transparent transparent;
      }
    }

    &-error {
      color: $base-color-white;
      background: $base-color-red;

      &:after {
        border-color: $base-color-red transparent transparent;
      }
    }

    &-warning {
      color: $base-color-white;
      background: $base-color-orange;

      &:after {
        border-color: $base-color-orange transparent transparent;
      }
    }
  }
</style>
