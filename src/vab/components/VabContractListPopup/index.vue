<template>
  <el-dialog
    v-drag
    append-to-body
    :title="title"
    :visible.sync="dialogFormVisible"
    width="1450px"
    :close-on-click-modal="false"
    top="2vh"
    @closed="close"
  >
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="80px"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item label="合同名称" prop="contractName">
            <el-input
              v-model="queryForm.contractName"
              placeholder="请输入合同名称"
            />
          </el-form-item>
          <el-form-item label="签署单位" prop="clienteleName">
            <el-input
              v-model="queryForm.clienteleName"
              placeholder="请输入签署单位"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              icon="el-icon-search"
              native-type="submit"
              type="primary"
              @click="handleQuery"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-right"
              @click.native="resetForm('form')"
            >
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
    </vab-query-form>
    <el-table
      ref="tableDataRef"
      v-loading="listLoading"
      border
      :height="450"
      :data="list"
      stripe
      row-key="id"
      @row-click="handleRowClick"
      @select="handleRowSelect"
      @select-all="selectAll"
    >
      <el-table-column type="selection" label="选择" width="55" align="center" :reserve-selection="true"  />
      <el-table-column
        v-for="(item, index) in columns"
        :key="index"
        :align="item.center ? item.center : 'center'"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'"
      >
      <template #default="{ row }">
        <span v-if="item.prop === 'contractMoney' || item.prop === 'contractRealMoney'">
          {{ thousands(row[item.prop]) }}
        </span>
        <span v-else>{{ row[item.prop] }}</span>
      </template>
      </el-table-column>
      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-sizes="[5, 10, 20]"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <div class="select-content">
      <div class="select-title">当前已选择:</div>
      <div class="select-list">
        <el-tag
          v-for="item in selectRows"
          :key="item.id"
          closable
          @close="closeTag(item)"
        >{{item.contractName}}</el-tag>
      </div>
    </div>
    <template #footer>
      <el-button @click="dialogFormVisible = false">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
  import { getContracPageData } from '@/api/contract'
  import { thousands } from '@/utils/util'
  export default {
    name: 'VabContractListPopup',
    data() {
      return {
        title:'选择合同',
        listLoading: false,
        dialogFormVisible: false,
        queryForm: {
          contractName: '',
          clienteleName: '',
          selectType: 'income',
          allPermissions: true,
        },
        layout: 'total, sizes, prev, pager, next, jumper',
        pageInfo: {
          curPage: 1,
          pageSize: 10,
          total: 0
        },
        selectRows: [],
        columns: [
          {
              label: '单号',
              prop: 'serialNumber',
              disableCheck: true
            },
            {
              label: '合同名称',
              prop: 'contractName'
            },
            {
              label: '合同金额(元)',
              prop: 'contractMoney',
              width: '150px'
            },
            {
              label: '变更后金额(元)',
              prop: 'contractRealMoney',
              width: '150px'
            },
            {
              label: '合同类型',
              prop: 'contractTypeName',
              width: '150px'
            },
            {
              label: '签署单位',
              prop: 'clienteleName'
            },
            {
              label: '所属部门',
              prop: 'departName',
              width: '150px'
            }
        ],
        list:[],
        selectIds: [],
        isMulti: false,
      }
    },
    computed: {
    },
    created() {
    },
    methods:{
      showDialog({ selectIds, isMulti, selectRows = [] }){
        this.isMulti = isMulti || false
        this.selectRows = selectRows
        if(selectIds) {
          this.selectIds = selectIds.split(',').map(item => {
            return {id: item}
          })
        }
        this.fetchData()
        this.dialogFormVisible = true
        if(this.selectIds.length > 0) {
          this.$nextTick(() => {
            this.$refs.tableDataRef.store.states.selection = [...this.selectIds]
          })
        }
      },
      selectAll(selection) {
        if (!this.isMulti) {
          this.$refs.tableDataRef.clearSelection()
          this.selectRows = []
        }else{
          this.selectRows = selection
        }
      },
      handleRowClick(row) {
        const idx = this.selectRows.findIndex(item => item.id === row.id)
        if(this.isMulti) {
          if(idx >= 0) {
            this.selectRows.splice(idx, 1)
            this.$refs.tableDataRef.toggleRowSelection(row, false)
          } else {
            this.selectRows.push(row)
            this.$refs.tableDataRef.toggleRowSelection(row, true)
          }
        } else {
          this.$refs.tableDataRef.clearSelection()
          if(idx >= 0) {
            this.selectRows = []
          } else {
            this.$refs.tableDataRef.toggleRowSelection(row, true)
            this.selectRows = [row]
          }
        }
      },
      handleRowSelect(selection, row) {
        if (this.isMulti) {
          this.selectRows = selection
        } else {
          this.$refs.tableDataRef.clearSelection()
          if(this.selectRows.length > 0 && selection.length === 0) {
            this.selectRows = []
          } else {
            this.$refs.tableDataRef.toggleRowSelection(row, true)
            this.selectRows = [row]
          }
        }
      },
      closeTag(row) {
        const idx = this.selectRows.findIndex(item => item.id === row.id)
        this.selectRows.splice(idx, 1)
        const { selection } = this.$refs.tableDataRef.store.states
        this.$refs.tableDataRef.store.states.selection = selection.filter(item => item.id !== row.id)
      },
      save(){
        let data = []
        if(this.selectRows && this.selectRows.length) {
          data = data.concat(this.selectRows)
        }
        this.$emit('getContractInfo', data)
        this.dialogFormVisible = false
      },
      close() {
        this.dialogFormVisible = false
       this.$refs.tableDataRef.clearSelection()
       this.selectRows = []
       this.selectIds = []
       this.pageInfo.curPage = 1
       this.$refs.form.resetFields()
      },
      async fetchData() {
        this.queryForm = {
          ...this.queryForm,
          curPage: this.pageInfo.curPage,
          pageSize: this.pageInfo.pageSize,
          approvalResult: 2,
        }
        this.listLoading = true
        const { result: { records, total }} = await getContracPageData(this.queryForm)
        this.list = records
        this.pageInfo.total = Number(total)
        this.listLoading = false
      },
      handleSizeChange(val) {
        this.pageInfo.pageSize = val
        this.pageInfo.curPage = 1
        this.fetchData()
      },
      handleCurrentChange(val) {
        this.pageInfo.curPage = val
        this.fetchData()
      },
      handleQuery() {
        this.pageInfo.curPage = 1
        this.fetchData()
      },
      resetForm(formName) {
        this.pageInfo.curPage = 1
        this.$refs[formName].resetFields()
        this.fetchData()
      },
      thousands(amount) {
        return thousands(amount)
      }
    }
  }
</script>

<style lang="scss" scoped>
::v-deep .el-table__header .el-checkbox {
  display: none;
}
.select-content {
  margin-top: 10px;
  .select-list {
    margin-top: 10px;
  }
}
</style>
