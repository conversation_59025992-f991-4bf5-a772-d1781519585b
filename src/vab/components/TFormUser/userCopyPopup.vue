<template>
  <el-dialog
    v-drag
    append-to-body
    :close-on-click-modal="false"
    :title="title"
    top="2vh"
    :visible.sync="dialogFormVisible"
    width="1300px"
  >
    <el-row :gutter="20">
      <el-col :span="6">
        <el-tree
          ref="departTree"
          v-loading="treeLoading"
          class="el_tree"
          :data="treeData"
          default-expand-all
          :expand-on-click-node="false"
          highlight-current
          node-key="id"
          :props="defaultProps"
          @node-click="departTreeClick"
        >
          <span slot-scope="{ data }" class="custom-tree-node">
            <span
              v-if="data.children.length == 0"
              class="show-ellipsis"
              :title="data.label"
            >
              <i class="el-icon-user" />
              {{ data.label }}
            </span>
            <span
              v-else-if="data.children.length !== 0"
              class="show-ellipsis"
              :title="data.label"
            >
              <vab-icon-mix icon="organization" />
              {{ data.label }}
            </span>
          </span>
        </el-tree>
      </el-col>
      <el-col :span="18">
        <vab-query-form>
          <vab-query-form-top-panel>
            <el-form
              ref="form"
              :inline="true"
              label-width="80px"
              :model="queryForm"
              @submit.native.prevent
            >
              <el-form-item label="用户姓名" prop="userName">
                <el-input
                  v-model="queryForm.userName"
                  placeholder="请输入用户姓名"
                />
              </el-form-item>
              <!-- <el-form-item label="用户账号" prop="userAccount">
            <el-input
              v-model="queryForm.userAccount"
              placeholder="请输入用户账号"
            />
          </el-form-item> -->
              <el-form-item label="手机号" prop="telephone">
                <el-input
                  v-model="queryForm.telephone"
                  placeholder="请输入手机号"
                />
              </el-form-item>

              <el-form-item>
                <el-button
                  icon="el-icon-search"
                  native-type="submit"
                  type="primary"
                  @click="handleQuery"
                >
                  查询
                </el-button>
                <el-button
                  icon="el-icon-refresh-right"
                  @click.native="resetForm('form')"
                >
                  重置
                </el-button>
              </el-form-item>
            </el-form>
          </vab-query-form-top-panel>
        </vab-query-form>
        <el-table
          ref="tableSort"
          v-loading="listLoading"
          border
          :data="list"
          :height="450"
          stripe
          @selection-change="setSelectRows"
        >
          <el-table-column align="center" label="选择" width="55">
            <template #default="{ row }">
              <el-radio
                v-model="radio"
                :disabled="row.state === '0'"
                :label="row.id"
                @change="getCurrentRow(row)"
              >
                {{ '' }}
              </el-radio>
            </template>
          </el-table-column>
          <el-table-column
            v-for="(item, index) in columns"
            :key="index"
            :align="item.center ? item.center : 'center'"
            :label="item.label"
            :prop="item.prop"
            :sortable="item.sortable ? item.sortable : false"
            :width="item.width ? item.width : 'auto'"
          >
            <template #default="{ row }">
              <span v-if="item.label === '所在部门'">
                {{ backDepart(row.departList) }}
              </span>
              <span v-else-if="item.label === '角色'">
                {{ backRolelist(row.roleList) }}
              </span>
              <span v-else>{{ row[item.prop] }}</span>
            </template>
          </el-table-column>
          <template #empty>
            <el-image
              class="vab-data-empty"
              :src="require('@/assets/empty_images/data_empty.png')"
            />
          </template>
        </el-table>
      </el-col>
    </el-row>
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-size="pageInfo.pageSize"
      :page-sizes="[5, 10, 20]"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <template #footer>
      <div style="text-align: left">
        <!--        <div style="color: red;margin-bottom: 5px; margin-top: -30px;">*-->
        <!--          <span>(选择后只添加，删除请点击删除)</span>-->
        <!--        </div>-->
        当前选择：
        <el-tag
          v-for="(tag, index) in selectUsers"
          :key="index"
          :closable="multiple"
          size="medium"
          @close="handleClose(index)"
        >
          {{ tag.userName }}
        </el-tag>
      </div>
      <el-button @click="dialogFormVisible = false">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
  import { getUserByPage } from '@/api/system/user-api'
  import { getDepartList } from '@/api/system/depart-api'
  import { formatEleDepartTree } from '@/utils/util.js'

  export default {
    data() {
      return {
        treeLoading: false,
        treeData: [],
        defaultProps: {
          children: 'children',
          label: 'label',
        },
        title: '请选择',
        dialogFormVisible: false,
        queryForm: {
          userAccount: '',
          userName: '',
          telephone: '',
          endTime: '',
          departCode: '',
        },
        layout: 'total, sizes, prev, pager, next, jumper',
        pageInfo: {
          curPage: 1,
          pageSize: 10,
          total: 0,
        },
        selectRows: [],
        columns: [
          {
            label: '用户姓名',
            prop: 'userName',
          },
          // {
          //   label: '用户账号',
          //   prop: 'userAccount',
          // },
          // {
          //   label: '性别',
          //   prop: 'genderName',
          // },
          {
            label: '手机号',
            prop: 'telephone',
          },
          // {
          //   label: '邮箱',
          //   prop: 'email',
          // },
          // {
          //   label: '角色',
          //   prop: 'roleList',
          // },
          {
            label: '所在部门',
            prop: 'departList',
          },
        ],
        list: [],
        selectUsers: [],
        radio: '',
        userList: [],
        multiple: false,
        listLoading: false,
        search: false,
      }
    },
    computed: {
      //   pageUserList(){
      //     let tempList=[...this.userList]
      //     if(this.search==true){
      //       tempList=tempList.filter((item,index)=>{
      //       return (this.queryForm.userName=='' || item.userName.indexOf(this.queryForm.userName)>=0) &&
      //       (this.queryForm.userAccount=='' || item.userAccount.indexOf(this.queryForm.userAccount)>=0)&&
      //       (this.queryForm.telephone=='' || item.telephone.indexOf(this.queryForm.telephone)>=0)
      //     })
      //     }
      //   this.pageInfo.total = tempList.length
      //   return tempList.slice(
      //   (this.pageInfo.curPage - 1) * this.pageInfo.pageSize,
      //   this.pageInfo.curPage * this.pageInfo.pageSize
      // )
      //   }
    },
    created() {
      this.fetchData()
      this.initTree()
    },
    methods: {
      async initTree() {
        this.treeLoading = true
        const { result } = await getDepartList({})
        this.treeData = formatEleDepartTree(result, '-1')
        // this.treeData = attachTopNode(this.treeData, '根机构', )
        this.treeLoading = false
      },
      departTreeClick(data) {
        this.queryForm.departCode = data.departCode
        this.pageInfo.curPage = 1
        this.fetchData()
      },
      showUserDialog(data) {
        this.multiple = data.multiple
        this.userList = data.userList
        this.selectUsers = data.userIds
        this.radio = this.selectUsers.map((user) => user.id).toString()
        this.dialogFormVisible = true
      },
      setSelectRows(val) {
        // 保存当前选中的对象
        this.selectRows = val
      },
      getCurrentRow(row) {
        //获取选中数据
        if (this.multiple) {
          let ids = this.selectUsers.map((item) => item.id)
          if (ids.indexOf(row.id) < 0) {
            this.selectUsers.push(row)
          }
        } else {
          this.selectUsers = [row]
        }
      },
      handleClose(index) {
        this.selectUsers.splice(index, 1)
      },
      save() {
        this.$emit('callBackCopyData', this.selectUsers)
        this.dialogFormVisible = false
      },
      backDepart(list) {
        return list.map((item) => item.departName).join(',')
      },
      backRolelist(list) {
        return list.map((item) => item.roleName).join(',')
      },
      async fetchData() {
        this.queryForm.curPage = this.pageInfo.curPage
        this.queryForm.pageSize = this.pageInfo.pageSize
        this.listLoading = true
        const {
          result: { records, total },
        } = await getUserByPage(this.queryForm)
        this.list = records
        this.pageInfo.total = Number(total)
        this.listLoading = false
      },
      handleSizeChange(val) {
        this.pageInfo.pageSize = val
        this.pageInfo.curPage = 1
        this.fetchData()
      },
      handleCurrentChange(val) {
        this.pageInfo.curPage = val
        this.fetchData()
      },
      handleQuery() {
        this.pageInfo.curPage = 1
        // this.search = true
        this.fetchData()
      },
      resetForm(formName) {
        this.pageInfo.curPage = 1
        this.$refs[formName].resetFields()
        // this.search = false
        this.fetchData()
      },
    },
  }
</script>

<style>
  .el_tree .el-tree-node__content {
    line-height: 30px;
    height: 30px;
    font-size: 16px;
  }
</style>
