<template>
  <el-dialog
    v-drag
    :title="title"
    :visible.sync="isShowDialog"
    width="500px"
    center
    :close-on-click-modal="false"
    top="5vh"
    append-to-body
    v-loading="treeLoading"
  >
    <el-input v-model="filterText" clearable placeholder="输入关键字进行过滤" style="margin-bottom: 10px;" />
    <el-tree
      ref="departTree"
      style="max-height:600px;overflow:auto;"
      :filter-node-method="filterNode"
      :data="departList"
      node-key="id"
      check-strictly
      default-expand-all
      show-checkbox
      :props="defaultProps"
      @check-change="handleNodeClick"
      
    />
    <div slot="footer" class="dialog-footer">
      <el-button type="danger" @click="isShowDialog = false">
        关闭
      </el-button>
      <el-button type="primary" @click="confirmSelect" :disabled="treeLoading">
        确定
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import { formatEleDropDownTree, attachTopNode} from '@/utils/util.js'
import {mapGetters} from 'vuex'
export default {
  name: 'VabWorkFlowDepart',
  components: {
  },
  data() {
    return {
      title: '请选择启动流程的部门',
      isShowDialog: false,
      departData: [],
      isMultiSelect: false,
      isRoot: true,
      defaultProps: {
        children: 'children',
        label: 'departName'
      },
      checkedId: '',
      treeLoading:false,
      filterText: '',
    }
  },
  computed:{
    ...mapGetters({
      departList: 'acl/departList'
    }),
  },
  created() {
  },
  watch: {
    // 根据关键词过滤
    filterText(val) {
      this.$refs.departTree.filter(val)
    }
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true
      return data.departName.indexOf(value) !== -1
    },
    // 只能单选部门方法
    handleNodeClick(data, checked, node) {
      if (checked === true) {
        this.checkedId = data.id
        this.$refs.departTree.setCheckedKeys([data.id])
      } else {
        if (this.checkedId === data.id) {
          this.$refs.departTree.setCheckedKeys([data.id])
        }
      }
    },
   
    async showDialog() {
      
      this.isShowDialog = true
      this.$nextTick(()=>{
        this.checkedId=''
        this.$refs.departTree.setCheckedKeys([])
      })
    },
    async confirmSelect() {
      var selectDatas = null
      selectDatas = this.$refs.departTree.getCheckedNodes()
      if(selectDatas.length==0){
        this.$baseMessage('请选择要启动流程的部门！', 'error', 'vab-hey-message-error')
        return 
      }
      this.treeLoading=true
      await this.$parent.startWorkFlowReal(selectDatas[0])
      this.treeLoading=false
      this.isShowDialog = false
    }

  }
}
</script>
