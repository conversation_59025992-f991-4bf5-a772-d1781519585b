<template>
  <el-dialog v-drag title="流程启动信息" :visible.sync="isShowDialog" width="550px" center :close-on-click-modal="false"
    append-to-body @closed="closedDialog">
    <el-form :model="formData" :rules="rules" ref="formDataRef" label-width="120px">
      <template v-for="(item,index) in conditionList">
        <el-form-item :label="item.label" :prop="item.value" :key="index">
          <el-input :value="formDataName[item.value]" readonly placeholder="请选择">
            <!-- <el-button slot="append" icon="" @click.native="selectApprovalUser(item)" /> -->
            <el-button-group slot="append">
              <el-button icon="el-icon-close" @click="clearApprovalUser(item)"></el-button>
              <el-button icon="el-icon-search" @click.native="selectApprovalUser(item)"></el-button>
            </el-button-group>
          </el-input>
        </el-form-item>
      </template>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="danger" @click="isShowDialog = false">
        关闭
      </el-button>
      <el-button type="primary" @click="confirmStartFlow">
        确定
      </el-button>
    </div>
    <vab-user-list-dialog ref="userPopup" @callBackData="callBackData" />
  </el-dialog>
</template>
<script>
  import { mapGetters } from 'vuex'
  import { getDictList } from '@/api/system/dict-api'
  export default {
    name: 'VabStartFlowProcess',
    data() {
      return {
        isShowDialog: false,
        formData: {},
        formDataName: {},
        rules: {},
        callback: null,
        flowDefine: null,
        approvalConditionList: [], // 动态审批条件
        conditionList: [], // 当前流程审批条件
        approvalFieldName: '',
        isMultiSelect: false,
      }
    },
    computed: {},
    created() {
      this.getApprovalConditionList()
    },
    methods: {
      async showDialog(flowData, callback) {
        const { flowDefine, conditionList } = flowData
        this.flowDefine = flowDefine
        this.callback = callback
        const formData = {}
        const formDataName = {}
        const cdList = []
        const rules = {}
        conditionList.forEach(item => {
          const conditionInfo = this.approvalConditionList.find(cd => cd.value === item)
          const fieldName = this.getFieldsName(item)
          cdList.push({
            value: fieldName,
            label: conditionInfo.remark,
            isMultiSelect: conditionInfo.isMultiSelect,
          })
          formData[fieldName] = ''
          formDataName[fieldName] = ''
          if(!conditionInfo.isMultiSelect) {
            const rulesItem = [{
              required: true,
              message: '请选择' + conditionInfo.remark,
              trigger: 'change'
            }]
            rules[fieldName] = rulesItem
          }
        })
        this.conditionList = cdList
        this.formData = formData
        this.rules = rules
        this.formDataName = formDataName
        this.isShowDialog = true
        this.$nextTick(() => {
          this.$refs.formDataRef.clearValidate()
        })
      },
      closedDialog() {
        this.formData = {}
        this.formDataName = {}
        this.rules = {}
        this.$refs.formDataRef.resetFields()
      },
      confirmStartFlow() {
        this.$refs.formDataRef.validate(valid => {
          if(valid) {
            const data = {
              ...this.flowDefine,
              flowVariables: {
                ...this.formData
              }
            }
            this.callback(data)
            this.isShowDialog = false
          }
        })
      },
      // 选择审批人
      selectApprovalUser(cdInfo) {
        this.isMultiSelect = cdInfo.isMultiSelect
        this.approvalFieldName = cdInfo.value
        this.$refs.userPopup.showUserDialog({isMultiSelect: this.isMultiSelect})
      },
      clearApprovalUser(cdInfo) {
        this.formData[cdInfo.value] = ''
        this.formDataName[cdInfo.value] = ''
      },
      callBackData(data) {
        if(!this.isMultiSelect) {
          this.formData[this.approvalFieldName] = data[0].id
          this.formDataName[this.approvalFieldName] = data[0].userName
        } else {
          const idArr = data.map(item => item.id)
          const userNameArr = data.map(item => item.userName)
          this.formData[this.approvalFieldName] = idArr.join(',')
          this.formDataName[this.approvalFieldName] = userNameArr.join(',')
        }
      },
      getFieldsName(item) {
        if(!item) return ''
        const regex = /\{(.*?)\}/
        const matchData = item.match(regex)
        const fieldName = matchData[1]
        return fieldName
      },
      async getApprovalConditionList() {
        const res = await getDictList({dictCode: 'dynamicApproval,dynamicSend'})
        const data = [...res.result[0].children, ...res.result[1].children]
        let list = []
        data.reduce((dataArr, item) => {
          if(!dataArr.includes(item.id)) {
            dataArr.push(item.id)
            list.push({
              id: item.id,
              label: item.dictName,
              value: item.dictCode,
              remark: item.remark,
              isMultiSelect: item.parentDictCode === 'dynamicSend' ? true : false
            })
          }
          return dataArr
        }, [])
        this.approvalConditionList = list
      }
    }
  }
</script>
<style scoped lang="scss">
  ::v-deep .el-input-group__append  {
    width: 90px;
    padding: 0;
    .el-button-group {
      width: 100%;
      display: block;
    }
    .el-button {
      width: 45px;
      margin: 0;
    }
    .el-button:nth-child(1) {
      border-right: 1px solid #dcdfe6;
      margin-left: -1px !important;
    }
  }
</style>
