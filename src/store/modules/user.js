/**
 * @description 登录、获取用户信息、退出登录、清除token逻辑，不建议修改
 */
import Vue from 'vue'
import {getUserInfo, login, logout, socialLogin} from '@/api/user'
import {getToken, getUserAccount, removeToken, removeUserAccount, setToken, setUserAccount,} from '@/utils/token'
import {resetRouter} from '@/router'
import {isArray, isString} from '@/utils/validate'
import {title} from '@/config'
import {getUserAllDataPermission} from '@/api/system/permissions-api'
import {getDepartList} from '@/api/system/depart-api'
import {getUploadFilesByPage} from "@/api/system/uploadFile-api";

const state = () => ({
  token: getToken(),
  userName: '游客',
  userId: '',
  avatar:
    'https://gk.jxth.com.cn/group1/M01/00/03/dSm0yl_kO1WAGdw9AAAYQnxlas8298.png',
  userAccount: getUserAccount(),
  departList: [],
  departPermissionId: [],
  telephone: '',
})
const getters = {
  token: (state) => state.token,
  userName: (state) => state.userName,
  userId: (state) => state.userId,
  avatar: (state) => state.avatar,
  userAccount: (state) => state.userAccount,
  departList: (state) => state.departList,
  departPermissionId: (state) => state.departPermissionId,
  telephone: (state) => state.telephone,
}
const mutations = {
  /**
   * @description 设置token
   * @param {*} state
   * @param {*} token
   */
  setToken(state, token) {
    state.token = token
    setToken(token)
  },
  /**
   * @description 设置用户名
   * @param {*} state
   * @param {*} userName
   */
  setUsername(state, userName) {
    state.userName = userName
  },
  /**
   * @description 设置用户ID
   * @param {*} state
   * @param {*} userId
   */
  setUserid(state, userId) {
    state.userId = userId
  },
  /**
   * @description 设置头像
   * @param {*} state
   * @param {*} avatar
   */
  setAvatar(state, avatar) {
    state.avatar = avatar
  },
  /**
   * @description 设置账户
   * @param {*} state
   * @param {*} userAccount
   */
  setUseraccount(state, userAccount) {
    state.userAccount = userAccount
    setUserAccount(userAccount)
  },
  setDepart(state, departList) {
    state.departList = departList
  },
  setDepartPermissionId(state, departPermissionId) {
    state.departPermissionId = departPermissionId
  },
  setTelephone(state, telephone) {
    state.telephone = telephone
  },
}
const actions = {
  /**
   * @description 登录拦截放行时，设置虚拟角色
   * @param {*} { commit, dispatch }
   */
  setVirtualRoles({ commit, dispatch }) {
    dispatch('acl/setFull', true, { root: true })
    commit(
      'setAvatar',
      'https://gk.jxth.com.cn/group1/M01/00/03/dSm0yl_kO1WAGdw9AAAYQnxlas8298.png'
    )
    commit('setUsername', 'admin(未开启登录拦截)')
  },
  /**
   * @description 登录
   * @param {*} { commit }
   * @param {*} userInfo
   */
  async login({ commit }, userInfo) {
    await login({
      userAccount: userInfo.userName.trim(),
      userPwd: userInfo.password,
    })
      .then((response) => {
        commit('setUseraccount', userInfo.userName.trim())
        // commit('setToken', token)
        const hour = new Date().getHours()
        const thisTime =
          hour < 8
            ? '早上好'
            : hour <= 11
            ? '上午好'
            : hour <= 13
            ? '中午好'
            : hour < 18
            ? '下午好'
            : '晚上好'
        Vue.prototype.$baseNotify(`欢迎登录${title}`, `${thisTime}！`)
      })
      .catch((error) => {
        const err = error.msg
        Vue.prototype.$baseMessage(err, 'error', 'vab-hey-message-error')
        throw err
      })
  },
  /**
   * @description 微信授权绑定
   * @param {*} { commit }
   * @param {*} userInfo
   */
  async wxBindLogin({ commit }, userInfo) {
    await login({
      userAccount: userInfo.userName.trim(),
      userPwd: userInfo.password,
    })
      .then((response) => {
        commit('setUseraccount', userInfo.userName.trim())
      })
      .catch((error) => {
        const err = error.msg
        Vue.prototype.$baseMessage(err, 'error', 'vab-hey-message-error')
        throw err
      })
  },
  /**
   * @description 第三方登录
   * @param {*} {}
   * @param {*} tokenData
   */
  async socialLogin({ commit }, socialData) {
    const { result } = await socialLogin(socialData)
    if (result) {
      commit('setUseraccount', result)
      const hour = new Date().getHours()
      const thisTime =
        hour < 8
          ? '早上好'
          : hour <= 11
          ? '上午好'
          : hour <= 13
          ? '中午好'
          : hour < 18
          ? '下午好'
          : '晚上好'
      Vue.prototype.$baseNotify(`欢迎登录${title}`, `${thisTime}！`)
    } else {
      const err = `扫码登录异常`
      Vue.prototype.$baseMessage(err, 'error', 'vab-hey-message-error')
      throw err
    }
  },
  /**
   * @description 获取用户信息接口 这个接口非常非常重要，如果没有明确底层前逻辑禁止修改此方法，错误的修改可能造成整个框架无法正常使用
   * @param {*} { commit, dispatch, state }
   * @returns
   */
  async getUserInfo({ commit, dispatch, state }) {
    if (state.userAccount) {
      const {
        result: {
          id,
          userName,
          avatar,
          roleList,
          permissionsList,
          menuList,
          departList,
          telephone,
        },
      } = await getUserInfo({
        userAccount: state.userAccount,
      })
      const {
        result: { departPermissionId },
      } = await getUserAllDataPermission({
        id: id,
        roleList: roleList,
        departList: departList,
      })

      // 查询所有的部门列表信息
      const { result: allDepartList } = await getDepartList({})

      const imgParams = {
        bizId: id,
        bizCode: 'userAvatar',
        curPage: 1,
        pageSize: 10,
      }
      // 获取用户头像
      const { result: { records }} = await getUploadFilesByPage(imgParams)
      /**
       * 检验返回数据是否正常，无对应参数，将使用默认用户名,头像,Roles和Permissions
       * userName {String}
       * avatar {String}
       * roles {List}
       * ability {List}
       */
      if (
        (userName && !isString(userName)) ||
        (id && !isString(id)) ||
        (avatar && !isString(avatar)) ||
        (roleList && !isArray(roleList)) ||
        (departList && !isArray(departList)) ||
        (permissionsList && !isArray(permissionsList)) ||
        (menuList && !isArray(menuList)) ||
        (departList && !isArray(departList))
      ) {
        const err = 'getUserInfo核心接口异常，请检查返回JSON格式是否正确'
        Vue.prototype.$baseMessage(err, 'error', 'vab-hey-message-error')
        throw err
      } else {
        let roles = roleList.map((item) => item.roleCode)
        let permissions = permissionsList.map((item) => item.permissionsLable)
        // console.log(roles,permissions)
        if (menuList) dispatch('routes/setMenulist', menuList, { root: true })
        // 如不使用userName用户名,可删除以下代码
        if (userName) commit('setUsername', userName)
        // 如不使用userName用户ID,可删除以下代码
        if (id) commit('setUserid', id)
        // 如不使用avatar头像,可删除以下代码
        if (avatar) commit('setAvatar', avatar)
        // 如不使用roles权限控制,可删除以下代码
        if (roles) dispatch('acl/setRole', roles, { root: true })
        // 如不使用permissions权限控制,可删除以下代码
        if (permissions)
          dispatch('acl/setPermission', permissions, { root: true })
        //存用户部门信息
        if (departList)
          dispatch('acl/setDepartList', departList, { root: true })
        if (telephone) commit('setTelephone', telephone)

        if (departList) commit('setDepart', departList)
        if (departPermissionId)
          commit('setDepartPermissionId', departPermissionId)
        if (allDepartList)
          dispatch('acl/setAllDepartList', allDepartList, { root: true })
        if(records.length > 0) {
          commit(
            'setAvatar',
            records[0].urlPath
          )
        }

        dispatch('acl/setRolesInfo', roleList, { root: true })
      }
    } else {
      const error = '账户丢失，请重新登录！'
      Vue.prototype.$baseMessage(error, 'error', 'vab-hey-message-error')
      throw error
    }
  },
  /**
   * @description 退出登录
   * @param {*} { dispatch }
   */
  async logout({ dispatch }) {
    await logout()
    await dispatch('resetAll')
    const bc = new BroadcastChannel('quit_system')
    bc.postMessage('')
    bc.close()
  },
  /**
   * @description 重置token、roles、permission、router、tabsBar等
   * @param {*} { commit, dispatch }
   */
  async resetAll({ commit, dispatch }) {
    commit('setUsername', '游客')
    commit(
      'setAvatar',
      'https://gk.jxth.com.cn/group1/M01/00/03/dSm0yl_kO1WAGdw9AAAYQnxlas8298.png'
    )
    commit('routes/setRoutes', [], { root: true })
    await dispatch('setToken', '')
    await dispatch('acl/setFull', false, { root: true })
    await dispatch('acl/setRole', [], { root: true })
    await dispatch('acl/setPermission', [], { root: true })
    await dispatch('tabs/delAllVisitedRoutes', null, { root: true })
    await resetRouter()
    removeToken()
    removeUserAccount()
  },
  /**
   * @description 设置token
   * @param {*} { commit }
   * @param {*} token
   */
  setToken({ commit }, token) {
    commit('setToken', token)
  },
  /**
   * @description 设置头像
   * @param {*} { commit }
   * @param {*} avatar
   */
  setAvatar({ commit }, avatar) {
    commit('setAvatar', avatar)
  },
}
export default { state, getters, mutations, actions }
