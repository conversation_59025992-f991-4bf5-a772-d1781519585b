import request from '@/utils/request'

// 获取应用数据
export function getApplicationData(data) {
  return request({
    url: '/hnsz/workbenchMenu/getDataListByPage',
    method: 'post',
    data
  })
}

// 获取应用数据列表
export function getApplicationList(data) {
  return request({
    url: '/hnsz/workbenchMenu/getDataList',
    method: 'post',
    data
  })
}
// 新增/修改
export function saveApplicationData(data) {
  return request({
    url: '/hnsz/workbenchMenu/saveData',
    method: 'post',
    data
  })
}

// 删除
export function delApplicationData(data) {
  return request({
    url: '/hnsz/workbenchMenu/deleteData',
    method: 'post',
    data
  })
}
