import request from '@/utils/request'

export function getPermissionById(id, curPage, pageSize) {
  return request({
    url: '/base/permission/getPermissionById/' + id + '/' + curPage + '/' + pageSize,
    method: 'get'
  })
}

export function delPermissionById(id) {
  return request({
    url: '/base/permission/delPermissionById/' + id,
    method: 'get'
  })
}

export function savePermission(data) {
  return request({
    url: '/base/permission/savePermission',
    method: 'post',
    data
  })
}

export function delBatchPermission(ids) {
  return request({
    url: '/base/permission/delBatchPermission/' + ids,
    method: 'get'
  })
}

