import request from '@/utils/request'

export function getUserList(data) {
  return request({
    url: '/base/user/getDataList',
    method: 'post',
    data
  })
}

export function getUserByPage(data) {
  return request({
    url: '/base/user/getDataListByPage',
    method: 'post',
    data
  })
}

export function getUser(data) {
  return request({
    url: '/base/user/getData',
    method: 'post',
    data
  })
}

export function deleteUser(data) {
  return request({
    url: '/base/user/deleteData',
    method: 'post',
    data
  })
}

export function saveUser(data) {
  return request({
    url: '/base/user/saveData',
    method: 'post',
    data
  })
}

export function checkUser(data) {
  return request({
    url: '/base/user/checkData',
    method: 'post',
    data
  })
}
