import request from '@/utils/request'

//查询定时任务List getDataList
export function getDataList(data) {
  return request({
    url: '/base/quartzJob/getDataList',
    method: 'post',
    data,
  })
}
//定时任务日志分页查询getDataListByPageLog
export function getDataListByPageLog(data) {
  return request({
    url: '/base/quartzJobLog/getDataListByPage',
    method: 'post',
    data,
  })
}
//定时任务日志清单List getDataListLog
export function getDataListLog(data) {
  return request({
    url: '/base/quartzJobLog/getDataList',
    method: 'post',
    data,
  })
}

//分页查询定时任务  getDataListByPage
export function getDataListByPage(data) {
  return request({
    url: '/base/quartzJob/getDataListByPage',
    method: 'post',
    data,
  })
}
// 启动定时任务 startQuartzJob
export function startQuartzJob(data) {
  return request({
    url: '/base/quartzJob/startQuartzJob',
    method: 'post',
    data,
  })
}

// 启动全部定时任务 startAllQuartzJob
export function startAllQuartzJob(data) {
  return request({
    url: '/base/quartzJob/startAllQuartzJob',
    method: 'post',
    data,
  })
}

// 删除定时任务 deleteQuartzJob
export function deleteQuartzJob(data) {
  return request({
    url: '/base/quartzJob/deleteQuartzJob',
    method: 'post',
    data,
  })
}
//删除定时任务日志 deleteDataLog
export function deleteDataLog(data) {
  return request({
    url: '/base/quartzJobLog/deleteData',
    method: 'post',
    data,
  })
}

//新增/修改定时任务 saveQuartzJob
export function saveQuartzJob(data) {
  return request({
    url: '/base/quartzJob/saveQuartzJob',
    method: 'post',
    data,
  })
}

// 新增/修改定时任务日志 saveQuartzJobLog
export function saveQuartzJobLog(data) {
  return request({
    url: '/base/quartzJobLog/saveData',
    method: 'post',
    data,
  })
}

// 暂停定时任务 stopQuartzJob
export function stopQuartzJob(data) {
  return request({
    url: '/base/quartzJob/stopQuartzJob',
    method: 'post',
    data,
  })
}
// 暂停全部定时任务 stopAllQuartzJob
export function stopAllQuartzJob(data) {
  return request({
    url: '/base/quartzJob/stopAllQuartzJob',
    method: 'post',
    data,
  })
}
