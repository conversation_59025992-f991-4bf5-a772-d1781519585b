import request from '@/utils/request'

export function uploadFile(data) {
  return request({
    url: '/base/upload/multiUpload',
    method: 'post',
    data,
  })
}

export function getUploadFilesByPage(data) {
  return request({
    url: '/base/uploadFile/getDataListByPage',
    method: 'post',
    data,
  })
}

export function getUploadFileList(data) {
  return request({
    url: '/base/uploadFile/getDataList',
    method: 'post',
    data,
  })
}

/**
 * 删除文件
 * @param id
 */
export function deleteFile(id) {
  return request({
    url: '/base/uploadFile/deleteData',
    method: 'post',
    data: { id },
  })
}

export function saveFile(data) {
  return request({
    url: '/base/upload/saveFile',
    method: 'post',
    data,
  })
}

export function deleteFileBatch(data) {
  return request({
    url: '/base/upload/deleteFileBatch',
    method: 'post',
    data,
  })
}

/**
 * 下载文件
 * @param id
 */
export function downLoadFileMinio(id) {
  return request({
    url: '/base/uploadFile/downLoadFileMinio',
    method: 'post',
    responseType: 'blob',
    data: { id },
  })
}

/**
 * 下载文件
 * @param id
 */
export function downloadFile(id) {
  return request({
    url: '/base/uploadFile/downLoadFile',
    method: 'post',
    responseType: 'blob',
    data: { id },
  })
}

export function getUploadFilesByProjectPage(curPage, pageSize, data) {
  return request({
    url:
      '/proxy/uploadRel/getUploadFilesByProjectPage/' +
      curPage +
      '/' +
      pageSize,
    method: 'post',
    data,
  })
}

export function multiUploadProject(data) {
  return request({
    url: '/proxy/uploadRel/multiUploadProject',
    method: 'post',
    data,
  })
}

export function findMaxCount() {
  return request({
    url: '/proxy/newupload/findMaxCount',
    method: 'post',
  })
}

export function newUploadFile(data) {
  return request({
    url: '/proxy/newupload/multiUpload',
    method: 'post',
    data,
  })
}

export function delUploadRelById(id) {
  return request({
    url: '/proxy/uploadRel/delUploadRelById/' + id,
    method: 'get',
  })
}

/**
 * 批量下载文件
 * @param id
 */
export function batchDownloadFile(ids) {
  return request({
    url: '/proxy/file/batchDownloadFile/' + ids,
    method: 'post',
    responseType: 'blob',
  })
}

export function saveSysUpload(data) {
  return request({
    url: '/base/uploadFile/saveUploadFile',
    method: 'post',
    data,
  })
}

export function multiUpload(data) {
  return request({
    url: '/base/uploadFile/multiUpload',
    method: 'post',
    data,
  })
}

export function uploadMinio(data) {
  return request({
    url: '/base/uploadFile/uploadMinio',
    method: 'post',
    data,
  })
}

/**
 * 删除文件
 * @param id
 */
export function deleteFileByBizId(data) {
  return request({
    url: '/base/uploadFile/deleteData',
    method: 'post',
    data,
  })
}
