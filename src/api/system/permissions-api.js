import request from '@/utils/request'

export function getPermissionsList(data) {
  return request({
    url: '/base/permissions/getDataList',
    method: 'post',
    data
  })
}

export function getPermissionsByPage(data) {
  return request({
    url: '/base/permissions/getDataListByPage',
    method: 'post',
    data
  })
}

export function getPermissions(data) {
  return request({
    url: '/base/permissions/getData',
    method: 'post',
    data
  })
}

export function deletePermissions(data) {
  return request({
    url: '/base/permissions/deleteData',
    method: 'post',
    data
  })
}

export function savePermissions(data) {
  return request({
    url: '/base/permissions/saveData',
    method: 'post',
    data
  })
}

export function checkPermissions(data) {
  return request({
    url: '/base/permissions/checkData',
    method: 'post',
    data
  })
}

/**
 * 保存数据权限
 * @param data
 * @returns {*}
 */
export function saveDataPermission(data) {
  return request({
    url: '/hnsz/userDataPermission/saveData',
    method: 'post',
    data
  })
}
/**
 * 获取当前用户\角色\部门的数据权限
 * @param data
 * @returns {*}
 */
export function getDataPermission(data) {
  return request({
    url: '/hnsz/userDataPermission/getData',
    method: 'post',
    data
  })
}

export function getDataPermissionList(data) {
  return request({
    url: '/hnsz/userDataPermission/getDataList',
    method: 'post',
    data
  })
}
/**
 * 获取当前用户所有数据权限
 * @param data
 * @returns {*}
 */
export function getUserAllDataPermission(data) {
  return request({
    url: '/hnsz/userDataPermission/getUserAllDataPermission',
    method: 'post',
    data
  })
}
