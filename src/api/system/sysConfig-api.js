import request from '@/utils/request'

export function getSysConfig(data) {
  return request({
    url: '/base/sysConfig/getData',
    method: 'post',
    data
  })
}

export function getSysConfigList(data) {
  return request({
    url: '/base/sysConfig/getDataList',
    method: 'post',
    data
  })
}

export function saveSysConfig(data) {
  return request({
    url: '/base/sysConfig/saveData',
    method: 'post',
    data
  })
}

export function deleteSysConfig(data) {
  return request({
    url: '/base/sysConfig/deleteData',
    method: 'post',
    data
  })
}

export function deleteSysConfigBatch(data) {
  return request({
    url: '/base/sysConfig/deleteDataBatch',
    method: 'post',
    data
  })
}

