import request from '@/utils/request'

export function getNoticesByPage(data) {
  return request({
    url: '/proxy/notice/getNoticesByPage',
    method: 'post',
    data
  })
}

export function saveNotice(data) {
  return request({
    url: '/proxy/notice/saveNotice',
    method: 'post',
    data
  })
}

export function deleteNotice(data) {
  return request({
    url: '/proxy/notice/deleteNotice',
    method: 'post',
    data
  })
}

export function batchDeleteNotices(data) {
  return request({
    url: '/proxy/notice/batchDeleteNotices',
    method: 'post',
    data
  })
}
