import request from '@/utils/request'

export function getData(data) {
  return request({
    url: '/hnsz/staffApplication/getData',
    method: 'post',
    data,
  })
}

export function getDataList(data) {
  return request({
    url: '/hnsz/staffApplication/getDataList',
    method: 'post',
    data,
  })
}

export function deleteData(data) {
  return request({
    url: '/hnsz/staffApplication/deleteData',
    method: 'post',
    data,
  })
}

export function saveData(data) {
  return request({
    url: '/hnsz/staffApplication/saveData',
    method: 'post',
    data,
  })
}

export function getDataListByPage(data) {
  return request({
    url: '/hnsz/staffApplication/getDataListByPage',
    method: 'post',
    data,
  })
}

// 导出数据
export function exportData(data) {
  return request({
    url: '/hnsz/staffApplication/exportData',
    method: 'post',
    responseType: 'blob',
    data
  })
}
