import request from '@/utils/request'

// 获取日志列表
export function getMeetingListData(data) {
  return request({
    url: '/hnsz/meetingMinutes/getDataListByPage',
    method: 'post',
    data
  })
}

// 获取日志list
export function getMeetingList(data) {
  return request({
    url: '/hnsz/meetingMinutes/getDataList',
    method: 'post',
    data
  })
}

// 删除日志
export function deleteMeetingById(data) {
  return request({
    url: '/hnsz/meetingMinutes/deleteData',
    method: 'post',
    data
  })
}

// 新增/修改日志数据
export function saveMeetingData(data) {
  return request({
    url: '/hnsz/meetingMinutes/saveData',
    method: 'post',
    data
  })
}

// 日志导出
export function exportMeetingData(data) {
  return request({
    url: '/hnsz/meetingMinutesApproval/exportData',
    method: 'post',
    responseType: 'blob',
    data
  })
}

// 修改日志已读状态
export function updateMeetingIfRead(data) {
  return request({
    url: '/hnsz/meetingMinutes/updateIfRead',
    method: 'post',
    data
  })
}

// 获取单条日志详情数据
export function getMeetingData(data) {
  return request({
    url: '/hnsz/meetingMinutes/getData',
    method: 'post',
    data
  })
}


export function getAprovalDataList(data) {
  return request({
    url: '/hnsz/meetingMinutes/getAprovalDataList',
    method: 'post',
    data
  })
}
