import request from '@/utils/request'

/**
 * 调岗申请 查询单条
 * @param data
 */
export function getDataJobTransferApplication(data) {
  return request({
    url: '/hnsz/transferPosition/getData',
    method: 'post',
    data,
  })
}

/**
 * 调岗申请 查询list
 * @param data
 */
export function getDataListJobTransferApplication(data) {
  return request({
    url: '/hnsz/transferPosition/getDataList',
    method: 'post',
    data,
  })
}

/**
 * 人员 查询list
 * @param data
 */
export function getEmployeeDataList(data) {
  return request({
    url: '/hnsz/employeeBase/getDataList',
    method: 'post',
    data,
  })
}

/**
 * 调岗申请 删除
 * @param data
 */
export function deleteDataJobTransferApplication(data) {
  return request({
    url: '/hnsz/transferPosition/deleteData',
    method: 'post',
    data,
  })
}

/**
 * 调岗申请 新增/修改
 * @param data
 */
export function saveDataJobTransferApplication(data) {
  return request({
    url: '/hnsz/transferPosition/saveData',
    method: 'post',
    data,
  })
}

/**
 * 调岗申请 查询分页
 * @param data
 */
export function getDataListByPageJobTransferApplication(data) {
  return request({
    url: '/hnsz/transferPosition/getDataListByPage',
    method: 'post',
    data,
  })
}
