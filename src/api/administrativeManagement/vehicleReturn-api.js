import request from '@/utils/request'

/**
 * 请假申请 查询单条
 * @param data
 */
export function getData(data) {
  return request({
    url: '/hnsz/vehicleReturn/getData',
    method: 'post',
    data,
  })
}

/**
 * 请假申请 查询list
 * @param data
 */
export function getDataList(data) {
  return request({
    url: '/hnsz/vehicleReturn/getDataList',
    method: 'post',
    data,
  })
}

/**
 * 请假申请 删除
 * @param data
 */
export function deleteData(data) {
  return request({
    url: '/hnsz/vehicleReturn/deleteData',
    method: 'post',
    data,
  })
}

/**
 * 请假申请 新增/修改
 * @param data
 */
export function saveData(data) {
  return request({
    url: '/hnsz/vehicleReturn/saveData',
    method: 'post',
    data,
  })
}

/**
 * 请假申请 查询分页
 * @param data
 */
export function getDataListByPage(data) {
  return request({
    url: '/hnsz/vehicleReturn/getDataListByPage',
    method: 'post',
    data,
  })
}

// 导出
export function exportData(data) {
  return request({
    url: '/hnsz/vehicleReturn/exportData',
    method: 'post',
    responseType: 'blob',
    data,
  })
}
