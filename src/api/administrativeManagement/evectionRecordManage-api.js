import request from '@/utils/request'

// 获取日志列表
export function getReportListData(data) {
  return request({
    url: '/hnsz/evectionRecord/getDataListByPage',
    method: 'post',
    data,
  })
}

// 获取日志list
export function getReportList(data) {
  return request({
    url: '/hnsz/evectionRecord/getDataList',
    method: 'post',
    data,
  })
}

// 删除日志
export function deleteReportById(data) {
  return request({
    url: '/hnsz/evectionRecord/deleteData',
    method: 'post',
    data,
  })
}

// 新增/修改日志数据
export function saveReportData(data) {
  return request({
    url: '/hnsz/evectionRecord/saveData',
    method: 'post',
    data,
  })
}

// 日志导出
export function exportReportData(data) {
  return request({
    url: '/hnsz/evectionRecordApproval/exportData',
    method: 'post',
    responseType: 'blob',
    data,
  })
}

// 修改日志已读状态
export function updateReportIfRead(data) {
  return request({
    url: '/hnsz/evectionRecord/updateIfRead',
    method: 'post',
    data,
  })
}

// 获取单条日志详情数据
export function getReportData(data) {
  return request({
    url: '/hnsz/evectionRecord/getData',
    method: 'post',
    data,
  })
}

export function getAprovalDataList(data) {
  return request({
    url: '/hnsz/evectionRecord/getAprovalDataList',
    method: 'post',
    data,
  })
}
