import request from '@/utils/request'
// 分页查询
export function getDataListByPage(data) {
  return request({
    url: '/hnsz/punchInPersonRecordSupplement/getDataListByPage',
    method: 'post',
    data
  })
}

// 获取列表
export function getDataList(data) {
  return request({
    url: '/hnsz/punchInPersonRecordSupplement/getDataList',
    method: 'post',
    data
  })
}





// 新增修改
export function saveData(data) {
  return request({
    url: '/hnsz/punchInPersonRecordSupplement/saveData',
    method: 'post',
    data
  })
}

// 删除
export function deleteData(data) {
  return request({
    url: '/hnsz/punchInPersonRecordSupplement/deleteData',
    method: 'post',
    data
  })
}

// 导出
export function exportData(data) {
  return request({
    url: '/hnsz/punchInPersonRecordSupplement/exportData',
    method: 'post',
    responseType: 'blob',
    data,
  })
}

//是否打过卡
export function selectOnePunchInPersonRecord(data) {
  return request({
    url: '/hnsz/punchInPersonRecordSupplement/selectOnePunchInPersonRecord',
    method: 'post',
    data
  })
}
