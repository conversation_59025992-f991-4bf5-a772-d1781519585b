import request from '@/utils/request'
export function getDataListByPage(data) {
  return request({
    url: '/hnsz/visitsRecordsApproval/getDataListByPage',
    method: 'post',
    data,
  })
}

export function getDataList(data) {
  return request({
    url: '/hnsz/visitsRecordsApproval/getDataList',
    method: 'post',
    data,
  })
}

export function getData(data) {
  return request({
    url: '/hnsz/visitsRecordsApproval/getData',
    method: 'post',
    data,
  })
}

export function saveData(data) {
  return request({
    url: '/hnsz/visitsRecordsApproval/saveData',
    method: 'post',
    data,
  })
}

export function deleteData(data) {
  return request({
    url: '/hnsz/visitsRecordsApproval/deleteData',
    method: 'post',
    data,
  })
}

export function checkData(data) {
  return request({
    url: '/hnsz/visitsRecordsApproval/checkData',
    method: 'post',
    data,
  })
}

export function exportData(data) {
  return request({
    url: '/hnsz/visitsRecordsApproval/exportData',
    method: 'post',
    data,
  })
}

export function getLatestApproval(data) {
  return request({
    url: '/hnsz/visitsRecordsApproval/getLatestApproval',
    method: 'post',
    data
  })
}
