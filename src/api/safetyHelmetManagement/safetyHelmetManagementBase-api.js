import request from '@/utils/request'

export function getDataListByPage(data) {
  return request({
    url: '/hnsz/safetyHelmetManagement/getDataListByPage',
    method: 'post',
    data,
  })
}

// export function registerSipUser(data) {
//   return request({
//     url: '/hnsz/safetyHelmetManagement/registerSipUser',
//     method: 'post',
//     data,
//   })
// }


export function getSafetyHelmetImageListByPage(data) {
  return request({
    url: '/hnsz/safetyHelmetManagement/getSafetyHelmetImageListByPage',
    method: 'post',
    data,
  })
}
export function getSafetyHelmetDataList(data) {
  return request({
    url: '/hnsz/safetyHelmetManagement/getSafetyHelmetDataList',
    method: 'post',
    data,
  })
}
export function joinMeeting(data) {
  return request({
    url: '/hnsz/safetyHelmetManagement/joinMeeting',
    method: 'post',
    data,
  })
}
export function getSafetyHelmetTwoDataList(data) {
  return request({
    url: '/hnsz/safetyHelmetManagement/getSafetyHelmetTwoDataList',
    method: 'post',
    data,
  })
}
//查询所有帽子当前实时数据集合
export function getSafetyHelmetDataGroupAllList(data) {
  return request({
    url: '/hnsz/safetyHelmetManagement/getSafetyHelmetDataGroupAllList',
    method: 'post',
    data,
  })
}
export function serverPushGetLocalVideoList(data) {
  return request({
    url: '/hnsz/safetyHelmetManagement/serverPushGetLocalVideoList',
    method: 'post',
    data,
  })
}
export function pushToClient(data) {
  return request({
    url: '/hnsz/safetyHelmetManagement/pushToClient',
    method: 'post',
    data,
  })
}
export function serverPushUploadVideo(data) {
  return request({
    url: '/hnsz/safetyHelmetManagement/serverPushUploadVideo',
    method: 'post',
    data,
  })
}
export function getDataList(data) {
  return request({
    url: '/hnsz/safetyHelmetManagement/getDataList',
    method: 'post',
    data,
  })
}
export function deleteSafetyHelmetImage(data) {
  return request({
    url: '/hnsz/safetyHelmetManagement/deleteSafetyHelmetImage',
    method: 'post',
    data,
  })
}

export function deleteData(data) {
  return request({
    url: '/hnsz/safetyHelmetManagement/deleteData',
    method: 'post',
    data,
  })
}

export function saveData(data) {
  return request({
    url: '/hnsz/safetyHelmetManagement/saveData',
    method: 'post',
    data,
  })
}

export function changeStatus(data) {
  return request({
    url: '/hnsz/safetyHelmetManagement/changeStatus',
    method: 'post',
    data,
  })
}

/**
 * 导出
 * @param data
 * @returns {*}
 */
export function exportData(data) {
  return request({
    url: '/hnsz/safetyHelmetManagement/exportData',
    method: 'post',
    responseType: 'blob',
    data,
  })
}
