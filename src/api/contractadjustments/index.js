import request from '@/utils/request'

//获取合同列表
export function getContracPageData(data) {
  return request({
    url: '/hnsz/contractAdjust/getDataListByPage',
    method: 'post',
    data,
  })
}

// 获取合同list
export function getContracList(data) {
  return request({
    url: '/hnsz/contractAdjust/getDataList',
    method: 'post',
    data,
  })
}

// 获取合同执行列表
export function getExecutionPageData(data) {
  return request({
    url: '/hnsz/contractadjustExecution/getDataListByPage',
    method: 'post',
    data,
  })
}

// 获取合同执行list
export function getExecutionList(data) {
  return request({
    url: '/hnsz/contractadjustExecution/getDataList',
    method: 'post',
    data,
  })
}

// 查询合同金额统计
export function getSumExecutionData(data) {
  return request({
    url: '/hnsz/contractadjustExecution/getTotalAmount',
    method: 'post',
    data,
  })
}

// 新增修改合同
export function saveContractData(data) {
  return request({
    url: '/hnsz/contractAdjust/saveData',
    method: 'post',
    data,
  })
}

// 判断收入合同是否唯一
export function checkSRSave(data) {
  return request({
    url: '/hnsz/contractAdjust/checkSRSave',
    method: 'post',
    data,
  })
}

//获取项目关联的预算，没有返回400
export function checkBudgetSave(data) {
  return request({
    url: '/hnsz/contractAdjust/checkSRSave',
    method: 'post',
    data,
  })
}

//获取单个明细金额合计 id:合同id;costId:费用项目id
export function getSumMoney(data) {
  return request({
    url: '/hnsz/contractAdjust/getSumMoney',
    method: 'post',
    data,
  })
}

//获取合同和明细 id:合同id
export function getDataAndDetail(data) {
  return request({
    url: '/hnsz/contractAdjust/getDataAndDetail',
    method: 'post',
    data,
  })
}

//删除合同
export function deleteContract(data) {
  return request({
    url: '/hnsz/contractAdjust/deleteData',
    method: 'post',
    data,
  })
}

// 导出合同
export function exportData(data) {
  return request({
    url: '/hnsz/contractAdjust/exportData',
    method: 'post',
    responseType: 'blob',
    data,
  })
}

// 导出合同
export function exportContractData(data) {
  return request({
    url: '/hnsz/contractadjustExecution/exportData',
    method: 'post',
    responseType: 'blob',
    data,
  })
}

export function getDataListByPagePurchaseApplication(data) {
  return request({
    url: '/hnsz/purchaseApplications/getDataListByPage',
    method: 'post',
    data,
  })
}

// 校验合同名称是否重复
export function checkData(data) {
  return request({
    url: '/hnsz/contractAdjust/checkData',
    method: 'post',
    data,
  })
}
