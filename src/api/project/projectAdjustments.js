import request from '@/utils/request'

export function getDataListByPage(data) {
  return request({
    url: '/hnsz/projectAdjustments/getDataListByPage',
    method: 'post',
    data,
  })
}

export function getDataList(data) {
  return request({
    url: '/hnsz/projectAdjustments/getDataList',
    method: 'post',
    data,
  })
}

export function saveData(data) {
  return request({
    url: '/hnsz/projectAdjustments/saveData',
    method: 'post',
    data,
  })
}

export function deleteData(data) {
  return request({
    url: '/hnsz/projectAdjustments/deleteData',
    method: 'post',
    data,
  })
}

export function exportData(data) {
  return request({
    url: '/hnsz/projectAdjustments/exportData',
    method: 'post',
    responseType: 'blob',
    data,
  })
}

export function checkData(data) {
  return request({
    url: '/hnsz/projectAdjustments/checkData',
    method: 'post',
    data,
  })
}


