import request from '@/utils/request'

export function getDataListByPage(data) {
  return request({
    url: '/hnsz/safetySuperviseLog/getDataListByPage',
    method: 'post',
    data,
  })
}

export function getDataList(data) {
  return request({
    url: '/hnsz/safetySuperviseLog/getDataList',
    method: 'post',
    data,
  })
}

export function deleteData(data) {
  return request({
    url: '/hnsz/safetySuperviseLog/deleteData',
    method: 'post',
    data,
  })
}

export function saveData(data) {
  return request({
    url: '/hnsz/safetySuperviseLog/saveData',
    method: 'post',
    data,
  })
}

export function changeStatus(data) {
  return request({
    url: '/hnsz/safetySuperviseLog/changeStatus',
    method: 'post',
    data,
  })
}

/**
 * 导出
 * @param data
 * @returns {*}
 */
export function exportData(data) {
  return request({
    url: '/hnsz/safetySuperviseLog/exportData',
    method: 'post',
    responseType: 'blob',
    data,
  })
}
