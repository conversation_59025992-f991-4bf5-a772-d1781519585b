import request from '@/utils/request'

export function getDataListByPage(data) {
  return request({
    url: '/hnsz/superviseMonthly/getDataListByPage',
    method: 'post',
    data,
  })
}

export function getDataList(data) {
  return request({
    url: '/hnsz/superviseMonthly/getDataList',
    method: 'post',
    data,
  })
}

export function deleteData(data) {
  return request({
    url: '/hnsz/superviseMonthly/deleteData',
    method: 'post',
    data,
  })
}

export function saveData(data) {
  return request({
    url: '/hnsz/superviseMonthly/saveData',
    method: 'post',
    data,
  })
}

export function changeStatus(data) {
  return request({
    url: '/hnsz/superviseMonthly/changeStatus',
    method: 'post',
    data,
  })
}

/**
 * 导出
 * @param data
 * @returns {*}
 */
export function exportData(data) {
  return request({
    url: '/hnsz/superviseMonthly/exportData',
    method: 'post',
    responseType: 'blob',
    data,
  })
}
