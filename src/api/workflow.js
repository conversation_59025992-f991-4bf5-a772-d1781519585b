import request from '@/utils/request'

export function getList(params) {
  return request({
    url: '/workflow/getList',
    method: 'get',
    params,
  })
}

export function doEdit(data) {
  return request({
    url: '/workflow/doEdit',
    method: 'post',
    data,
  })
}

// 获取流程数据
export function getFlowData(data) {
  return request({
    url: '/base/bizMenuFlowRel/getMenuFlowRelData',
    method: 'post',
    data,
  })
}

// 获取流程定义信息
export function getFlowDefInfo(data) {
  return request({
    url: '/base/bizMenuFlowRel/getWorkFlowDefInfoByMenuCodeAndModuleId',
    method: 'post',
    data,
  })
}

// 保存导入的流程数据
export function saveUploadFlowData(data) {
  return request({
    url: '/base/workFlowSubsystemRel/saveMenuFlowCopyRel',
    method: 'post',
    data,
  })
}

//-新增/修改 菜单关联流程
export function saveMenuFlowRel(data) {
  return request({
    url: '/base/bizMenuFlowRel/saveData',
    method: 'post',
    data
  })
}

//根据菜单和标段权限查找流程关联数据 getBizMenuFlowRel
export function getBizMenuFlowRel(data) {
  return request({
    url: '/base/bizMenuFlowRel/getMenuFlowRelData',
    method: 'post',
    data
  })
}
