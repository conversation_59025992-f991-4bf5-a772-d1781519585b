<template>
  <el-dialog v-drag append-to-body :title="title" :visible.sync="dialogFormVisible" width="1200px"
    :close-on-click-modal="false" :before-close="close" top="2vh">
    <el-row :gutter="20">
      <el-col :span="6">
        <div>
          <el-tree ref="departTree" v-loading="treeLoading" node-key="id" :data="treeData" :props="defaultProps"
            highlight-current default-expand-all :expand-on-click-node="false" @node-click="departTreeClick">
            <span slot-scope="{node,data}" class="custom-tree-node">
              <span v-if="data.children.length == 0" :title="data.label" class="show-ellipsis"><i
                  class="el-icon-user" />{{ data.label }}</span>
              <span v-else-if="data.children.length !== 0" :title="data.label" class="show-ellipsis"><vab-icon-mix
                  icon="organization" /> {{ data.label }}</span>
            </span>
          </el-tree>
        </div>
      </el-col>
      <el-col :span="18">
        <vab-query-form>
          <vab-query-form-top-panel>
            <el-form ref="form" :inline="true" label-width="80px" :model="queryForm" @submit.native.prevent>
              <el-form-item label="用户姓名" prop="userName">
                <el-input v-model="queryForm.userName" placeholder="请输入用户姓名" />
              </el-form-item>
              <!-- <el-form-item label="用户账号" prop="userAccount">
            <el-input
              v-model="queryForm.userAccount"
              placeholder="请输入用户账号"
            />
          </el-form-item>
          <el-form-item label="手机号" prop="telephone">
            <el-input
              v-model="queryForm.telephone"
              placeholder="请输入手机号"
            />
          </el-form-item> -->

              <el-form-item>
                <el-button icon="el-icon-search" native-type="submit" type="primary" @click="handleQuery">
                  查询
                </el-button>
                <el-button icon="el-icon-refresh-right" @click.native="resetForm('form')">
                  重置
                </el-button>
              </el-form-item>
            </el-form>
          </vab-query-form-top-panel>
        </vab-query-form>
        <el-table ref="tableDataRef" v-loading="listLoading" border row-key="id" :height="450" :data="list" stripe
          @row-click="handleRowClick" @selection-change="selectionChange" @select="handleRowSelect">
          <el-table-column type="selection" label="选择" width="55" align="center" />
          <el-table-column v-for="(item, index) in columns" :key="index" :align="item.center ? item.center : 'center'"
            :label="item.label" :prop="item.prop" :sortable="item.sortable ? item.sortable : false"
            :width="item.width ? item.width : 'auto'">
            <template #default="{ row }">
              <span v-if="item.label === '所在部门'">
                {{ backDepart(row.departList) }}
              </span>
              <span v-else-if="item.label === '角色'">
                {{ backRolelist(row.roleList) }}
              </span>
              <span v-else>{{ row[item.prop] }}</span>
            </template>
          </el-table-column>
          <template #empty>
            <el-image class="vab-data-empty" :src="require('@/assets/empty_images/data_empty.png')" />
          </template>
        </el-table>
      </el-col>
    </el-row>
    <el-pagination background :current-page="pageInfo.curPage" :layout="layout" :page-sizes="[5, 10, 20]"
      :page-size="pageInfo.pageSize" :total="pageInfo.total" @current-change="handleCurrentChange"
      @size-change="handleSizeChange" />
    <div class="select-content" v-if="isMulti">
      <div class="select-title">当前已选择:</div>
      <div class="select-list">
        <el-tag v-for="item in selectUserData" :key="item.userId" closable
          @close="closeTag(item)">{{item.userName}}</el-tag>
      </div>
    </div>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
  import {
    getUserByPage
  } from '@/api/system/user-api'
  import {
    getDepartList
  } from '@/api/system/depart-api'
  import {
    formatEleDepartTree,
    attachTopNode
  } from '@/utils/util.js'
  export default {
    props: {
      isMulti: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        title: '选择用户',
        listLoading: false,
        treeLoading: false,
        treeData: [],
        defaultProps: {
          children: 'children',
          label: 'label'
        },
        dialogFormVisible: false,
        queryForm: {
          userAccount: '',
          userName: '',
          telephone: '',
          endTime: '',
          departCode: ''
        },
        layout: 'total, sizes, prev, pager, next, jumper',
        pageInfo: {
          curPage: 1,
          pageSize: 10,
          total: 0
        },
        selectRows: [],
        columns: [{
          label: '用户姓名',
          prop: 'userName',
        },
        // {
        //   label: '用户账号',
        //   prop: 'userAccount',
        // },
        // {
        //   label: '性别',
        //   prop: 'genderName',
        // },
        // {
        //   label: '手机号',
        //   prop: 'telephone',
        // },
        // {
        //   label: '邮箱',
        //   prop: 'email',
        // },
        // {
        //   label: '角色',
        //   prop: 'roleList',
        // },
        {
          label: '所在部门',
          prop: 'departList'
        },
        ],
        list: [],
        selectIds: [],
        selectUserData: []
      }
    },
    computed: {

    },
    created() {
      this.initTree()
    },
    methods: {
      async initTree() {
        this.treeLoading = true
        const {
          result
        } = await getDepartList({})
        this.treeData = formatEleDepartTree(result, '-1')
        // this.treeData = attachTopNode(this.treeData, '根机构', )
        this.treeLoading = false
      },
      departTreeClick(data) {
        this.queryForm.departCode = data.departCode
        this.fetchData()
      },
      async showUserDialog({
        selUserList
      }) {
        this.listLoading = true
        if (selUserList.length) {
          this.selectUserData = selUserList
        }
        this.dialogFormVisible = true
        await this.fetchData()
      },
      // 表格选择发生变化
      selectionChange(val) {
        if (!this.isMulti) {
          if (val.length > 1) {
            this.$refs.tableDataRef.clearSelection()
            this.$refs.tableDataRef.toggleRowSelection(val.pop())
          } else if (val.length === 1) {
            const rowData = val.pop()
            this.selectRows = []
            this.selectUserData = []
            if (!!rowData) {
              this.selectRows.push(rowData)
              this.selectUserData.push({
                userId: rowData.id,
                userName: rowData.userName,
                telephone: rowData.telephone
              })
            }
          }
        }
      },
      // 点击表格列
      handleRowClick(row) {
        if (this.isMulti) {
          const selIdx = this.selectRows.findIndex(item => item.id === row.id)
          if (selIdx < 0) {
            // 复选
            this.selectRows.push(row)
            // 判断已选的数据里有无当前操作列数据
            const flag = this.selectUserData.some(item => item.userId === row.id)
            // 无则添加
            !flag && this.selectUserData.push({
              userId: row.id,
              userName: row.userName,
              telephone: row.telephone
            })
          } else {
            // 取消复选
            this.selectRows.splice(selIdx, 1)
            // 去除已选数据里的当前列数据
            const idx = this.selectUserData.findIndex(item => item.userId === row.id)
            this.selectUserData.splice(idx, 1)
          }
        }
        this.$refs.tableDataRef.toggleRowSelection(row)
      },
      // 点击表格多选框
      handleRowSelect(selection, row) {
        if (this.isMulti) {
          const selIdx = this.selectRows.findIndex(item => item.id === row.id)
          if (selIdx < 0) {
            // 复选
            this.selectRows.push(row)
            // 判断已选的数据里有无当前操作列数据
            const flag = this.selectUserData.some(item => item.userId === row.id)
            // 无则添加
            !flag && this.selectUserData.push({
              userId: row.id,
              userName: row.userName,
              telephone: row.telephone
            })
          } else {
            // 取消复选
            this.selectRows.splice(selIdx, 1)
            // 去除已选数据里的当前列数据
            const idx = this.selectUserData.findIndex(item => item.userId === row.id)
            this.selectUserData.splice(idx, 1)
          }
        }
      },
      closeTag(row) {
        const idx = this.selectUserData.findIndex(item => item.userId === row.userId)
        this.selectUserData.splice(idx, 1)
        const selIdx = this.selectRows.findIndex(item => item.id === row.userId)
        selIdx > -1 && this.selectRows.splice(selIdx, 1)
        this.$forceUpdate()
        this.tableToggleSelect()
      },
      save() {
        let data = []
        if (this.isMulti && this.selectUserData.length) {
          console.log(this.selectUserData)
          data = data.concat(this.selectUserData)
        } else {
          if (this.selectRows.length && this.selectUserData.length) {
            data.push({
              ...this.selectRows[0],
              ...this.selectUserData[0]
            })
          }
        }
        this.$refs.tableDataRef.clearSelection()
        this.selectRows = []
        this.selectUserData = []
        this.$emit('getUserInfo', data)
        this.pageInfo.curPage = 1
        this.$refs.form.resetFields()
        this.dialogFormVisible = false
      },
      close() {
        this.$refs.tableDataRef.clearSelection()
        this.selectRows = []
        this.selectUserData = []
        this.pageInfo.curPage = 1
        this.$refs.form.resetFields()
        this.dialogFormVisible = false
      },
      backDepart(list) {
        return list.map(item => item.departName).join(',')
      },
      backRolelist(list) {
        return list.map(item => item.roleName).join(',')
      },
      async fetchData() {
        this.queryForm.curPage = this.pageInfo.curPage
        this.queryForm.pageSize = this.pageInfo.pageSize
        this.listLoading = true
        const {
          result: {
            records,
            total
          }
        } = await getUserByPage(this.queryForm)
        this.list = records
        this.pageInfo.total = Number(total)
        this.getSelectRows()
        this.listLoading = false
        if (this.selectRows.length) {
          this.tableToggleSelect()
        }
      },
      // 对表格的多选框进行复选
      tableToggleSelect() {
        const rowData = []
        const selectIds = this.selectRows.map(item => item.id)
        this.list.forEach(item => {
          if (selectIds.includes(item.id)) {
            rowData.push(item)
          }
        })
        this.$nextTick(() => {
          this.$refs.tableDataRef.clearSelection()
          if (rowData.length > 0) {
            rowData.forEach(item => {
              this.$refs.tableDataRef.toggleRowSelection(item)
            })
          }
        })
      },
      getSelectRows() {
        this.selectRows = []
        const selectIds = this.selectUserData.map(item => item.userId)
        this.list.forEach(item => {
          if (selectIds.includes(item.id)) {
            this.selectRows.push(item)
          }
        })
      },
      handleSizeChange(val) {
        this.pageInfo.pageSize = val
        this.pageInfo.curPage = 1
        this.fetchData()
      },
      handleCurrentChange(val) {
        this.pageInfo.curPage = val
        this.fetchData()
      },
      handleQuery() {
        this.pageInfo.curPage = 1
        this.fetchData()
      },
      resetForm(formName) {
        this.pageInfo.curPage = 1
        this.$refs[formName].resetFields()
        this.fetchData()
      }
    }
  }
</script>

<style lang="scss" scoped>
  ::v-deep .el-table__header .el-checkbox {
    display: none;
  }

  .select-content {
    margin-top: 10px;

    .select-list {
      margin-top: 10px;
    }
  }
</style>