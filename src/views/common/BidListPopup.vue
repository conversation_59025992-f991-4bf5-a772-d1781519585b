<template>
  <el-dialog
    v-drag
    append-to-body
    :close-on-click-modal="false"
    :title="title"
    top="2vh"
    :visible.sync="dialogFormVisible"
    width="1100px"
    @closed="close"
  >
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="80px"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item label="公司名称" prop="projectName">
            <el-input
              v-model="queryForm.projectName"
              clearable
              placeholder="请输入公司名称"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              icon="el-icon-search"
              native-type="submit"
              type="primary"
              @click="handleQuery"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-right"
              @click.native="resetForm('form')"
            >
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
    </vab-query-form>
    <el-table
      ref="tableDataRef"
      v-loading="listLoading"
      border
      :data="list"
      :height="450"
      row-key="id"
      stripe
      @row-click="handleRowClick"
      @select="handleRowSelect"
      @select-all="selectAll"
    >
      <el-table-column
        align="center"
        label="选择"
        :reserve-selection="true"
        type="selection"
        width="55"
      />
      <el-table-column
        v-for="(item, index) in columns"
        :key="index"
        :align="item.center ? item.center : 'center'"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'"
      />
      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-size="pageInfo.pageSize"
      :page-sizes="[5, 10, 20]"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <div v-if="isMulti" class="select-content">
      <div class="select-title">当前已选择:</div>
      <div class="select-list">
        <el-tag
          v-for="item in selectRows"
          :key="item.id"
          closable
          @close="closeTag(item)"
        >
          {{ item.projectName }}
        </el-tag>
      </div>
    </div>
    <template #footer>
      <el-button @click="dialogFormVisible = false">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>
  </el-dialog>
</template>


<script>
import { getDataListByPage } from '@/api/bidManagement/bidManagement'
export default {
  data() {
    return {
      title: '投标列表',
      listLoading: false,
      dialogFormVisible: false,
      layout: 'total, sizes, prev, pager, next, jumper',
      pageInfo: {
        curPage: 1,
        pageSize: 10,
        total: 0,
      },
      selectRows: [],
      columns: [
        {
          label: '投标单号',
          prop: 'serialNumber',
        },
        {
          label: '项目名称',
          prop: 'projectName',
        },
        {
          label: '建设单位',
          prop: 'constructionUnitName',
          width: '200',
        },
        {
          label: '登记人',
          prop: 'registerByName',
        },
        {
          label: '登记部门',
          prop: 'createDepartName',
        },
      ],
      list: [],
      selectIds: [],
      queryForm: {
        projectName: '',
        allPermissions: true,
      },
      isMulti: false,
      clienteleTypeList: [],
      industryList: [],
    }
  },
  computed: {},
  created() {
  },
  methods: {
    async showDialog(queryData = {}, isMulti) {
      this.isMulti = isMulti
      this.queryForm = {
        ...this.queryForm,
        ...queryData,
      }
      this.dialogFormVisible = true
      await this.fetchData()
    },
    selectAll(selection) {
      if (!this.isMulti) {
        this.$refs.tableDataRef.clearSelection()
        this.selectRows = []
      } else {
        this.selectRows = selection
      }
    },
    handleRowClick(row) {
      const idx = this.selectRows.findIndex((item) => item.id === row.id)
      if (this.isMulti) {
        if (idx >= 0) {
          this.selectRows.splice(idx, 1)
          this.$refs.tableDataRef.toggleRowSelection(row, false)
        } else {
          this.selectRows.push(row)
          this.$refs.tableDataRef.toggleRowSelection(row, true)
        }
      } else {
        this.$refs.tableDataRef.clearSelection()
        if (idx >= 0) {
          this.selectRows = []
        } else {
          this.$refs.tableDataRef.toggleRowSelection(row, true)
          this.selectRows = [row]
        }
      }
    },
    handleRowSelect(selection, row) {
      if (this.isMulti) {
        this.selectRows = selection
      } else {
        this.$refs.tableDataRef.clearSelection()
        if (this.selectRows.length > 0 && selection.length === 0) {
          this.selectRows = []
        } else {
          this.$refs.tableDataRef.toggleRowSelection(row, true)
          this.selectRows = [row]
        }
      }
    },
    closeTag(row) {
      const idx = this.selectRows.findIndex((item) => item.id === row.id)
      this.selectRows.splice(idx, 1)
      const { selection } = this.$refs.tableDataRef.store.states
      this.$refs.tableDataRef.store.states.selection = selection.filter(
        (item) => item.id !== row.id
      )
    },
    save() {
      let data = []
      if (this.selectRows && this.selectRows.length) {
        data = data.concat(this.selectRows)
      }
      this.$emit('getInfo', data)
      this.dialogFormVisible = false
    },
    close() {
      this.dialogFormVisible = false
      this.$refs.tableDataRef.clearSelection()
      this.selectRows = []
      this.selectIds = []
      this.pageInfo.curPage = 1
      this.$refs.form.resetFields()
    },
    async fetchData() {
      const queryForm = {
        ...this.queryForm,
        curPage: this.pageInfo.curPage,
        pageSize: this.pageInfo.pageSize,
        approvalResult: '2',
      }
      this.listLoading = true
      const {
        result: { records, total },
      } = await getDataListByPage(queryForm)
      this.list = records
      this.pageInfo.total = Number(total)
      this.listLoading = false
    },
    handleSizeChange(val) {
      this.pageInfo.pageSize = val
      this.pageInfo.curPage = 1
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.pageInfo.curPage = val
      this.fetchData()
    },
    handleQuery() {
      this.pageInfo.curPage = 1
      this.fetchData()
    },
    resetForm(formName) {
      this.pageInfo.curPage = 1
      this.$refs[formName].resetFields()
      this.fetchData()
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .el-table__header .el-checkbox {
  display: none;
}
.select-content {
  margin-top: 10px;
  .select-list {
    margin-top: 10px;
  }
}
</style>
