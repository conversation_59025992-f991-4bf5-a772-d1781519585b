<template>
  <el-dialog
    v-drag
    append-to-body
    :close-on-click-modal="false"
    left
    :title="title"
    :visible.sync="isShowDialog"
    width="1200px"
  >
    <div v-if="isShow == true" class="tools">
      <el-button
        :disabled="
          !hasPermission(['admin']) || (onlyOneFile && list.length >= 1)
        "
        icon="el-icon-plus"
        style="margin-bottom: 10px"
        type="primary"
        @click="handleAdd"
      >
        新增
      </el-button>
    </div>
    <el-table
      v-loading="listLoading"
      border
      class="tableList"
      :data="list"
      element-loading-text="加载中"
      max-height="490"
      stripe
    >
      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        :align="item.center ? item.center : 'center'"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'"
      >
        <template #default="{ row }">
          <span v-if="item.label === '文件名'">
            <div v-if="/^(jpeg|png|jpg|bmp)$/.test(row.fileExt)">
              <el-image
                fit="cover"
                :preview-src-list="[fileAddr + row.urlPath]"
                :src="fileAddr + row.urlPath"
                style="width: 80px; height: 80px"
                title="点击预览"
              />
              <div>{{ row.fileName }}</div>
            </div>
            <!--            <el-link v-else-if="/^(pdf|xls|doc|docx)$/.test(row.fileExt)" type="primary" :href="fileAddr + row.urlPath" target="_blank" :underline="false" title="点击预览">-->
            <!--              {{ row.fileName }}-->
            <!--            </el-link>-->
            <el-link
              v-else
              :href="
                'http://kkview.jxth.com.cn:8012/onlinePreview?url=' +
                encodeURIComponent(toBase(fileAddr + row.urlPath))
              "
              target="_blank"
              title="点击预览"
              type="primary"
              :underline="false"
            >
              {{ row.fileName }}
            </el-link>
            <!--            <span v-else>{{ row.fileName }}</span>-->
          </span>
          <span v-else-if="item.label === '文件大小(MB)'">
            {{ fileSizeFormat(row.fileSize) }}
          </span>
          <span v-else-if="item.label === '文件地址'">
            {{ fileAddr + row.urlPath }}
          </span>
          <span v-else>{{ row[item.prop] }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="操作" width="200">
        <template #default="{ row }">
          <el-button
            icon="el-icon-download"
            style="margin: 0 10px 10px 0 !important"
            type="success"
            @click="downloadFile(row)"
          >
            下载
          </el-button>
          <el-button
            v-if="isShow == true"
            :disabled="!hasPermission(['admin'])"
            icon="el-icon-delete"
            style="margin: 0 10px 10px 0 !important"
            type="danger"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <template #empty>
      <el-image
        class="vab-data-empty"
        :src="require('@/assets/empty_images/data_empty.png')"
      />
    </template>
<!--    <el-pagination-->
<!--      background-->
<!--      :current-page="pageInfo.curPage"-->
<!--      :layout="layout"-->
<!--      :page-size="pageInfo.pageSize"-->
<!--      :total="pageInfo.total"-->
<!--      @current-change="handleCurrentChange"-->
<!--      @size-change="handleSizeChange"-->
<!--    />-->
    <CommonUploadLargeFileFdfsPopup
      ref="CommonUploadLargeFileFdfsPopup"
      @refreshUploadFileList="refreshUploadFileList"
    />
    <!-- <el-image-viewer v-if="isShowViewer" :on-close="closeViewer" :url-list="imgSrcList" /> -->
  </el-dialog>
</template>

<script>
  import {
    getUploadFilesByPage,
    deleteFile,
    downloadFile,
  } from '@/api/system/uploadFile-api'
  import tableMix from '@/views/mixins/table'
  import CommonUploadLargeFileFdfsPopup from '@/views/system/common/CommonUploadLargeFileFdfsPopup'
  import { fileAddr } from '@/utils/constants'
  import { hasPermission } from "@/utils/permission";
  // import { baseURL } from '@/config'

  export default {
    name: 'CommonUploadList',
    components: {
      CommonUploadLargeFileFdfsPopup,
      // ElImageViewer
    },
    mixins: [tableMix],
    data() {
      return {
        checkList: ['文件名', '文件大小(MB)', '文件类型', '上传人', '上传时间'],
        columns: [
          {
            label: '文件名',
            prop: 'fileName',
            disableCheck: true,
          },
          {
            label: '文件大小(MB)',
            prop: 'fileSize',
            disableCheck: true,
          },
          {
            label: '文件类型',
            prop: 'fileExt',
          },
          {
            label: '上传人',
            prop: 'createByName',
          },
          {
            label: '上传时间',
            prop: 'createTime',
          },
        ],
        onlyOneFile: false,
        fileAddr: '',
        title: '附件列表',
        addTitle: '新增附件',
        isShowDialog: false,
        isShowAddDialog: false,
        listLoading: false,
        isShowViewer: false,
        fileLists: [],
        imgSrcList: [],
        tableData: [],
        queryForm: {
          fileName: '',
          bizId: '',
          bizCode: '',
        },
        isShow: true,
      }
    },
    created() {},
    methods: {
      hasPermission,
      // checkPermission(value) {
      //   return checkPermission(value)
      // },
      toBase(url) {
        return this.$Base64.encode(url)
      },
      fileSizeFormat(data) {
        const fileSize = data
        const fileSizeMb = parseFloat(fileSize / 1048576).toFixed(4)
        return fileSizeMb
      },

      async fetchData() {
        this.queryForm.curPage = this.pageInfo.curPage
        this.queryForm.pageSize = this.pageInfo.pageSize
        this.listLoading = true
        const {
          result: { records, total },
        } = await getUploadFilesByPage(this.queryForm)
        this.list = records
        this.pageInfo.total = Number(total)
        this.listLoading = false
      },

      handleDelete(row) {
        if (row.id) {
          this.$baseConfirm('你确定要删除当前文件吗', null, async () => {
            deleteFile(row.id)
              .then((response) => {
                this.pageInfo.curPage = 1
                this.fetchData()
                this.$baseMessage(
                  '删除成功!',
                  'success',
                  'vab-hey-message-success'
                )
              })
              .catch((err) => {
                this.$baseMessage('删除失败!', 'error', 'vab-hey-message-error')
              })
          })
        }
      },
      handleAdd() {
        this.$refs.CommonUploadLargeFileFdfsPopup.showUploadLargeDialog(
          this.queryForm
        )
      },
      showUploadListDialog(data) {
        this.onlyOneFile = false
        this.pageInfo.curPage = 1
        this.queryForm.bizId = data.bizId
        this.queryForm.bizCode = data.bizCode
        if (data.title) {
          this.title = data.title
        }
        if (data.onlyOneFile) {
          this.onlyOneFile = data.onlyOneFile
        }
        this.isShow = data.isShow === false ? data.isShow : true
        this.fileLists = []
        this.isShowDialog = true
        this.fetchData()
      },
      batchUpload() {
        const fileList = this.fileLists
        const params = new FormData()
        if (fileList.length < 1) {
          this.$baseMessage(
            '请选择最少一个文件！',
            'error',
            'vab-hey-message-error'
          )
          return
        }
        fileList.forEach((file) => {
          params.append('file', file)
        })
        params.append('bizCode', this.bizCode)
        params.append('bizId', this.bizId)
        uploadFile(params).then((response) => {
          this.$baseMessage('上传成功！', 'success', 'vab-hey-message-success')
        })
        return false
      },
      refreshUploadFileList() {
        this.pageInfo.curPage = 1
        this.fetchData()
      },
      downloadFile(row) {
        downloadFile(row.id).then((response) => {
          const data = response
          if (!data) {
            return
          }
          // 构造a标签 通过a标签来下载
          const url = window.URL.createObjectURL(new Blob([data]))
          const a = document.createElement('a')
          a.style.display = 'none'
          a.href = url
          // 此处的download是a标签的内容，固定写法，不是后台api接口
          a.setAttribute('download', row.fileName + '.' + row.fileExt)
          document.body.appendChild(a)
          // 点击下载
          a.click()
          // 下载完成移除元素
          document.body.removeChild(a)
          // 释放掉blob对象
          window.URL.revokeObjectURL(url)
        })
      },
      onPreview(row) {
        this.imgSrcList = row.imgSrcList
        this.isShowViewer = true
      },
      // 关闭查看器
      closeViewer() {
        this.isShowViewer = false
      },
    },
  }
</script>

<style scoped></style>
