<template>
  <el-dialog v-drag append-to-body :title="title" :visible.sync="dialogFormVisible" width="1100px"
    :close-on-click-modal="false" top="2vh">
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form ref="form" :inline="true" label-width="80px" :model="queryForm" @submit.native.prevent>
          <el-form-item label="监听器名称" prop="listenerName">
            <el-input v-model="queryForm.listenerName" placeholder="请输入监听器名称" />
          </el-form-item>
          <el-form-item label="监听器类型" prop="listenerType">
            <el-select v-model="queryForm.listenerType" placeholder="选择监听器类型">
              <el-option v-for="item in listenerTypeArr" :key="item.id" :label="item.dictName" :value="item.id" />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button icon="el-icon-search" native-type="submit" type="primary" @click="handleQuery">
              查询
            </el-button>
            <el-button icon="el-icon-refresh-right" @click.native="resetForm('form')">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
    </vab-query-form>
    <el-table ref="tableSort" v-loading="listLoading" border :height="450" :data="list" stripe
      @selection-change="setSelectRows">
      <el-table-column label="选择" width="55" align="center">
        <template #default="{ row }">
          <el-radio :label="row.id" v-model="radio" @change.native="getCurrentRow(row)">{{""}}</el-radio>
        </template>
      </el-table-column>
      <el-table-column v-for="(item, index) in columns" :key="index" :align="item.center ? item.center : 'center'"
        :label="item.label" :prop="item.prop" :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'">
        <template #default="{ row }">
          <span v-if="item.label === '状态'">
            {{ row.statu === '1'? '生效' : '未生效' }}
          </span>
          <span v-else>{{ row[item.prop] }}</span>
        </template>
      </el-table-column>
      <template #empty>
        <el-image class="vab-data-empty" :src="require('@/assets/empty_images/data_empty.png')" />
      </template>
    </el-table>
    <el-pagination background :current-page="pageInfo.curPage" :layout="layout" :page-sizes="[5, 10, 20]"
      :page-size="pageInfo.pageSize" :total="pageInfo.total" @current-change="handleCurrentChange"
      @size-change="handleSizeChange" />
    <template #footer>
      <div style="text-align: left;">
        <div style="color: red;margin-bottom: 5px;">*(选择后只添加，删除请点击删除)</div>
        当前选中的流程监听器：
        <el-tag size="medium" v-for="tag in listenerNames" :key="tag" closable @close="handleClose(tag)">
          {{ tag }}
        </el-tag>
      </div>
      <el-button @click="dialogFormVisible = false">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
  import { getWorkflowListenerByPage } from '@/api/workflow/workFlowListener-api'
  import { getDictList } from '@/api/system/dict-api'

  export default {
    data() {
      return {
        title: '选择流程监听器',
        dialogFormVisible: false,
        queryForm: {
          listenerName: "",
          listenerType: ""
        },
        listenerTypeArr: [],
        layout: 'total, sizes, prev, pager, next, jumper',
        pageInfo: {
          curPage: 1,
          pageSize: 10,
          total: 0
        },
        selectRows: [],
        columns: [{
            label: '排序',
            prop: 'sort'
          },
          {
            label: '监听器名称',
            prop: 'listenerName',
          },
          {
            label: '监听器类型',
            prop: 'listenerTypeName',
          },
          {
            label: '事件类型',
            prop: 'eventTypeName',
          },
          {
            label: '表达类型',
            prop: 'expTypeName',
          },
          {
            label: '表达式',
            prop: 'expression',
          },
          {
            label: '状态',
            prop: 'statu',
          },
          {
            label: '备注',
            prop: 'remark'
          }
        ],
        list: [],
        listenerIds: [],
        listenerNames: [],
        type: '',
        radio: ''
      }
    },
    computed: {

    },
    created() {
      this.getDictDetailsByCode()
      this.fetchData()
    },
    methods: {
      showWorkflowListenerDialog(data, type) {
        this.type = type
        this.radio = ''
        this.listenerIds = []
        this.listenerNames = []
        if (data.listenerNames !== '') {
          this.listenerIds = data.listenerIds.split(",")
          this.listenerNames = data.listenerNames.split(",")
        }
        this.dialogFormVisible = true
      },
      setSelectRows(val) {
        // 保存当前选中的对象
        this.selectRows = val
      },
      getCurrentRow(row) { //获取选中数据
        if (this.listenerIds.indexOf(row.id) < 0) {
          this.listenerIds.push(row.id)
          this.listenerNames.push(row.listenerName)
        }
      },
      handleClose(tag) {
        let index = this.listenerNames.indexOf(tag)
        this.listenerNames.splice(index, 1)
        this.listenerIds.splice(index, 1)
      },
      save() {
        if (this.type === 'node') {
          this.$emit('callBackListenerData', this.listenerIds, this.listenerNames)
        } else {
          this.$emit('callBackFlowListenerData', this.listenerIds, this.listenerNames)
        }
        this.dialogFormVisible = false
      },
      async fetchData() {
        this.queryForm.curPage = this.pageInfo.curPage
        this.queryForm.pageSize = this.pageInfo.pageSize
        this.listLoading = true
        const {
          result: {
            records,
            total
          }
        } = await getWorkflowListenerByPage(this.queryForm)
        this.list = records
        this.pageInfo.total = Number(total)
        this.listLoading = false
      },
      async getDictDetailsByCode() {
        await getDictList({dictCode: 'listenerType'}).then(response => {
          this.listenerTypeArr = response.result.find((item) => {
            return item.dictCode == 'listenerType'
          }).children
        })
      },
      handleSizeChange(val) {
        this.pageInfo.pageSize = val
        this.pageInfo.curPage = 1
        this.fetchData()
      },
      handleCurrentChange(val) {
        this.pageInfo.curPage = val
        this.fetchData()
      },
      handleQuery() {
        this.pageInfo.curPage = 1
        this.fetchData()
      },
      resetForm(formName) {
        this.pageInfo.curPage = 1
        this.$refs[formName].resetFields()
        this.fetchData()
      },
    }
  }
</script>

<style lang="scss" scoped>

</style>
