<template>
  <div class="con-container">
    <el-tabs v-model="activeName" type="card" :before-leave="beforeLeaveTab">
      <el-tab-pane label="基本信息" name="first">
        <el-form
          ref="dataForm"
          v-loading="formloading"
          :inline="true"
          :rules="rules"
          :model="formData"
          label-position="right"
          label-width="130px"
        >
          <table class="form-table">
            <tr>
              <td>
                <el-form-item label="单号" prop="serialNumber">
                  <el-input
                    v-model="formData.serialNumber"
                    placeholder="自动生成"
                    disabled
                    style="width: 250px"
                  ></el-input>
                </el-form-item>
              </td>
              <td>
                <el-form-item label="登记部门" prop="registerDepart">
                  <t-form-depart-tree
                    v-model="formData.registerDepart"
                    :treeData="departData"
                    :defaultProps="{
                      children: 'children',
                      label: 'departName',
                    }"
                    :showTop="false"
                    disabled
                  />
                </el-form-item>
              </td>
              <td>
                <el-form-item label="登记日期" prop="recordDate">
                  <el-date-picker
                    v-model="formData.recordDate"
                    type="date"
                    style="width: 250px"
                    :disabled="!formData.isAdd"
                    placeholder="选择登记日期"
                    :clearable="false"
                  ></el-date-picker>
                </el-form-item>
              </td>
            </tr>
            <tr>
              <td style="position: relative">
                <el-form-item label="项目单号" prop="projectCode">
                  <el-input
                    v-model="formData.projectCode"
                    placeholder="选择合同所属项目"
                    @click.native="selectContractProject"
                    style="width: 250px"
                    :disabled="!formData.isAdd"
                  >
                  </el-input>
                </el-form-item>
                <span
                  v-if="formData.budgetId"
                  class="view-budget"
                  @click="viewBudgetDetail"
                >
                  项目预算详情
                </span>
              </td>
              <td colspan="2">
                <el-form-item label="项目名称" prop="projectName">
                  <el-input
                    v-model="formData.projectName"
                    placeholder="自动关联"
                    readonly
                    style="width: 783px"
                    disabled
                  ></el-input>
                </el-form-item>
              </td>
            </tr>
            <tr>
              <td>
                <el-form-item label="合同编号" prop="contractCode">
                  <el-input
                    v-model="formData.contractCode"
                    placeholder="请输入合同编号"
                    style="width: 250px"
                  ></el-input>
                </el-form-item>
              </td>
              <td colspan="2">
                <el-form-item label="合同名称" prop="contractName">
                  <el-input
                    v-model="formData.contractName"
                    placeholder="请输入合同名称"
                    style="width: 783px"
                  ></el-input>
                </el-form-item>
              </td>
            </tr>
            <tr>
              <td>
                <el-form-item label="合同金额(元)" prop="contractMoney">
                  <el-input
                    v-model="formData.contractMoney"
                    placeholder="自动计算"
                    style="width: 250px"
                    disabled
                    type="number"
                  ></el-input>
                </el-form-item>
              </td>
              <td>
                <el-form-item label="合同份数" prop="contractNum">
                  <el-input
                    v-model="formData.contractNum"
                    placeholder="请输入合同分数"
                    style="width: 250px"
                  ></el-input>
                </el-form-item>
              </td>
              <td>
                <!-- <el-form-item label="所需公章" prop="officialSeal">
                  <el-select
                    v-model="formData.officialSeal"
                    multiple
                    style="width: 250px"
                  >
                    <el-option
                      v-for="item in officialSealTypeList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item> -->
                <el-form-item label="签署时间" prop="signedTime">
                  <el-date-picker
                    v-model="formData.signedTime"
                    type="date"
                    style="width: 250px"
                    placeholder="选择登记日期"
                  ></el-date-picker>
                </el-form-item>
              </td>
            </tr>
            <tr>
              <td>
                <el-form-item label="所属部门" prop="departId">
                  <!-- <el-popover placement="bottom" width="250" trigger="click" @show="departTreeShow" @hide="departTreeHide">
                    <el-tree ref="departTree" style="max-height:600px;overflow:auto;" :filter-node-method="filterNode"
                      :data="departData" node-key="id" check-strictly default-expand-all show-checkbox :props="defaultProps"
                      @check-change="departNodeClick" />
                    <el-input slot="reference" disabled v-model="formData.departName" placeholder="自动关联" readonly style="width: 250px;"></el-input>
                  </el-popover> -->
                  <t-form-depart-tree
                    v-model="formData.departId"
                    :treeData="departData"
                    :defaultProps="{
                      children: 'children',
                      label: 'departName',
                    }"
                    :showTop="false"
                    disabled
                    placeholder="自动关联"
                  />
                </el-form-item>
              </td>
              <td colspan="2">
                <el-form-item label="合同签署单位" prop="contractSigningUnit">
                  <el-input
                    :value="formData.clienteleName"
                    placeholder="请选择合同签署单位"
                    style="width: 783px"
                    readonly
                    @click.native="selectSpplier"
                  ></el-input>
                </el-form-item>
              </td>
              <td>
                <!-- <el-form-item label="签署时间" prop="signedTime">
                  <el-date-picker
                    v-model="formData.signedTime"
                    type="date"
                    style="width: 250px"
                    placeholder="选择登记日期"
                  ></el-date-picker>
                </el-form-item> -->
              </td>
            </tr>
            <tr>
              <td>
                <el-form-item label="采购方联系人" prop="purchaseContacts">
                  <el-input
                    v-model="formData.purchaseContacts"
                    placeholder="请收入采购联系人"
                    style="width: 250px"
                  ></el-input>
                </el-form-item>
              </td>
              <td>
                <el-form-item label="联系方式" prop="principalMobile">
                  <el-input
                    v-model="formData.principalMobile"
                    placeholder="请输入联系方式"
                    type="number"
                    style="width: 250px"
                  ></el-input>
                </el-form-item>
              </td>
              <td>
                <el-form-item label="采购人" prop="projectPrincipalName">
                  <el-input
                    v-model="formData.projectPrincipalName"
                    readonly
                    style="width: 250px"
                    placeholder="请选择采购人"
                    disabled
                    @click.native="selectProjectCharge"
                  ></el-input>
                </el-form-item>
              </td>
            </tr>
            <!-- <tr>
              <td>
                <el-form-item label="项目负责人" prop="projectPrincipalName">
                  <el-input
                    v-model="formData.projectPrincipalName"
                    readonly
                    style="width: 250px"
                    placeholder="请选择项目负责人"
                    @click.native="selectProjectCharge"
                  ></el-input>
                </el-form-item>
              </td>
              <td>
                <el-form-item label="联系电话" prop="principalMobile">
                  <el-input
                    v-model="formData.principalMobile"
                    placeholder="自动关联"
                    disabled
                    maxlength="11"
                    style="width: 250px"
                  ></el-input>
                </el-form-item>
              </td>
              <td>
                <el-form-item label="登记人" prop="registrantName">
                  <el-input
                    v-model="formData.registrantName"
                    disabled
                    style="width: 250px"
                  ></el-input>
                </el-form-item>
              </td>
            </tr> -->
            <!-- <tr>
              <td colspan="3">
                <el-form-item label="采购关联" prop="purchaseId">
                  <el-button type="primary" @click="exportPurchaseIn">
                    导入采购信息
                  </el-button>
                  <div class="des-info" v-if="formData.purchaseId">
                    <div class="des-type">采购名称</div>
                    <div class="des-val">{{ formData.purchaseReasons }}</div>
                    <div class="des-type">采购单号</div>
                    <div class="des-val">
                      {{ formData.purchaseCode }}
                      <span class="view-tips" @click="viewPurchaseDetail">
                        查看采购详情
                      </span>
                    </div>
                  </div>
                </el-form-item>
              </td>
            </tr> -->
            <tr>
              <td colspan="3">
                <el-form-item label="付款条件" prop="collectionTerms">
                  <el-input
                    v-model="formData.collectionTerms"
                    type="textarea"
                    rows="3"
                    placeholder="请输入内容"
                    maxlength="1000"
                    style="width: 1320px"
                  ></el-input>
                </el-form-item>
              </td>
            </tr>
            <tr>
              <td colspan="3">
                <el-form-item label="备注" prop="remarks">
                  <el-input
                    v-model="formData.remarks"
                    type="textarea"
                    rows="3"
                    placeholder="请输入内容"
                    maxlength="1000"
                    style="width: 1320px"
                  ></el-input>
                </el-form-item>
              </td>
            </tr>
          </table>
        </el-form>
      </el-tab-pane>
      <!-- <el-tab-pane label="采购信息" name="second">
        <PurchaseExportIn
          ref="purchaseExportInRef"
          :projectId="formData.projectId"
          :selectType="formData.selectType"
          :dataObj="{
            purchaseId: formData.purchaseId,
            purchaseCode: formData.purchaseCode,
            purchaseReasons: formData.purchaseReasons,
            projectName: formData.projectName,
          }"
        />
      </el-tab-pane> -->
      <el-tab-pane label="费用明细" name="third">
        <ExpenseDetail
          ref="expenseDetailRef"
          :detailList="detailList"
          :budgetId="formData.budgetId"
          :contractId="formData.id"
          @updateMoney="updateMoney"
        />
      </el-tab-pane>
    </el-tabs>
    <ProjectListPopup
      ref="projectListPopupRef"
      @getProjectInfo="getProjectInfo"
    />
    <VabUserListDialog ref="userListPopupRef" @callBackData="getUserInfo" />
    <!-- 采购数据导入 -->
    <PurchaseExportInDialog
      ref="purchaseExportInDialogRef"
      @getInfo="getPurchaseData"
    />
    <!-- 采购详情 -->
    <PurchaseDetail ref="purchaseDetailRef" />
    <!-- 预算详情 -->
    <BudgetDetail ref="budgetDetailRef" />
    <!-- 合同签署单位选择 -->
    <SelectSupplierDialog ref="selectSupplierRef" @getInfo="getSupplierData" />
  </div>
</template>

<script>
  import ProjectListPopup from '@/views/common/ProjectListPopup.vue'
  import { getDataList } from '@/api/financialManagement/budgetManagement'
  import ExpenseDetail from './ExpenseDetail.vue'
  import PurchaseExportIn from './PurchaseExportIn.vue'
  import PurchaseExportInDialog from './PurchaseExportInDialog.vue'
  import { deepClone } from '@/utils'
  import { getDataAndDetail } from '@/api/contract'
  import { constantsExpose } from '@/utils/constantsExpose.js'
  import { getPurchaseData } from '@/api/administrativeManagement/purchaseApplication-api'
  import { getBudgetInfoIncludeDetails } from '@/api/financialManagement/budgetManagement'
  import BudgetDetail from '@/views/financialManagement/budgetManagement/components/budgetApplicationDetail.vue'
  import PurchaseDetail from '@/views/approvalManagement/purchasingManagement/components/purchaseApplicationDetail.vue'
  import SelectSupplierDialog from './SelectSupplierDialog.vue'
  import { checkContractName } from '@/api/contract'
  export default {
    components: {
      ProjectListPopup,
      ExpenseDetail,
      PurchaseExportIn,
      PurchaseExportInDialog,
      BudgetDetail,
      PurchaseDetail,
      SelectSupplierDialog,
    },
    props: {
      dataObj: {
        type: Object,
        default() {
          return {}
        },
      },
      departData: {
        type: Array,
        default() {
          return []
        },
      },
      officialSealTypeList: {
        type: Array,
        default() {
          return []
        },
      },
    },
    data() {
      const validateContractName = async (rule, value, callback) => {
        const params = {
          id: this.formData.id,
          contractName: value
        }
        await checkContractName(params).then(res => {
          if(res.result && res.result === '400') {
            callback(new Error('该合同名称已存在'))
          } else {
            callback()
          }
        })
      }
      return {
        formloading: false,
        formData: {},
        defaultProps: {
          children: 'children',
          label: 'label',
        },
        rules: {
          registerDepart: [
            {
              required: true,
              message: '请选择登记部门',
              trigger: 'blur,change',
            },
          ],
          recordDate: [
            {
              required: true,
              message: '请选择登记日期',
              trigger: 'blur,change',
            },
          ],
          projectCode: [
            { required: true, message: '请选择项目', trigger: 'blur,change' },
          ],
          contractName: [
            { required: true, message: '请输入合同名称', trigger: 'blur' },
            { validator: validateContractName, trigger: 'blur' }
          ],
          // contractCode: [
          //   { required: true, message: '请输入合同编号', trigger: 'blur' },
          // ],
          // contractMoney: [
          //   {required: true, message: '请输入合同金额', trigger: 'blur'}
          // ],
          // departId: [
          //   {required: true, message: '请选择所属部门', trigger: 'change'}
          // ],
          officialSeal: [
            { required: true, message: '请选择所需用章', trigger: 'change' },
          ],
          projectPrincipalName: [
            { required: true, message: '请选择项目采购人', trigger: 'change' },
          ],
          purchaseId: [
            { required: true, message: '请关联采购', trigger: 'change' },
          ],
          collectionTerms: [
            { required: true, message: '请输入付款条件', trigger: 'blur' },
          ],
        },
        activeName: 'first',
        detailList: [], // 费用明细
      }
    },
    async created() {
      this.formData = { ...this.dataObj }
      if (!this.formData.isAdd) {
        const params = {
          id: this.formData.id,
          systemId: constantsExpose.SYSTEM_ID,
        }
        const { result } = await getDataAndDetail(params)
        this.detailList = result.detailList
      } else {
        this.formData.projectPrincipal = this.$store.getters['user/userId']
        this.formData.projectPrincipalName = this.$store.getters['user/userName']
      }
    },
    methods: {
      beforeLeaveTab(activeName, oldActiveName) {
        if (oldActiveName == 'first') {
          let flag = false
          this.$refs.dataForm.validate((valid) => {
            flag = valid
            if(!flag) {
              this.$baseMessage(
                '请先完善基本信息',
                'warning',
                'vab-hey-message-warning'
              )
            }
          })
          return flag
        }
      },
      selectContractProject() {
        this.$refs.projectListPopupRef.showDialog({
          selectIds: this.formData.projectId,
          projectName: this.formData.projectName
        })
      },
      updateMoney(money) {
        this.formData.contractMoney = money
      },
      registerDepartNodeClick(data, checked, node) {
        if (this.formData.registerDepart) {
          if (checked) {
            this.$refs.registerDepartTree.setCheckedKeys([])
            this.$refs.registerDepartTree.setCheckedKeys([data.id])
          } else {
            this.formData.registerDepart = ''
            this.$refs.registerDepartTree.setCheckedKeys([])
          }
        } else {
          this.formData.registerDepart = data.id
          this.$refs.registerDepartTree.setCheckedKeys([data.id])
        }
      },
      registerDepartTreeHide() {
        const selectDatas = this.$refs.registerDepartTree.getCheckedNodes()
        if (selectDatas.length > 0) {
          this.formData.registerDepartName = selectDatas[0].label
          this.formData.registerDepart = selectDatas[0].id
          this.formData.departCode = selectDatas[0].departCode
        } else {
          this.formData.registerDepartName = ''
          this.formData.registerDepart = ''
          this.formData.departCode = ''
        }
      },
      registerDepartTreeShow() {
        if (!this.formData.isAdd) {
          this.$nextTick(() => {
            this.$refs.registerDepartTree.setCheckedKeys([
              this.formData.registerDepart,
            ])
          })
        }
      },
      filterNode(value, data) {
        if (!value) return true
        return data.label.indexOf(value) !== -1
      },
      departTreeShow() {
        if (!this.formData.isAdd) {
          this.$nextTick(() => {
            this.$refs.departTree.setCheckedKeys([this.formData.departId])
          })
        }
      },
      departTreeHide() {
        const selectDatas = this.$refs.departTree.getCheckedNodes()
        if (selectDatas.length > 0) {
          this.formData.departName = selectDatas[0].label
          this.formData.departId = selectDatas[0].id
        } else {
          this.formData.departName = ''
          this.formData.departId = ''
        }
      },
      departNodeClick(data, checked, node) {
        if (this.formData.departId) {
          if (checked) {
            this.$refs.departTree.setCheckedKeys([])
            this.$refs.departTree.setCheckedKeys([data.id])
          } else {
            this.formData.departId = ''
            this.$refs.departTree.setCheckedKeys([])
          }
        } else {
          this.formData.departId = data.id
          this.$refs.departTree.setCheckedKeys([data.id])
        }
      },
      selectProjectCharge() {
        // 若有多个则以逗号区分
        let selUserList = []
        if (this.formData.projectPrincipal) {
          selUserList.push({
            userId: this.formData.projectPrincipal,
            userName: this.formData.projectPrincipalName,
          })
        }
        this.$refs.userListPopupRef.showUserDialog({ selUserList })
      },
      getUserInfo(data) {
        if (data.length > 0) {
          this.formData.projectPrincipal = data[0].id
          this.formData.projectPrincipalName = data[0].userName
          //this.formData.principalMobile = data[0].telephone || ''
        } else {
          this.formData.projectPrincipal = ''
          this.formData.projectPrincipalName = ''
         // this.formData.principalMobile = ''
        }
      },
      async getProjectInfo(data) {
        if (data.length > 0) {
          const dataObj = data[0]
          const { id, projectName, serialNumber, registerDepartId} = dataObj
          if (this.formData.budgetId && id !== this.formData.budgetId) {
            this.formData.budgetId = ''
            this.detailList = []
          }
          this.formData = {
            ...this.formData,
            projectId: id,
            projectName,
            projectCode: serialNumber,
            departId: registerDepartId,
          }
          this.$refs.dataForm.clearValidate('projectCode')
          const params = {
            projectId: id,
            approvalResult: '2',
          }
          const { result } = await getDataList(params)
          if (result.length) {
            const { id, departId, departName } = result[0]
            // this.formData.departId = departId
            // this.formData.departName = departName
            this.formData.budgetId = id
          }
        }
      },
      // 导入采购信息
      exportPurchaseIn() {
        if (!this.formData.projectId) {
          this.$baseMessage(
            '请先关联合同所属项目',
            'warning',
            'vab-hey-message-warning'
          )
          return
        }
        const paramsData = {
          projectId: this.formData.projectId,
          selectType: this.formData.selectType,
        }
        this.$refs.purchaseExportInDialogRef.showDialog(paramsData)
      },
      // 获取采购信息
      getPurchaseData(data) {
        if (!data.length) return
        const { id, reasons, serialNumber, projectName } = data[0]
        this.formData.purchaseId = id
        this.formData.purchaseCode = serialNumber
        this.formData.purchaseReasons = reasons
        this.$refs.dataForm.clearValidate('purchaseId')
      },
      // 查看采购信息
      async viewPurchaseDetail() {
        const params = {
          id: this.formData.purchaseId,
        }
        const { result } = await getPurchaseData(params)
        this.$refs.purchaseDetailRef.showDialog(result)
      },
      // 项目预算详情
      async viewBudgetDetail() {
        const { result } = await getBudgetInfoIncludeDetails(
          this.formData.budgetId
        )
        const budgetData = result.budget[0]
        this.$refs.budgetDetailRef.showDialog(budgetData)
      },
      // 选择供应商
      selectSpplier() {
        this.$refs.selectSupplierRef.showDialog()
      },
      getSupplierData(data) {
        if(data.length > 0) {
          this.formData.clienteleName = data[0].name
          this.formData.contractSigningUnit = data[0].id
        }
      }
    },
  }
</script>

<style lang="scss" scoped>
  ::v-deep .form-table {
    .el-input__inner {
      padding-right: 0;
      &::-webkit-inner-spin-button {
        margin: 0;
        -webkit-appearance: none !important;
      }
      &::-webkit-outer-spin-button {
        margin: 0;
        -webkit-appearance: none !important;
      }
    }
    .el-form-item__error {
      position: absolute !important;
      top: 45px;
      left: 10px;
    }
  }
  .des-info {
    width: 1320px !important;
    border: 1px solid #ebeef5;
    display: flex;
    align-items: center;
    font-size: 14px;
    margin-top: 18px;
    .des-type {
      width: 200px;
      padding: 10px;
      background: #fafafa;
      color: #909399;
      box-sizing: border-box;
      border-right: 1px solid #ebeef5;
    }
    .des-val {
      padding: 10px;
      min-width: 200px;
      flex: 1;
      box-sizing: border-box;
      border-right: 1px solid #ebeef5;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
    .view-tips {
      font-size: 12px;
      color: #1e7dff;
      cursor: pointer;
      padding-left: 5px;
    }
  }

  .view-budget {
    position: absolute;
    left: 405px;
    top: 30px;
    font-size: 12px;
    color: #1e7dff;
    cursor: pointer;
    padding-left: 5px;
  }
</style>
