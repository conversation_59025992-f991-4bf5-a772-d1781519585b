<template>
  <el-dialog
    :close-on-click-modal="false"
    append-to-body
    :title="pageTitle"
    :visible.sync="dialogDetailVisible"
    width="1650px"
    :before-close="closeBtn"
    @closed="closedDialog"
    top="2vh"
    center
    v-loading="formloading"
  >
    <div class="con-container">
      <el-tabs v-model="activeName" type="card" :before-leave="beforeLeaveTab">
        <el-tab-pane label="基本信息" name="first">
          <el-form
            ref="dataForm"
            v-loading="formloading"
            :inline="true"
            :rules="rules"
            :model="formData"
            label-position="right"
            label-width="130px"
          >
            <table class="form-table">
              <tr>
                <td>
                  <el-form-item label="单号" prop="serialNumber">
                    <el-input
                      v-model="formData.serialNumber"
                      placeholder="自动生成"
                      disabled
                      style="width: 250px"
                    ></el-input>
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="登记部门" prop="createDepart">
                    <t-form-tree
                      v-model="formData.createDepart"
                      :treeData="departData"
                      :defaultProps="{
                        children: 'children',
                        label: 'departName',
                      }"
                      :showTop="false"
                      disabled
                    />
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="登记日期" prop="recordDate">
                    <el-date-picker
                      v-model="formData.recordDate"
                      type="date"
                      style="width: 250px"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      :disabled="!formData.isAdd"
                      placeholder="选择登记日期"
                      :clearable="false"
                    ></el-date-picker>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td style="position: relative">
                  <el-form-item label="项目单号" prop="projectCode">
                    <el-input
                      v-model="formData.projectCode"
                      placeholder="选择合同所属项目"
                      @click.native="selectContractProject"
                      style="width: 250px"
                      readonly
                      :disabled="!formData.isAdd"
                    >
                    </el-input>
                  </el-form-item>
                  <span
                    v-if="formData.budgetId"
                    class="view-budget"
                    @click="viewBudgetDetail"
                  >
                    项目预算详情
                  </span>
                </td>
                <td colspan="2">
                  <el-form-item label="项目名称" prop="projectName">
                    <el-input
                      v-model="formData.projectName"
                      placeholder="自动关联"
                      readonly
                      style="width: 783px"
                      disabled
                    ></el-input>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td>
                  <el-form-item label="合同编号" prop="contractCode">
                    <el-input
                      v-model="formData.contractCode"
                      placeholder="请输入合同编号"
                      style="width: 250px"
                    ></el-input>
                  </el-form-item>
                </td>
                <td colspan="2">
                  <el-form-item label="合同名称" prop="contractName">
                    <el-input
                      v-model="formData.contractName"
                      placeholder="请输入合同名称"
                      style="width: 783px"
                    ></el-input>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td>
                  <el-form-item label="合同金额(元)" prop="contractMoney">
                    <el-input
                      v-model="formData.contractMoney"
                      placeholder="自动计算"
                      style="width: 250px"
                      disabled
                      type="number"
                    ></el-input>
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="合同份数" prop="contractNum">
                    <el-input
                      v-model="formData.contractNum"
                      placeholder="请输入合同分数"
                      style="width: 250px"
                    ></el-input>
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="签署时间" prop="signedTime">
                    <el-date-picker
                      v-model="formData.signedTime"
                      type="date"
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      style="width: 250px"
                      placeholder="选择登记日期"
                    ></el-date-picker>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td>
                  <el-form-item label="所属部门" prop="depart">
                    <t-form-tree
                      v-model="formData.depart"
                      :treeData="departData"
                      :defaultProps="{
                        children: 'children',
                        label: 'departName',
                      }"
                      :showTop="false"
                      disabled
                      placeholder="自动关联"
                    />
                  </el-form-item>
                </td>
                <td colspan="2">
                  <el-form-item label="合同签署单位" prop="contractSigningUnit">
                    <el-input
                      :value="formData.clienteleName"
                      placeholder="请选择合同签署单位"
                      style="width: 783px"
                      readonly
                      @click.native="selectSpplier"
                    ></el-input>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td>
                  <el-form-item label="采购方联系人" prop="purchaseContacts">
                    <el-input
                      v-model="formData.purchaseContacts"
                      placeholder="请收入采购联系人"
                      style="width: 250px"
                    ></el-input>
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="联系方式" prop="principalMobile">
                    <el-input
                      v-model="formData.principalMobile"
                      placeholder="请输入联系方式"
                      style="width: 250px"
                    ></el-input>
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="采购人" prop="purchasePrincipalName">
                    <el-input
                      v-model="formData.purchasePrincipalName"
                      readonly
                      style="width: 250px"
                      placeholder="请选择采购人"
                      @click.native="selectUserData"
                    ></el-input>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td colspan="3">
                  <el-form-item label="付款条件" prop="collectionTerms">
                    <el-input
                      v-model="formData.collectionTerms"
                      type="textarea"
                      rows="3"
                      placeholder="请输入内容"
                      maxlength="1000"
                      style="width: 1320px"
                    ></el-input>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td colspan="3">
                  <el-form-item label="备注" prop="remarks">
                    <el-input
                      v-model="formData.remarks"
                      type="textarea"
                      rows="3"
                      placeholder="请输入内容"
                      maxlength="1000"
                      style="width: 1320px"
                    ></el-input>
                  </el-form-item>
                </td>
              </tr>
            </table>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="费用明细" name="third">
          <ExpenseDetail
            ref="expenseDetailRef"
            :detailList="detailList"
            :budgetId="formData.budgetId"
            :contractId="formData.id"
            @updateMoney="updateMoney"
          />
        </el-tab-pane>
      </el-tabs>
      <UploadLargeFileFdfsPopup ref="UploadLargeFileFdfsPopup" />
    </div>
    <div slot="footer" class="dialog-footer" style="text-align: center">
      <el-button type="danger" @click="closeBtn">关闭</el-button>
      <el-button type="primary" @click="saveBtn">保存</el-button>
    </div>
    <ProjectListPopup
      ref="projectListPopupRef"
      @getProjectInfo="getProjectInfo"
    />
    <!-- 合同签署单位选择 -->
    <SelectSupplierDialog ref="selectSupplierRef" @getInfo="getSupplierData" />
    <!-- 预算详情 -->
    <BudgetDetail ref="budgetDetailRef" />
    <!-- 人员选择 -->
    <VabUserListDialog ref="userListPopupRef" @callBackData="getUserInfo" />
  </el-dialog>
</template>

<script>
  import { mapGetters } from 'vuex'
  import { parseTime, genUUID } from '@/utils/th_utils'
  import { constantsExpose } from '@/utils/constantsExpose.js'
  import ProjectListPopup from '@/views/common/ProjectListPopup.vue'
  import SelectSupplierDialog from '../common/SelectSupplierDialog.vue'
  import ExpenseDetail from '../common/ExpenseDetail.vue'
  import UploadLargeFileFdfsPopup from '@/views/common/UploadLargeFileFdfsPopup.vue'
  import BudgetDetail from '@/views/financialManagement/budgetManagement/components/budgetApplicationDetail.vue'
  import {
    saveContractData,
    checkSRSave,
    checkContractName,
    getDataAndDetail,
  } from '@/api/contract'
  import {
    getDataList as getBudgetList,
    getBudgetInfoIncludeDetails,
    getBudgetData,
  } from '@/api/financialManagement/budgetManagement'
  export default {
    inheritAttrs: false,
    components: {
      ProjectListPopup,
      SelectSupplierDialog,
      UploadLargeFileFdfsPopup,
      ExpenseDetail,
      BudgetDetail,
    },
    props: {
      departData: {
        type: Array,
        default() {
          return []
        },
      },
    },
    data() {
      const validateContractName = async (rule, value, callback) => {
        const data = {
          isAdd: this.formData.isAdd,
          contractName: this.formData.contractName,
        }
        if (!this.formData.isAdd) {
          data.id = this.formData.id
        }
        await checkContractName(data).then((response) => {
          if (response.result === 200) {
            callback()
          } else {
            callback(new Error('合同名称已存在，请修改!'))
          }
        })
      }
      return {
        dialogDetailVisible: false,
        formloading: false,
        formData: {},
        defaultProps: {
          children: 'children',
          label: 'label',
        },
        rules: {
          recordDate: [
            { required: true, message: '请选择登记日期', trigger: 'blur,change' },
          ],
          projectCode: [
            { required: true, message: '请选择项目', trigger: 'blur' },
          ],
          contractName: [
            { required: true, message: '请输入合同名称', trigger: 'blur' },
            { validator: validateContractName, trigger: 'blur' }
          ],
          contractSigningUnit: [
            { required: true, message: '请选择合同签署单位', trigger: 'change' },
          ],
          signedTime: [
            { required: true, message: '请选择签署时间', trigger: 'change' },
          ],
          purchaseContacts: [
            { required: true, message: '请输入采购方联系人', trigger: 'blur' },
          ],
          collectionTerms: [
            { required: true, message: '请输入付款条件', trigger: 'blur' },
          ]
        },
        activeName: 'first',
        detailList: [], // 费用明细
      }
    },
    computed: {
      ...mapGetters({
        departList: 'user/departList',
        userName: 'user/userName',
        userId: 'user/userId',
      }),
      pageTitle() {
        return this.formData.isAdd ? '新增合同' : '编辑合同'
      },
    },
    methods: {
      async showDialog(row) {
        this.dialogDetailVisible = true
        this.formloading = true
        this.formData = { ...row }
        if (this.formData.isAdd) {
          this.init()
          this.formData.selectType = row.selectType
          this.formData.createDepart = this.departList[0].id
          this.formData.createDepartName = this.departList[0].departName
          this.formData.purchasePrincipal = this.userId
          this.formData.purchasePrincipalName = this.userName
        } else {
          // 编辑时处理业务类型
          if (!this.formData.contractRealMoney) {
            this.formData.contractRealMoney = this.formData.contractMoney
          }
          const params = {
            id: this.formData.id,
            systemId: constantsExpose.SYSTEM_ID,
          }
          const { result } = await getDataAndDetail(params)
          this.detailList = result.detailList
        }
        this.formloading = false
        this.$nextTick(() => {
          this.$refs.UploadLargeFileFdfsPopup.getParamsData({
            bizId: this.formData.id,
            bizCode: 'contractManagement_projectProcurementContract',
          })
          this.$refs.dataForm.clearValidate()
        })
      },
      beforeLeaveTab(activeName, oldActiveName) {
        if (oldActiveName == 'first') {
          return new Promise((resolve, reject) => {
            this.$refs.dataForm.validate((valid) => {
              if(!valid) {
                this.$baseMessage(
                  '请先完善基本信息',
                  'warning',
                  'vab-hey-message-warning'
                )
                reject()
              } else {
                resolve()
              }
            })
          })
        }
      },
      updateMoney(money) {
        this.formData.contractMoney = money
      },
      selectContractProject() {
        this.$refs.projectListPopupRef.showDialog({
          selectIds: this.formData.projectId,
          projectName: this.formData.projectName
        })
      },
      saveBtn() {
        const purchaseData = { ...this.formData }
        const detailList = this.$refs.expenseDetailRef.tableData
        this.$refs.dataForm.validate( async (valid) => {
          if (valid) {
            const paramsData = {
              ...purchaseData,
              operationCode: 'XMHT',
              detailList,
            }
            if (!paramsData.budgetId) {
              this.$baseMessage(
                '合同所属项目未关联预算!',
                'warning',
                'vab-hey-message-warning'
              )
              return
            }
            if (detailList.length === 0) {
              this.$baseMessage(
                '未导入合同费用明细!',
                'warning',
                'vab-hey-message-warning'
              )
              return
            }
            if(!paramsData.contractMoney || paramsData.contractMoney * 100 === 0) {
              this.$baseMessage(
                '费用明细中费用项目金额不能为0',
                'warning',
                'vab-hey-message-warning'
              )
              return
            }
            this.formloading = true
            await saveContractData(paramsData).then((res) => {
              if (res.code === 200) {
                this.$baseMessage('保存成功!', 'success', 'vab-hey-message-success')
                this.$emit('refreshPage')
                this.formloading = false
                this.formData = {}
                this.dialogDetailVisible = false
              }
            })
          }
        })
      },
      closedDialog() {
        this.formData = {}
        this.detailList = []
        this.activeName = 'first'
      },
      closeBtn() {
        this.$refs.UploadLargeFileFdfsPopup.closeDelImg()
        this.$refs.dataForm.resetFields()
        this.dialogDetailVisible = false
      },
      filterNode(value, data) {
        if (!value) return true
        return data.label.indexOf(value) !== -1
      },
      async getProjectInfo(data) {
        if (data.length > 0 && data[0].id !== this.formData.projectId) {
          const dataObj = data[0]
          const {
            id,
            projectName,
            serialNumber,
            depart,
            departCode,
            departName,
          } = dataObj
          if (this.formData.budgetId && id !== this.formData.budgetId) {
            this.formData.budgetId = ''
            this.detailList = []
          }
          this.formData = {
            ...this.formData,
            projectId: id,
            projectName,
            projectCode: serialNumber,
            depart,
            departCode,
            departName,
          }
          this.$refs.dataForm.clearValidate('projectCode')
          const params = {
            projectId: id,
            approvalResult: '2',
            allPermissions: true,
          }
          const { result } = await getBudgetList(params)
          if (result.length > 0) {
            const { id } = result[0]
            this.formData.budgetId = id
          }
        }
      },
      // 项目预算详情
      async viewBudgetDetail() {
        const { result } = await getBudgetInfoIncludeDetails(this.formData.budgetId)
        const budgetData = result.budget[0]
        this.$refs.budgetDetailRef.showDialog(budgetData)
      },
      selectSpplier() {
        // if(!this.formData.budgetId) {
        //   this.$baseMessage(
        //     '请先选择合同所属项目!',
        //     'warning',
        //     'vab-hey-message-warning'
        //   )
        //   return
        // }
        this.$refs.selectSupplierRef.showDialog()
      },
      getSupplierData(data) {
        if(data.length > 0) {
          this.formData.clienteleName = data[0].name
          this.formData.contractSigningUnit = data[0].id
        }
      },
      selectUserData() {
        this.$refs.userListPopupRef.showUserDialog({})
      },
      getUserInfo(data) {
        if (data.length > 0) {
          this.formData.purchasePrincipal = data[0].id
          this.formData.purchasePrincipalName = data[0].userName
        }
      },
      init() {
        this.formData = {
          id: genUUID(),
          isAdd: true,
          selectType: '',
          serialNumber: '',
          recordDate: parseTime(new Date(), '{y}-{m}-{d}'),
          projectId: '',
          projectName: '',
          projectCode: '',
          contractName: '',
          contractCode: '',
          contractMoney: '',
          contractRealMoney: '',
          contractSigningUnit: '',
          clienteleName: '',
          signedTime: parseTime(new Date(), '{y}-{m}-{d}'),
          contractNum: '',
          depart: '',
          departName: '',
          departCode: '',
          remarks: '',
          createDepart: '',
          createDepartName: '',
          purchasePrincipal: '',
          purchasePrincipalName: '',
          detailList: [],
          budgetId: '',
          principalMobile: '',
          serialNumberTable:'contract',
          viewUser: this.userId,
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  ::v-deep .form-table {
    .el-input__inner {
      padding-right: 0;
      &::-webkit-inner-spin-button {
        margin: 0;
        -webkit-appearance: none !important;
      }
    }
    .el-form-item__error {
      position: absolute !important;
      top: 45px;
      left: 10px;
    }
  }
  .view-budget {
    position: absolute;
    left: 405px;
    top: 30px;
    font-size: 12px;
    color: #1e7dff;
    cursor: pointer;
    padding-left: 5px;
  }
</style>
