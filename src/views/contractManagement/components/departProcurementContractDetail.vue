<template>
  <el-dialog
    v-drag
    append-to-body
    center
    :close-on-click-modal="false"
    title="合同详情"
    top="5vh"
    :visible.sync="dialogDetailVisible"
    width="1650px"
    @closed="closedDialog"
  >
    <div style="display: flex; max-height: 75vh; overflow-y: scroll">
      <div style="flex: 1">
        <el-tabs type="card">
          <el-tab-pane label="基本信息">
            <el-descriptions
              border
              class="descriptions-box"
              :column="3"
              size="medium"
            >
              <el-descriptions-item>
                <template slot="label">单号</template>
                {{ formData.serialNumber }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">登记部门</template>
                {{ formData.createDepartName }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">登记日期</template>
                {{ formData.recordDate | dateformat('YYYY-MM-DD') }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">合同编号</template>
                {{ formData.contractCode }}
              </el-descriptions-item>
              <el-descriptions-item :span="2">
                <template slot="label">合同名称</template>
                {{ formData.contractName }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">合同金额(元)</template>
                {{ formData.contractMoney | currencyFormat }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">合同份数</template>
                {{ formData.contractNum }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">签署时间</template>
                {{ formData.signedTime | dateformat('YYYY-MM-DD') }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">所属部门</template>
                {{ formData.departName }}
              </el-descriptions-item>
              <el-descriptions-item :span="2">
                <template slot="label">合同签署单位</template>
                {{ formData.clienteleName }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">采购方联系人</template>
                {{ formData.purchaseContacts }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">联系电话</template>
                {{ formData.principalMobile }}
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">采购人</template>
                {{ formData.purchasePrincipalName }}
              </el-descriptions-item>
              <el-descriptions-item :span="3">
                <template slot="label">付款条件</template>
                <div v-html="$formatText(formData.collectionTerms)"></div>
              </el-descriptions-item>
              <el-descriptions-item :span="3">
                <template slot="label">备注</template>
                {{ formData.remarks }}
              </el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>
          <el-tab-pane label="费用明细">
            <el-table border :data="formData.detailList" size="medium" stripe>
              <el-table-column
                align="center"
                label="费用项目"
                prop="costName"
              />
              <el-table-column align="center" label="金额(元)" prop="money">
                <template #default="{ row }">
                  {{ row.money | currencyFormat }}
                </template>
              </el-table-column>
              <el-table-column align="center" label="备注" prop="remarks" />
              <el-table-column align="center" label="操作" width="150">
                <template #default="{ row, index }">
                  <el-button
                    icon="el-icon-link"
                    type="success"
                    @click="
                      showAttchFileList(
                        row,
                        'contractManagement_departProcurementContract_detail'
                      )
                    "
                  >
                    附件
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
        <!-- 附件上传 -->
        <UploadLargeFileFdfsPopupDetail
          ref="UploadLargeFileFdfsPopupDetail"
          style="margin-top: 20px"
        />
        <!-- 审批 -->
        <VabApproveFlow
          v-if="isApproval"
          ref="ApprovalFlow"
          @callBackRefresh="callBackRefresh"
        />
      </div>
      <div v-if="showApprovalRecords && formData.approvalResult !== '0'">
        <!-- 审批记录 -->
        <VabWorkFlowApproveRecDlg ref="workFlwApprovalRecDlg" />
      </div>
    </div>
    <div slot="footer" v-if="!isApproval" class="dialog-footer">
      <el-button type="danger" @click="dialogDetailVisible = false">
        关闭
      </el-button>
    </div>
    <!-- 附件 -->
    <CommonUploadListPopup ref="CommonUploadListPopup" />
  </el-dialog>
</template>

<script>
  import UploadLargeFileFdfsPopupDetail from '@/views/common/UploadLargeFileFdfsPopupDetail.vue'
  import { getDataAndDetail } from '@/api/contract'
  import { constantsExpose } from '@/utils/constantsExpose.js'
  import CommonUploadListPopup from '@/views/common/CommonUploadListPopup.vue'
  export default {
    components: {
      UploadLargeFileFdfsPopupDetail,
      CommonUploadListPopup,
    },
    data() {
      return {
        dialogDetailVisible: false,
        formData: {},
        isApproval: false,
        selectType: '',
        showApprovalRecords: true, //详情状态默认展示审批记录
      }
    },
    methods: {
      async showApprovalFlowByParams(bizKey, row, paramsMap) {
        await this.showDialog(row)
        this.isApproval = true
        this.$nextTick(() => {
          this.$refs.ApprovalFlow.showApprovalFlowByParams(
            bizKey,
            row,
            paramsMap
          )
        })
      },
      callBackRefresh() {
        this.$parent.fetchData()
        this.dialogDetailVisible = false
      },
      closedDialog() {
        this.formData = {}
        this.isApproval = false
        this.selectType = ''
      },
      async showDialog(row) {
        this.isApproval = false
        const params = {
          id: row.id,
          systemId: constantsExpose.SYSTEM_ID,
          selectType: 'organ_purchase',
        }
        const { result } = await getDataAndDetail(params)
        this.formData = { ...result }
        this.selectType = this.formData.selectType
        this.dialogDetailVisible = true
        this.$nextTick(() => {
          if (this.formData.approvalResult !== '0') {
            this.$refs.workFlwApprovalRecDlg.getTaskProcessListByBizKey(
              this.formData.id
            )
          }
          this.$refs.UploadLargeFileFdfsPopupDetail.getParamsData({
            bizId: this.formData.id,
            bizCode: 'contractManagement_departProcurementContract',
            isShow: true,
          })
        })
      },
      // 查看附件
      showAttchFileList(row, bizCode) {
        const data = {
          bizId: row.id,
          bizCode: bizCode,
          isShow: false,
        }
        this.$refs.CommonUploadListPopup.showUploadListDialog(data)
      },
    },
  }
</script>

<style lang="scss" scoped>
  ::v-deep .descriptions-box {
    .el-descriptions-item__cell {
      min-width: 150px;
    }
  }
</style>
