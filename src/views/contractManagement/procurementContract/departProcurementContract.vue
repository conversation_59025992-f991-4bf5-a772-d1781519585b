<template>
  <div
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="80px"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item label="所属部门" label-width="110px" prop="departName">
            <el-input
              v-model="queryForm.departName"
              clearable
              placeholder="请输入所属部门"
            />
          </el-form-item>
          <el-form-item
            label="合同名称"
            label-width="110px"
            prop="contractName"
          >
            <el-input
              v-model="queryForm.contractName"
              clearable
              placeholder="请输入合同名称"
            />
          </el-form-item>
          <el-form-item
            label="合同金额(元)"
            prop="contractMoney"
            label-width="110px"
          >
            <el-input
              v-model="queryForm.contractMoney"
              clearable
              placeholder="请输入合同金额"
            />
          </el-form-item>
          <el-form-item label="审批状态" prop="approvalResult" label-width="110px">
            <el-select
              ref="selectFlow"
              v-model="queryForm.approvalResult"
              filterable
              placeholder="请选择审批状态"
              
            >
              <el-option option value="">所有状态</el-option>
              <el-option
                v-for="item in approvalResultNameList"
                :key="item.id"
                :label="item.approvalResultName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="签署单位"
            v-show="!fold"
            label-width="110px"
            prop="clienteleName"
          >
            <el-input
              v-model="queryForm.clienteleName"
              clearable
              placeholder="请输入签署单位"
            />
          </el-form-item>

          <el-form-item
            label="采购人"
            prop="purchasePrincipalName"
            v-show="!fold"
            label-width="110px"
          >
            <el-input
              v-model="queryForm.purchasePrincipalName"
              clearable
              placeholder="请输入采购人"
            />
          </el-form-item>
          <el-form-item
            label="登记人"
            prop="createByName"
            label-width="110px"
            v-show="!fold"
          >
            <el-input
              v-model="queryForm.createByName"
              clearable
              placeholder="请输入登记人"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              icon="el-icon-search"
              native-type="submit"
              type="primary"
              @click="handleQuery"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-right"
              @click.native="resetFormPage"
            >
              重置
            </el-button>
            <el-button
              type="text"
              @click="handleFold"
              style="margin: 0 0 0 10px !important"
            >
              <span v-if="fold">展开</span>
              <span v-else>合并</span>
              <vab-icon
                class="vab-dropdown"
                :class="{ 'vab-dropdown-active': fold }"
                icon="arrow-up-s-line"
              />
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
      <vab-query-form-left-panel :span="12">
        <el-button
          icon="el-icon-plus"
          type="primary"
          @click="handleAdd"
          v-permissions="{ permission: ['departProcurementContract:add'] }"
        >
          添加
        </el-button>
        <!-- <el-button
          icon="el-icon-delete"
          type="danger"
          @click="batchDelete"
          v-permissions="{ permission: ['departProcurementContract:del'] }"
        >
          批删
        </el-button> -->
        <el-button
          icon="el-icon-download"
          type="warning"
          :disabled="exportLoading"
          @click.native="exportBtn('采购合同列表.xlsx')"
        >
          导出
        </el-button>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="12">
        <el-button
          style="margin: 0 10px 10px 0 !important"
          type="primary"
          @click="clickFullScreen"
        >
          <vab-icon
            :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'"
          />
          表格全屏
        </el-button>
        <el-popover
          ref="popover"
          popper-class="custom-table-checkbox"
          trigger="hover"
        >
          <el-radio-group v-model="lineHeight">
            <el-radio label="medium">大</el-radio>
            <el-radio label="small">中</el-radio>
            <el-radio label="mini">小</el-radio>
          </el-radio-group>
          <template #reference>
            <el-button style="margin: 0 10px 10px 0 !important" type="primary">
              <vab-icon icon="line-height" />
              表格尺寸
            </el-button>
          </template>
        </el-popover>
        <el-popover popper-class="custom-table-checkbox" trigger="hover">
          <el-checkbox-group v-model="checkList">
            <vab-draggable v-bind="dragOptions" :list="columns">
              <div v-for="(item, index) in columns" :key="item + index">
                <vab-icon icon="drag-drop-line" />
                <el-checkbox
                  :disabled="item.disableCheck === true"
                  :label="item.label"
                >
                  {{ item.label }}
                </el-checkbox>
              </div>
            </vab-draggable>
          </el-checkbox-group>
          <template #reference>
            <el-button
              icon="el-icon-setting"
              style="margin: 0 0 10px 0 !important"
              type="primary"
            >
              可拖拽列设置
            </el-button>
          </template>
        </el-popover>
      </vab-query-form-right-panel>
    </vab-query-form>
    <el-table
      ref="tableSort"
      v-loading="listLoading"
      border
      :data="list"
      :height="height - 20"
      :size="lineHeight"
      stripe
      @selection-change="setSelectRows"
    >
      <el-table-column
        align="center"
        type="selection"
        width="55"
        :selectable="selectableFun"
      />
      <el-table-column align="center" label="序号" width="60">
        <template #default="{ $index }">
          {{ getIndex($index) }}
        </template>
      </el-table-column>
      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        :align="item.center ? item.center : 'center'"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable ? item.sortable : false"
        :min-width="item.width ? item.width : 'auto'"
      >
        <template #default="{ row }">
          <span v-if="item.label === '签署时间'">
            {{ row[item.prop] | dateformat('YYYY-MM-DD') }}
          </span>
          <span v-else-if="item.label === '采购合同金额(元)'">
            {{ row[item.prop] | currencyFormat }}
          </span>
          <span v-else-if="item.label === '项目名称'">部门机关采购</span>
          <span v-else-if="item.label === '审批状态'">
            <el-tag :type="fmtFlowType(row)">
              {{ row[item.prop] }}
            </el-tag>
          </span>
          <span v-else>
            {{ row[item.prop] }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="320" fixed="right">
        <template #default="{ row }">
          <el-button
            icon="el-icon-edit"
            style="margin: 0 10px 10px 0 !important"
            type="primary"
            @click="handleEdit(row)"
            :disabled="isDisabledEditFun(row)"
            v-permissions="{ permission: ['departProcurementContract:update'] }"
          >
            编辑
          </el-button>
          <el-button
            v-if="isAssignee(row)"
            icon="el-icon-edit-outline"
            style="margin: 0 10px 10px 0 !important"
            type="warning"
            @click.native.prevent="showApprovalDlg(row)"
          >
            审批
          </el-button>
          <el-button
            icon="el-icon-view"
            style="margin: 0 10px 10px 0 !important"
            type="success"
            @click="handleDetail(row)"
          >
            详情
          </el-button>
          <el-dropdown>
            <el-button size="mini" type="success">
              <i class="el-icon-more" />
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                @click.native.prevent="
                  showReport(
                    row,
                    '/ReportServer?reportlet=/oaNew/contract/JGHT.cpt'
                  )
                "
              >
                <i class="el-icon-s-order" />
                查看报表
              </el-dropdown-item>
              <el-dropdown-item
                v-if="isStartFlowFlag(row)"
                @click.native.prevent="startWorkFlow(row)"
              >
                <i class="el-icon-video-play" />
                启动流程
              </el-dropdown-item>
              <el-dropdown-item
                v-if="isDeleteFlowFlag(row)"
                @click.native.prevent="deleteWorkFlow(row)"
              >
                <i class="el-icon-refresh-left" />
                撤回流程
              </el-dropdown-item>
              <el-dropdown-item
                v-if="isDeleteFlag(row)"
                v-permissions="{
                  permission: ['departProcurementContract:del'],
                }"
                @click.native.prevent="handleDelete(row)"
                style="color: #fd5353"
              >
                <i class="el-icon-delete" />
                删除
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
      style="position: static; margin-top: 45px; margin-bottom: 10px"
    />

    <!-- 新增修改 -->
    <TableEdit
      ref="tableEditRef"
      :departData="allDepartList"
      @refreshPage="resetFormPage"
    />
    <!-- 详情 -->
    <TableDetail ref="tableDetail" />
    <!-- 流程启动动态审批条件处理 -->
    <VabStartFlowProcess ref="VabStartFlowProcess" />
    <VabTableExport ref="tableExport" @refreshPage="resetFormPage" />
  </div>
</template>

<script>
  import tableMix from '@/views/mixins/table'
  import TableEdit from '../components/departProcurementContractEdit.vue'
  import TableDetail from '../components/departProcurementContractDetail.vue'
  import {
    getContracPageData,
    deleteContract,
    exportData,
  } from '@/api/contract'
  import { reportAddr } from '@/utils/constants'
  export default {
    name: 'departProcurementContract',
    mixins: [tableMix],
    components: {
      TableEdit,
      TableDetail,
    },
    data() {
      return {
        formMaxHeight: 2,
        hasCard: true,
        queryForm: {
          selectType: 'organ_purchase',
          departName: '',
          contractName: '',
          contractMoney: '',
          clienteleName: '',
          purchasePrincipalName: '',
          createByName: '',
        },
        checkList: [
          '合同单号',
          '合同名称',
          '采购合同金额(元)',
          '签署单位',
          '签署时间',
          '项目所属部门',
          '审批状态',
        ],
        columns: [
          {
            label: '合同单号',
            width: '250px',
            prop: 'serialNumber',
          },
      
          {
            label: '合同名称',
            width: '200px',
            prop: 'contractName',
          },
          {
            label: '采购合同金额(元)',
            width: '140px',
            prop: 'contractMoney',
          },
          {
            label: '签署单位',
            width: '200px',
            prop: 'clienteleName',
          },

          {
            label: '签署时间',
            width: '120px',
            prop: 'signedTime',
          },
          {
            label: '项目所属部门',
            width: '200px',
            prop: 'departName',
          },
          {
            label: '审批状态',
            width: '100px',
            prop: 'approvalResultName',
          },
        ],
      }
    },
    computed: {
      flowVariables() {
        return {
          amount: this.flowData.contractMoney,
        }
      },
    },
    created() {
      this.fetchData()
    },
    methods: {
      fetchData() {
        this.listLoading = true

        let queryForm = {
          ...this.queryForm,
          selectType: 'organ_purchase',
          curPage: this.pageInfo.curPage,
          pageSize: this.pageInfo.pageSize,
        }
        getContracPageData(queryForm).then((res) => {
          this.listLoading = false
          const {
            result: { total, records },
          } = res
          this.list = records
          this.pageInfo.total = Number(total)
        })
      },
      filterNode(value, data) {
        if (!value) return true
        return data.label.indexOf(value) !== -1
      },
      resetFormPage() {
        this.resetForm('form')
      },
      // 编辑项目
      handleEdit(row) {
        this.$refs['tableEditRef'].showDialog({ isAdd: false, ...row })
      },
      handleDetail(row) {
        this.$refs['tableDetail'].showDialog(row)
      },
      // 添加项目
      handleAdd() {
        this.$refs['tableEditRef'].showDialog({
          isAdd: true,
          selectType: 'organ_purchase',
        })
      },
      // 删除项目
      handleDelete(row) {
        if (row.id) {
          this.$baseConfirm('你确定要删除当前合同吗', null, async () => {
            deleteContract({ id: row.id })
              .then(() => {
                this.pageInfo.curPage = 1
                this.batchDeleteImg(row.id, 'contractManagement')
                this.fetchData()
                this.$baseMessage(
                  '删除成功!',
                  'success',
                  'vab-hey-message-success'
                )
              })
              .catch(() => {
                this.$baseMessage('删除失败!', 'error', 'vab-hey-message-error')
              })
          })
        }
      },
      // 批删除项目
      batchDelete() {
        if (this.selectRows.length == 0) {
          this.$baseMessage(
            '请选中最少一条记录!',
            'error',
            'vab-hey-message-error'
          )
          return
        }
        const idArr = this.selectRows.map((item) => item.id).join(',')
        this.$baseConfirm('你确定要删除选中合同吗', null, async () => {
          deleteContract({ id: idArr })
            .then(() => {
              this.pageInfo.curPage = 1
              this.batchDeleteImg(idArr, 'contractManagement')
              this.fetchData()
              this.$baseMessage(
                '删除成功!',
                'success',
                'vab-hey-message-success'
              )
            })
            .catch(() => {
              this.$baseMessage('删除失败!', 'error', 'vab-hey-message-error')
            })
        })
      },
      exportBtn(excelName) {
        this.queryForm.excelName = excelName
        this.$refs['tableExport'].showDialog(this.columns)
      },
      exportData(columns) {
        this.exportLoading = true
        exportData({ ...this.queryForm, ...columns }).then((response) => {
          const link = document.createElement('a')
          link.download = this.queryForm.excelName
          link.href = window.URL.createObjectURL(new Blob([response]))
          document.body.appendChild(link)
          link.click()
          link.download = ''
          document.body.removeChild(link)
          URL.revokeObjectURL(response)
        })
        this.exportLoading = false
      },
      // exportBtn(excelName) {
      //   this.exportLoading = true
      //   this.queryForm.bizKey = this.$route.name
      //   this.queryForm.excelName = excelName
      //   exportData(this.queryForm).then((response) => {
      //     const link = document.createElement('a')
      //     link.download = excelName
      //     link.href = window.URL.createObjectURL(new Blob([response]))
      //     document.body.appendChild(link)
      //     link.click()
      //     link.download = ''
      //     document.body.removeChild(link)
      //     URL.revokeObjectURL(response)
      //   })
      //   this.exportLoading = false
      // },
      // 限制勾选
      selectableFun(row) {
        return this.isDeleteFlag(row)
      },
    },
  }
</script>

<style lang="scss" scoped>
  $base: '.depart-management';
  #{$base}-container {
    padding: 0 !important;
    background: $base-color-background !important;

    &.vab-fullscreen {
      padding: 20px !important;
    }
  }
</style>
