<template>
  <el-dialog
    v-drag
    append-to-body
    center
    :close-on-click-modal="false"
    title="客户详情"
    top="5vh"
    :visible.sync="dialogDetailVisible"
    width="1650px"
  >
    <div style="display: flex; max-height: 75vh; overflow-y: scroll">
      <div style="flex: 1">
        <el-descriptions
          v-loading="formLoading"
          border
          class="margin-top"
          :column="3"
          size="medium"
          title="基本信息"
        >
          <el-descriptions-item>
            <template slot="label">单号</template>
            {{ formData.serialNumber }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">登记人</template>
            {{ formData.createByName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">登记部门</template>
            {{ formData.createDepartName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">登记日期</template>
            {{ formData.recordDate | dateformat('YYYY-MM-DD') }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">公司类型</template>
            {{ formData.clienteleTypeName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">所属行业</template>
            {{ formData.industryName }}
          </el-descriptions-item>
          <el-descriptions-item :span="3">
            <template slot="label">公司名称</template>
            {{ formData.clienteleName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">客户联系人</template>
            {{ formData.customerContact }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">客户职务</template>
            {{ formData.customerPost }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">客户联系方式</template>
            {{ formData.customerContactInformation }}
          </el-descriptions-item>
          <el-descriptions-item :span="3">
            <template slot="label">办公地址</template>
            {{ formData.officeAddress }}
          </el-descriptions-item>
        </el-descriptions>
        <el-descriptions
          v-loading="formLoading"
          border
          class="margin-two"
          :column="3"
          size="medium"
          style="margin: 10px 0 20px 0"
          title="开票信息"
        >
          <el-descriptions-item :span="2">
            <template slot="label">开户名</template>
            {{ formData.accountName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">税号</template>
            {{ formData.taxId }}
          </el-descriptions-item>
          <el-descriptions-item :span="2">
            <template slot="label">注册地址</template>
            {{ formData.companyAddress }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">电话</template>
            {{ formData.phone }}
          </el-descriptions-item>
          <el-descriptions-item :span="2">
            <template slot="label">开户银行</template>
            {{ formData.bank }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">银行账户</template>
            {{ formData.bankAccount }}
          </el-descriptions-item>
          <el-descriptions-item :span="3">
            <template slot="label">备注</template>
            {{ formData.remark }}
          </el-descriptions-item>
        </el-descriptions>
        <!-- 附件上传 -->
        <UploadLargeFileFdfsPopupDetail ref="UploadLargeFileFdfsPopupDetail" />
        <!-- 审批 -->
        <VabApproveFlow
          v-if="isApproval"
          ref="ApprovalFlow"
          @callBackRefresh="callBackRefresh"
        />
      </div>
      <div v-if="formData.approvalResult !== '0'">
        <!-- 审批记录 -->
        <VabWorkFlowApproveRecDlg ref="workFlwApprovalRecDlg" />
      </div>
    </div>
    <div slot="footer" v-if="!isApproval" class="dialog-footer">
      <el-button type="danger" @click="dialogDetailVisible = false">
        关闭
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import UploadLargeFileFdfsPopupDetail from '@/views/common/UploadLargeFileFdfsPopupDetail.vue'
  export default {
    components: {
      UploadLargeFileFdfsPopupDetail,
    },
    props: {},
    data() {
      return {
        dialogDetailVisible: false,
        formData: {},
        formLoading: false,
        isApproval: false,
      }
    },
    methods: {
      showApprovalFlowByParams(bizKey, row, params) {
        this.showDialog(row)
        this.isApproval = true
        this.$nextTick(() => {
          this.$refs.ApprovalFlow.showApprovalFlowByParams(bizKey, row, params)
        })
      },
      callBackRefresh() {
        this.$parent.fetchData()
        this.dialogDetailVisible = false
      },
      showDialog(row) {
        this.isApproval = false
        this.formData = { ...row }
        this.$nextTick(() => {
          if (this.formData.approvalResult !== '0') {
            this.$refs.workFlwApprovalRecDlg.getTaskProcessListByBizKey(
              this.formData.id
            )
          }
          this.$refs.UploadLargeFileFdfsPopupDetail.getParamsData({
            bizId: this.formData.id,
            bizCode: this.$route.name,
            isShow: true,
          })
        })
        this.dialogDetailVisible = true
      },
    },
  }
</script>
<style lang="scss" scoped>
  .margin-two {
    padding-top: 10px;
  }
</style>
