<template>
  <el-dialog
    v-drag
    append-to-body
    :before-close="close"
    center
    :close-on-click-modal="false"
    :title="pageTitle"
    top="5vh"
    :visible.sync="dialogFormVisible"
    width="1420px"
  >
    <div style="max-height: 75vh; overflow-y: scroll; overflow-x: hidden">
      <el-form
        ref="dataForm"
        v-loading="formLoading"
        :inline="true"
        label-position="right"
        label-width="140px"
        :model="formData"
        :rules="rules"
      >
        <table class="form-table">
          <tr class="title">
            <td colspan="3">基本信息</td>
          </tr>
          <tr>
            <td>
              <el-form-item label="单号" prop="serialNumber">
                <el-input
                  v-model="formData.serialNumber"
                  disabled
                  placeholder="自动生成"
                />
              </el-form-item>
            </td>
            <td>
              <el-form-item label="登记人" prop="createBy">
                <el-input
                  v-model="formData.createByName"
                  disabled
                  style="width: 250px"
                />
              </el-form-item>
            </td>
            <td>
              <el-form-item label="登记部门" prop="createDepart">
                <el-input
                  v-model="formData.createDepartName"
                  disabled
                  placeholder="自动生成"
                  style="width: 250px"
                />
              </el-form-item>
            </td>
          </tr>
          <tr>
            <td>
              <el-form-item label="登记日期" prop="createTime">
                <el-date-picker
                  v-model="formData.createTime"
                  placeholder="请选择登记日期"
                  style="width: 250px"
                  type="date"
                />
              </el-form-item>
            </td>
            <td>
              <el-form-item label="付款方式" prop="paymentMethod">
                <el-select
                  v-model="formData.paymentMethod"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in paymentMethodType"
                    :key="item.id"
                    :label="item.dictName"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </td>
            <td>
              <el-form-item label="注册资金（万元）" prop="registeredCapital">
                <el-input
                  v-model="formData.registeredCapital"
                  oninput="value=value.replace(/[^\d.]/g, '')"
                  placeholder="请输入注册资金"
                  style="width: 250px"
                />
              </el-form-item>
            </td>
          </tr>
          <tr>
            <td colspan="3">
              <el-form-item label="公司名称" prop="name">
                <el-input
                  v-model="formData.name"
                  placeholder="请输入公司名称"
                  style="width: 1158px"
                  @change="handleClienteleNameChange"
                />
              </el-form-item>
            </td>
          </tr>
          <tr>
            <td>
              <el-form-item label="供应商联系人" prop="linkmanName">
                <el-input
                  v-model="formData.linkmanName"
                  placeholder="请输入供应商联系人"
                />
              </el-form-item>
            </td>
            <td>
              <el-form-item label="供应商职务" prop="jobTitle">
                <el-input
                  v-model="formData.jobTitle"
                  placeholder="请输入供应商职务"
                />
              </el-form-item>
            </td>
            <td>
              <el-form-item label="供应商联系方式" prop="telephone">
                <el-input
                  v-model="formData.telephone"
                  placeholder="请输入供应商联系方式"
                  style="width: 250px"
                />
              </el-form-item>
            </td>
          </tr>
          <tr>
            <td colspan="3">
              <el-form-item label="账期" prop="currentAccount">
                <el-input
                  v-model="formData.currentAccount"
                  placeholder="请输入账期"
                  style="width: 1158px"
                />
              </el-form-item>
            </td>
          </tr>
          <tr>
            <td colspan="3">
              <el-form-item label="产品内容" prop="productContent">
                <el-input
                  v-model="formData.productContent"
                  placeholder="请输入产品内容"
                  :rows="3"
                  style="width: 1158px"
                  type="textarea"
                />
              </el-form-item>
            </td>
          </tr>
          <tr>
            <td colspan="3">
              <el-form-item label="办公地址" prop="locationArea">
                <el-input
                  v-model="formData.locationArea"
                  placeholder="请输入办公地址"
                  style="width: 1158px"
                />
              </el-form-item>
            </td>
          </tr>
          <tr class="title">
            <td colspan="3">开票信息</td>
          </tr>
          <tr>
            <td colspan="2">
              <el-form-item label="开户名" prop="accountName">
                <el-input
                  v-model="formData.accountName"
                  disabled
                  placeholder="请输入开户名"
                  style="width: 715px"
                />
              </el-form-item>
            </td>
            <td>
              <el-form-item label="税号" prop="taxId">
                <el-input v-model="formData.taxId" placeholder="请输入税号" />
              </el-form-item>
            </td>
          </tr>
          <tr>
            <td colspan="2">
              <el-form-item label="公司地址" prop="companyAddress">
                <el-input
                  v-model="formData.companyAddress"
                  placeholder="请输入注册地址"
                  style="width: 715px"
                />
              </el-form-item>
            </td>
            <td>
              <el-form-item label="电话" prop="phone">
                <el-input v-model="formData.phone" placeholder="请输入电话" />
              </el-form-item>
            </td>
          </tr>

          <tr>
            <td colspan="2">
              <el-form-item label="开户行" prop="bank">
                <el-input
                  v-model="formData.bank"
                  placeholder="请输入开户银行"
                  style="width: 715px"
                />
              </el-form-item>
            </td>
            <td>
              <el-form-item label="银行账户" prop="bankAccount">
                <el-input
                  v-model="formData.bankAccount"
                  placeholder="请输入银行账户"
                />
              </el-form-item>
            </td>
          </tr>
          <tr>
            <td colspan="3">
              <el-form-item label="备注" prop="remark">
                <el-input
                  v-model="formData.remark"
                  maxlength="1000"
                  placeholder="请输入内容"
                  :rows="2"
                  style="width: 1165px"
                  type="textarea"
                />
              </el-form-item>
            </td>
          </tr>
          <!--          <tr>-->
          <!--            <td colspan="3">-->
          <!--              <el-form-item label="上个版本" prop="remark">-->
          <!--                <el-link-->
          <!--                  type="primary"-->
          <!--                  @click="showDetails(formData.oldId)"-->
          <!--                >-->
          <!--                  {{ formData.oldSerialNumber }}-->
          <!--                </el-link>-->
          <!--                <span>&nbsp;&nbsp;</span>-->
          <!--              </el-form-item>-->
          <!--            </td>-->
          <!--          </tr>-->
        </table>
      </el-form>
    </div>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>
    <UploadLargeFileFdfsPopup ref="UploadLargeFileFdfsPopup" />
  </el-dialog>
</template>

<script>
  import { mapGetters } from 'vuex'
  import {
    saveData,
    checkData,
    getData,
  } from '@/api/provider/supplierAdjustments'
  import { genUUID, parseTime } from '@/utils/th_utils.js'
  import UploadLargeFileFdfsPopup from '@/views/common/UploadLargeFileFdfsPopup.vue'
  import { constantsExpose } from '@/utils/constantsExpose'

  export default {
    components: { UploadLargeFileFdfsPopup },
    props: {
      paymentMethodType: {
        type: Array,
        default() {
          return []
        },
      },
    },
    data() {
      const validateName = (rule, value, callback) => {
        const data = {
          isAdd: this.formData.isAdd,
          name: this.formData.name,
        }
        if (!this.formData.isAdd) {
          data.id = this.formData.historyId
        }
        checkData(data).then((response) => {
          if (response.result === 200) {
            callback()
          } else {
            callback(new Error('供应商已存在'))
          }
        })
      }
      const validateTelephone = (rule, value, callback) => {
        const reg = /^[0-9-]+$/
        if (reg.test(value)) {
          return callback()
        }
        callback(new Error('格式输入有误，请输入手机号或者座机号！'))
      }
      return {
        dialogFormVisible: false,
        showSupplier: false,
        formLoading: false,
        formData: {},
        rules: {
          createBy: [
            { required: true, message: '登记人为必填', trigger: 'blur' },
          ],
          name: [
            { required: true, message: '公司名称为必填', trigger: 'blur' },
            {
              validator: validateName,
              trigger: 'blur',
            },
          ],
          linkmanName: [
            { required: true, message: '联系人为必填', trigger: 'blur' },
          ],
          createTime: [
            { required: true, message: '登记日期为必填', trigger: 'blur' },
          ],
          registeredCapital: [
            { required: true, message: '注册资金为必填', trigger: 'blur' },
          ],
          jobTitle: [
            { required: true, message: '职务为必填', trigger: 'blur' },
          ],
          telephone: [
            { required: true, message: '联系方式为必填', trigger: 'blur' },
            {
              validator: validateTelephone,
              trigger: 'blur',
            },
          ],
          paymentMethod: [
            { required: true, message: '付款方式为必填', trigger: 'blur' },
          ],
          productContent: [
            { required: true, message: '产品内容为必填', trigger: 'blur' },
          ],
          bankAccount: [
            { required: true, message: '银行账户为必填', trigger: 'blur' },
          ],
          taxId: [{ required: true, message: '税号为必填', trigger: 'blur' }],
          companyAddress: [
            { required: true, message: '注册地址为必填', trigger: 'blur' },
          ],
          bank: [
            { required: true, message: '开户银行为必填', trigger: 'blur' },
          ],
          phone: [
            { required: true, message: '电话为必填', trigger: 'blur' },
            {
              validator: validateTelephone,
              trigger: 'blur',
            },
          ],
        },
      }
    },
    computed: {
      ...mapGetters({
        userId: 'user/userId',
        userName: 'user/userName',
        departList: 'acl/departList',
      }),
      pageTitle() {
        return this.formData.isAdd ? '新增供应商' : '编辑供应商'
      },
    },
    methods: {
      handleClienteleNameChange() {
        this.formData.accountName = this.formData.name
      },
      async showDialog(row) {
        this.formData = { ...row }
        if (this.formData.isAdd) {
          this.init()
        }
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs.UploadLargeFileFdfsPopup.getParamsData({
            bizId: this.formData.id,
            bizCode: this.$route.name,
          })
        })
      },
      // 初始化表单
      init() {
        this.formData = {
          id: genUUID(),
          serialNumber: '',
          createDepart: this.departList[0].id,
          createDepartName: this.departList[0].departName,
          createTime: parseTime(new Date()),
          depart: this.departList[0].id,
          departName: this.departList[0].departName,
          departCode: this.departList[0].departCode,
          createBy: this.userId,
          createByName: this.userName,
          operationCode: 'GYSXZ',
          serialNumberTable: 'provider_info_history',
          name: '',
          linkmanName: '',
          telephone: '',
          accountName: '',
          locationArea: '',
          bank: '',
          bankAccount: '',
          taxId: '',
          remark: '',
          updateBy: '',
          updateTime: '',
          isAdd: true,
        }
      },
      save() {
        this.$refs.dataForm.validate((valid) => {
          if (valid) {
            this.formLoading = true
            const formData = {
              ...this.formData,
            }
            saveData(formData).then((res) => {
              if (res.code === 200) {
                this.$baseMessage(
                  '保存成功!',
                  'success',
                  'vab-hey-message-success'
                )
                this.$emit('refreshPage')
                this.formLoading = false
                this.dialogFormVisible = false
              }
            })
          }
        })
      },
      close() {
        this.$refs.UploadLargeFileFdfsPopup.closeDelImg()
        this.formLoading = false
        this.dialogFormVisible = false
        this.$refs.dataForm.resetFields()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .dropTree {
    width: 100%;
    max-height: 300px;
    overflow: auto;
  }
</style>
