<template>
  <div
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="80px"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item label="项目名称：" label-width="100px" prop="projectName">
            <el-input
              v-model="queryForm.projectName"
              clearable
              placeholder="请输入项目名称"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item
            label="业务类型"
            label-width="100px"
            prop="businessType"
          >
            <el-select
              v-model="queryForm.businessType"
              filterable
              clearable
              placeholder="请选择"
              style="width: 200px"
            >
              <el-option
                v-for="item in businessType"
                :key="item.id"
                :label="item.dictName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="投标状态"
            label-width="100px"
            prop="businessType"
          >
            <el-select
              v-model="queryForm.bidStatus"
              filterable
              clearable
              placeholder="请选择"
              style="width: 200px"
            >
              <el-option
                v-for="item in bidStatus"
                :key="item.id"
                :label="item.dictName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="建设单位" label-width="100px" prop="constructionUnit">
            <el-input
              v-model="queryForm.constructionUnit"
              placeholder="请输入建设单位"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label-width="100px" v-show="!fold" label="登记人" prop="registerByName">
            <el-input
              v-model="queryForm.registerByName"
              clearable
              placeholder="请输入登记人"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item v-show="!fold" label="项目所在区域" label-width="100px" prop="destinationCity">
            <el-input
              v-model="queryForm.destinationCity"
              clearable
              placeholder="请输入项目所在区域"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item
            v-show="!fold"
            label="保证金状态"
            label-width="100px"
            prop="businessType"
          >
            <el-select
              v-model="queryForm.bidDepositStatus"
              filterable
              clearable
              placeholder="请选择"
              style="width: 200px"
            >
              <el-option
                v-for="item in bidDepositStatus"
                :key="item.id"
                :label="item.dictName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label-width="100px" v-show="!fold" label="审批状态" prop="approvalResult">
            <el-select
              ref="selectFlow"
              v-model="queryForm.approvalResult"
              filterable
              clearable
              placeholder="请选择审批状态"
              style="width: 200px"
            >
              <el-option option value="">所有状态</el-option>
              <el-option
                v-for="item in approvalResultNameList"
                :key="item.id"
                :label="item.approvalResultName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              icon="el-icon-search"
              native-type="submit"
              type="primary"
              @click="handleQuery"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-right"
              @click.native="resetFormPage"
            >
              重置
            </el-button>
          </el-form-item>
          <el-button
            style="margin: 0 0 0 10px !important"
            type="text"
            @click="handleFold"
          >
            <span v-if="fold">展开</span>
            <span v-else>合并</span>
            <vab-icon
              class="vab-dropdown"
              :class="{ 'vab-dropdown-active': fold }"
              icon="arrow-up-s-line"
            />
          </el-button>
        </el-form>
      </vab-query-form-top-panel>
      <vab-query-form-left-panel :span="12">
        <el-button
          v-permissions="{ permission: ['supplierManagement:add'] }"
          icon="el-icon-plus"
          type="primary"
          @click="handleAdd"
        >
          添加
        </el-button>
        <!--        <el-button-->
        <!--          icon="el-icon-delete"-->
        <!--          type="danger"-->
        <!--          @click="batchDelete"-->
        <!--          v-permissions="{ permission: ['supplierManagement:del'] }"-->
        <!--        >-->
        <!--          批删-->
        <!--        </el-button>-->
        <el-button
          :disabled="exportLoading"
          icon="el-icon-refresh-right"
          type="warning"
          @click.native="exportBtn('投标管理.xlsx')"
        >
          导出
        </el-button>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="12">
        <el-button
          style="margin: 0 10px 10px 0 !important"
          type="primary"
          @click="clickFullScreen"
        >
          <vab-icon
            :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'"
          />
          表格全屏
        </el-button>
        <el-popover
          ref="popover"
          popper-class="custom-table-checkbox"
          trigger="hover"
        >
          <el-radio-group v-model="lineHeight">
            <el-radio label="medium">大</el-radio>
            <el-radio label="small">中</el-radio>
            <el-radio label="mini">小</el-radio>
          </el-radio-group>
          <template #reference>
            <el-button style="margin: 0 10px 10px 0 !important" type="primary">
              <vab-icon icon="line-height"/>
              表格尺寸
            </el-button>
          </template>
        </el-popover>
        <el-popover popper-class="custom-table-checkbox" trigger="hover">
          <el-checkbox-group v-model="checkList">
            <vab-draggable v-bind="dragOptions" :list="columns">
              <div v-for="(item, index) in columns" :key="item + index">
                <vab-icon icon="drag-drop-line"/>
                <el-checkbox
                  :disabled="item.disableCheck === true"
                  :label="item.label"
                >
                  {{ item.label }}
                </el-checkbox>
              </div>
            </vab-draggable>
          </el-checkbox-group>
          <template #reference>
            <el-button
              icon="el-icon-setting"
              style="margin: 0 0 10px 0 !important"
              type="primary"
            >
              可拖拽列设置
            </el-button>
          </template>
        </el-popover>
      </vab-query-form-right-panel>
    </vab-query-form>
    <el-table
      ref="tableSort"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      :size="lineHeight"
      stripe
      @selection-change="setSelectRows"
    >
      <el-table-column align="center" type="selection" width="55"/>
      <el-table-column align="center" label="序号" width="60">
        <template #default="{ $index }">
          {{ getIndex($index) }}
        </template>
      </el-table-column>
      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        :align="item.center ? item.center : 'center'"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'"
      >
        <template #default="{ row }">
          <span v-if="item.label === '审批状态'">
            <el-tag :type="fmtFlowType(row)">
              {{ row[item.prop] }}
            </el-tag>
          </span>
          <span v-else-if="item.label === '开标时间'">
            {{ row[item.prop] | dateformat('YYYY-MM-DD') }}
          </span>
          <span v-else-if="item.label === '保证金预计退回时间'">
            {{ row[item.prop] | dateformat('YYYY-MM-DD') }}
          </span>
          <span v-else-if="item.label === '登记日期'">
            {{ row[item.prop] | dateformat('YYYY-MM-DD') }}
          </span>
          <span v-else-if="item.label === '投标保证金'">
            {{ row[item.prop] | currencyFormat }}
          </span>
          <span v-else>
            {{ row[item.prop] }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="300">
        <template #default="{ row }">
          <el-button
            v-permissions="{ permission: ['supplierManagement:update'] }"
            :disabled="isDisabledEditFun(row)"
            icon="el-icon-edit"
            style="margin: 0 10px 10px 0 !important"
            type="primary"
            @click="handleEdit(row)"
          >
            编辑
          </el-button>
          <el-button
            v-if="isAssignee(row)"
            icon="el-icon-edit-outline"
            style="margin: 0 10px 10px 0 !important"
            type="success"
            @click.native.prevent="showApprovalDlg(row)"
          >
            审批
          </el-button>
          <el-button
            icon="el-icon-view"
            style="margin: 0 10px 10px 0 !important"
            type="success"
            @click="handleDetail(row)"
          >
            详情
          </el-button>
          <el-dropdown>
            <el-button size="mini" type="success">
              <i class="el-icon-more"/>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                @click.native.prevent="
                  showReport(
                    row,
                    '/ReportServer?reportlet=/hnsz/bidManagement/TBGL.cpt'
                  )
                "
              >
                <i class="el-icon-s-order" />
                查看报表
              </el-dropdown-item>
              <el-dropdown-item
                v-if="isStartFlowFlag(row)"
                @click.native.prevent="startWorkFlow(row)"
              >
                <i class="el-icon-video-play"/>
                启动流程
              </el-dropdown-item>
              <el-dropdown-item
                v-if="isDeleteFlowFlag(row)"
                @click.native.prevent="deleteWorkFlow(row)"
              >
                <i class="el-icon-refresh-left"/>
                撤回流程
              </el-dropdown-item>
              <el-dropdown-item
                v-if="isDeleteFlag(row)"
                v-permissions="{ permission: ['supplierManagement:del'] }"
                style="color: #fd5353"
                @click.native.prevent="handleDelete(row)"
              >
                <i class="el-icon-delete"/>
                删除
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <tableDetail ref="tableDetail"/>
    <tableEdit
      ref="tableEdit"
      :business-type="businessType"
      :bid-deposit-status="bidDepositStatus"
      :bid-status="bidStatus"
      :depart-tree-data="allDepartList"
      @refreshPage="resetFormPage"
    />
    <!--    部门流程选择-->
    <!--    <VabWorkFlowDepart ref="VabWorkFlowDepart"  />-->
    <!-- 流程启动动态审批条件处理 -->
    <VabStartFlowProcess ref="VabStartFlowProcess"/>
    <VabTableExport ref="tableExport"/>
  </div>
</template>

<script>
import tableMix from '@/views/mixins/table'
import tableDetail from './components/bidManagementDetail.vue'
import tableEdit from './components/bidManagementEdit.vue'
import {getDictList} from '@/api/system/dict-api'
import {
  getDataListByPage,
  deleteData,
  exportData,
} from '@/api/bidManagement/bidManagement'

export default {
  name: 'SupplierManagement',
  components: {
    tableDetail,
    tableEdit,
  },
  mixins: [tableMix],
  data() {
    return {
      formMaxHeight: 2,
      list: [],
      columns: [
        {
          label: '单号',
          prop: 'serialNumber',
          width: '250',
        },
        {
          label: '投标状态',
          prop: 'bidStatusName',
          width: '150',
        },
        {
          label: '项目名称',
          prop: 'projectName',
          width: '260',
        },
        {
          label: '业务类型',
          prop: 'businessTypeName',
          width: '160',
        },
        {
          label: '归属项目',
          prop: 'vestedItemsName',
          width: '160',
        },
        {
          label: '开标时间',
          prop: 'bidOpenTime',
          width: '120',
        },
        {
          label: '项目所在区域',
          prop: 'destinationCity',
          width: '160',
        },
        {
          label: '建设单位',
          prop: 'constructionUnitName',
          width: '300',
        },
        {
          label: '投标负责人',
          prop: 'tenderResponsibleByName',
        },
        {
          label: '投标保证金',
          prop: 'bidDeposit',
        },
        {
          label: '保证金状态',
          prop: 'bidDepositStatusName',
        },
        {
          label: '保证金状态',
          prop: 'bidDepositStatusName',
        },
        {
          label: '保证金预计退回时间',
          prop: 'estimatedReturnTime',
          width: '120',
        },
        {
          label: '登记人',
          prop: 'registerByName',
        },
        {
          label: '登记部门',
          prop: 'createDepartName',
          width: '300',
        },
        {
          label: '登记日期',
          prop: 'registerTime',
          width: '120',
        },
        {
          label: '审批状态',
          prop: 'approvalResultName',
          width: '100',
        },
      ],
      queryForm: {
        projectName: '',
        businessType: '',
        bidStatus: '',
        constructionUnit: '',
        registerByName: '',
        destinationCity: '',
        bidDepositStatus: '',
      },
      businessType: [],
      bidDepositStatus: [],
      bidStatus: [],
    }
  },
  created() {
    this.initDefaultCheck()
    this.getDictListByCodes()
    this.fetchData()
  },
  methods: {
    getDictListByCodes() {
      getDictList({dictCode: 'businessType,bidDepositStatus,bidStatus'}).then((response) => {
        this.businessType = response.result[0].children
        this.bidDepositStatus = response.result[1].children
        this.bidStatus = response.result[2].children
      })
    },
    fetchData() {
      this.listLoading = true
      const queryForm = {
        ...this.queryForm,
        curPage: this.pageInfo.curPage,
        pageSize: this.pageInfo.pageSize,
      }
      getDataListByPage(queryForm).then((res) => {
        this.listLoading = false
        const {
          result: {total, records},
        } = res
        this.list = records
        this.pageInfo.total = Number(total)
      })
    },
    // 添加
    handleAdd() {
      this.$refs['tableEdit'].showDialog({isAdd: true})
    },
    // 编辑
    handleEdit(row) {
      this.$refs['tableEdit'].showDialog({
        isAdd: false,
        ...row,
      })
    },
    //详情
    handleDetail(row) {
      this.$refs['tableDetail'].showDialog(row)
    },
    // 删除
    handleDelete(row) {
      if (row.id) {
        this.$baseConfirm('你确定要删除当前数据吗', null, async () => {
          deleteData({id: row.id})
            .then(() => {
              this.pageInfo.curPage = 1
              this.batchDeleteImg(row.id, this.$route.name)
              this.fetchData()
              this.$baseMessage(
                '删除成功!',
                'success',
                'vab-hey-message-success'
              )
            })
            .catch(() => {
              this.$baseMessage('删除失败!', 'error', 'vab-hey-message-error')
            })
        })
      }
    },

    // 批删
    // batchDelete() {
    //   if (this.selectRows.length === 0) {
    //     this.$baseMessage(
    //       '请选中最少一条记录!',
    //       'error',
    //       'vab-hey-message-error'
    //     )
    //     return
    //   }
    //   const idArr = this.selectRows.map((item) => item.id).join(',')
    //   this.$baseConfirm('你确定要删除选中数据 吗', null, async () => {
    //     deleteData({ id: idArr })
    //       .then(() => {
    //         this.pageInfo.curPage = 1
    //         this.batchDeleteImg(idArr, this.$route.name)
    //         this.fetchData()
    //         this.$baseMessage(
    //           '删除成功!',
    //           'success',
    //           'vab-hey-message-success'
    //         )
    //       })
    //       .catch(() => {
    //         this.$baseMessage('删除失败!', 'error', 'vab-hey-message-error')
    //       })
    //   })
    // },
    exportBtn(excelName) {
      this.queryForm.excelName = excelName
      this.$refs['tableExport'].showDialog(this.columns)
    },
    exportData(columns) {
      this.exportLoading = true
      exportData({...this.queryForm, ...columns}).then((response) => {
        const link = document.createElement('a')
        link.download = this.queryForm.excelName
        link.href = window.URL.createObjectURL(new Blob([response]))
        document.body.appendChild(link)
        link.click()
        link.download = ''
        document.body.removeChild(link)
        URL.revokeObjectURL(response)
      })
      this.exportLoading = false
    },
  },
}
</script>

<style lang="scss" scoped>
$base: '.depart-management';
#{$base}-container {
  padding: 0 !important;
  background: $base-color-background !important;

  &.vab-fullscreen {
    padding: 20px !important;
  }
}
</style>
