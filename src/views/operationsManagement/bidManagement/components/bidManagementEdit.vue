<template>
  <el-dialog
    v-drag
    append-to-body
    :before-close="close"
    center
    :close-on-click-modal="false"
    :title="pageTitle"
    top="5vh"
    :visible.sync="dialogFormVisible"
    width="1420px"
  >
    <div style="max-height: 75vh; overflow-y: scroll; overflow-x: hidden">
      <el-form
        ref="dataForm"
        v-loading="formLoading"
        :inline="true"
        label-position="right"
        label-width="140px"
        :model="formData"
        :rules="rules"
      >
        <table class="form-table">
          <!--          <tr class="title">-->
          <!--            <td colspan="3">基本信息</td>-->
          <!--          </tr>-->
          <tr>
            <td>
              <el-form-item label="单号" prop="serialNumber">
                <el-input
                  v-model="formData.serialNumber"
                  disabled
                  placeholder="自动生成"
                />
              </el-form-item>
            </td>
            <td>
              <el-form-item label="登记人" prop="registerBy">
                <el-input
                  v-model="formData.createByName"
                  disabled
                  style="width: 250px"
                />
              </el-form-item>
            </td>
            <td>
              <el-form-item label="登记部门" prop="createDepart">
                <el-input
                  v-model="formData.createDepartName"
                  disabled
                  placeholder="自动生成"
                  style="width: 250px"
                />
              </el-form-item>
            </td>
          </tr>
          <tr>
            <td>
              <el-form-item label="登记日期" prop="registerTime">
                <el-date-picker
                  v-model="formData.registerTime"
                  placeholder="请选择登记日期"
                  style="width: 250px"
                  type="date"
                />
              </el-form-item>
            </td>
            <td>
              <el-form-item label="投标负责部门" prop="tenderResponsibleDepart">
                <t-form-tree
                  v-model="formData.tenderResponsibleDepart"
                  :default-props="{
                    children: 'children',
                    label: 'departName',
                  }"
                  :show-top="false"
                  :tree-data="departTreeData"
                />
              </el-form-item>
            </td>
            <td>
              <el-form-item label="投标负责人" prop="tenderResponsibleBy">
                <t-form-user
                  v-model="formData.tenderResponsibleBy"
                  placeholder="请选择"
                />
              </el-form-item>
            </td>
          </tr>
          <tr>
            <td>
              <el-form-item label="项目名称" prop="projectName">
                <el-input
                  v-model="formData.projectName"
                  placeholder="请输入项目名称"
                />
              </el-form-item>
            </td>
            <td>
              <el-form-item label="业务类型" prop="businessType">
                <el-select
                  v-model="formData.businessType"
                  placeholder="请选择"
                  @change="businessTypeChange(formData.businessType)"
                >
                  <el-option
                    v-for="item in businessType"
                    :key="item.id"
                    :label="item.dictName"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </td>
            <td>
              <el-form-item label="归属项目" prop="vestedItems">
                <el-select v-model="formData.vestedItems" placeholder="请选择">
                  <el-option
                    v-for="item in vestedItems"
                    :key="item.id"
                    :label="item.dictName"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </td>
          </tr>
          <tr>
            <td colspan="2">
              <el-form-item label="建设单位" prop="constructionUnitName">
                <el-input
                  v-model="formData.constructionUnitName"
                  placeholder="请选择建设单位"
                  readonly
                  style="width: 700px"
                  @click.native="selectCustomer"
                />
              </el-form-item>
            </td>
            <td>
              <el-form-item label="项目所在区域" prop="destinationCity">
                <!--<el-input-->
                <!--  v-model="formData.destinationCity"-->
                <!--  style="width: 280px"-->
                <!--/>-->
                <el-cascader
                  v-model="selectedOptions2"
                  clearable
                  filterable
                  :options="areaOptions"
                  :props="{ label: 'name', value: 'name' }"
                  style="width: 280px"
                  @change="addressChoose2"
                />
              </el-form-item>
            </td>
          </tr>
          <tr>
            <td colspan="1">
              <el-form-item label="开标时间" prop="bidOpenTime">
                <el-date-picker
                  v-model="formData.bidOpenTime"
                  placeholder="请选择开标时间"
                  style="width: 250px"
                  type="date"
                />
              </el-form-item>
            </td>
            <td colspan="2">
              <el-form-item label="开标地点" prop="bidOpenPlace">
                <el-input
                  v-model="formData.bidOpenPlace"
                  placeholder="请输入开标地点"
                  style="width: 700px"
                />
              </el-form-item>
            </td>
          </tr>
          <tr>
            <td>
              <el-form-item label="投标保证金(元)：" prop="bidDeposit">
                <el-input
                  v-model="formData.bidDeposit"
                  placeholder="请输入投标保证金"
                />
              </el-form-item>
            </td>
            <td>
              <el-form-item label="保证金状态" prop="bidDepositStatus">
                <el-select
                  v-model="formData.bidDepositStatus"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in bidDepositStatus"
                    :key="item.id"
                    :label="item.dictName"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </td>
            <td>
              <el-form-item label="预计退回时间" prop="estimatedReturnTime">
                <el-date-picker
                  v-model="formData.estimatedReturnTime"
                  placeholder="请选择预计退回时间"
                  style="width: 250px"
                  type="date"
                />
              </el-form-item>
            </td>
          </tr>
          <tr>
            <td>
              <el-form-item label="投标状态" prop="bidStatus">
                <el-select v-model="formData.bidStatus" placeholder="请选择">
                  <el-option
                    v-for="item in bidStatus"
                    :key="item.id"
                    :label="item.dictName"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </td>
            <td>
              <el-form-item label="状态时间" prop="bidStatusTime">
                <el-date-picker
                  v-model="formData.bidStatusTime"
                  placeholder="请选择状态时间"
                  style="width: 250px"
                  type="date"
                />
              </el-form-item>
            </td>
            <td>
              <el-form-item label="项目造价(元):" prop="projectCost">
                <el-input
                  v-model="formData.projectCost"
                  placeholder="请输入项目造价(元)"
                />
              </el-form-item>
            </td>
          </tr>
          <tr>
            <td colspan="3">
              <el-form-item label="项目概况：" prop="projectOverview">
                <el-input
                  v-model="formData.projectOverview"
                  maxlength="1000"
                  placeholder="请输入内容"
                  :rows="2"
                  style="width: 1165px"
                  type="textarea"
                />
              </el-form-item>
            </td>
          </tr>
          <tr>
            <td colspan="3">
              <el-form-item label="备注：" prop="remark">
                <el-input
                  v-model="formData.remark"
                  maxlength="1000"
                  placeholder="请输入内容"
                  :rows="2"
                  style="width: 1165px"
                  type="textarea"
                />
              </el-form-item>
            </td>
          </tr>
        </table>
      </el-form>
    </div>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>
    <UploadLargeFileFdfsPopup ref="UploadLargeFileFdfsPopup" />
    <!--    <VabUserListDialog ref="userListPopupRef" @callBackData="getUserInfo" />-->
    <!-- 客户单位选择 -->
    <SelectCustomerDialog ref="selectCustomerRef" @getInfo="getCustomerData" />
  </el-dialog>
</template>

<script>
  import { mapGetters } from 'vuex'
  import { saveData } from '@/api/bidManagement/bidManagement'
  import { genUUID, parseTime } from '@/utils/th_utils.js'
  import { areaList } from '@/utils/area.js'
  import UploadLargeFileFdfsPopup from '@/views/common/UploadLargeFileFdfsPopup.vue'
  import SelectCustomerDialog from '../common/SelectCustomerDialog.vue'
  import TFormUser from '@/vab/components/TFormUser/index.vue'
  import { getDictList } from '@/api/system/dict-api'
  export default {
    components: { UploadLargeFileFdfsPopup, SelectCustomerDialog, TFormUser },
    props: {
      businessType: {
        type: Array,
        default() {
          return []
        },
      },
      bidDepositStatus: {
        type: Array,
        default() {
          return []
        },
      },
      bidStatus: {
        type: Array,
        default() {
          return []
        },
      },
      departTreeData: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        dialogFormVisible: false,
        showSupplier: false,
        formLoading: false,
        formData: {},
        selectedOptions2: [],
        areaOptions: areaList,
        selectedOptions: [],
        vestedItems: [],
        rules: {
          projectName: {
            required: true,
            message: '项目名称为必填',
            trigger: 'blur',
          },
          businessType: {
            required: true,
            message: '业务类型为必填',
            trigger: 'change',
          },
          // vestedItems: [
          //   {
          //     validator: (rule, value, callback) => {
          //       if (this.vestedItems.length > 0 && !value) {
          //         callback(new Error('请选择归属项目'));
          //       } else {
          //         callback();
          //       }
          //     },
          //     trigger: 'change'
          //   }
          // ],
          constructionUnitName: {
            required: true,
            message: '建设单位为必填',
            trigger: 'change',
          },
          destinationCity: {
            required: true,
            message: '项目所在区域为必填',
            trigger: 'blur',
          },
          // bidOpenTime: {
          //   required: true,
          //   message: '开标时间为必填',
          //   trigger: 'blur',
          // },
          bidOpenPlace: {
            required: true,
            message: '开标地点为必填',
            trigger: 'blur',
          },
          // bidDeposit: {
          //   required: true,
          //   validator: (rule, value, callback) => {
          //     const regex = /^(?:\d{1,9})(?:\.\d{1,2})?$/; // 允许9位整数和最多两位小数
          //     if (!value) {
          //       callback(new Error('投标保证金为必填'));
          //     } else if (!regex.test(value)) {
          //       callback(new Error('投标保证金必须为数字，且最多9位整数和2位小数'));
          //     } else {
          //       callback();
          //     }
          //   },
          // },
          projectCost: {
            required: true,
            validator: (rule, value, callback) => {
              const regex = /^(?:\d{1,9})(?:\.\d{1,2})?$/ // 允许9位整数和最多两位小数
              if (!value) {
                callback(new Error('项目造价为必填'))
              } else if (!regex.test(value)) {
                callback(
                  new Error('项目造价必须为数字，且最多9位整数和2位小数')
                )
              } else {
                callback()
              }
            },
          },
        },
      }
    },
    computed: {
      ...mapGetters({
        userId: 'user/userId',
        userName: 'user/userName',
        departList: 'acl/departList',
      }),
      pageTitle() {
        return this.formData.isAdd ? '新增投标管理' : '编辑投标管理'
      },
    },
    methods: {
      businessTypeChange(id) {
        this.vestedItems = []
        // console.log(1111,id)
        this.formData.vestedItems = ''
        getDictList({ parentId: id }).then((response) => {
          // this.businessType = response.result[0].children
          this.vestedItems = response.result
          // console.log(response.result)
        })
      },
      selectCustomer() {
        this.$refs.selectCustomerRef.showDialog({ isNewVersion: '0' })
      },
      getCustomerData(data) {
        if (data.length > 0) {
          this.formData.constructionUnitName = data[0].accountName
          this.formData.constructionUnit = data[0].id
        }
      },
      addressChoose2(value) {
        console.log('##选中的地区', value)
        this.formData.destinationCity =
          value[0] + '/' + value[1] + '/' + value[2]
      },
      selectUserData() {
        this.$refs.userListPopupRef.showUserDialog({})
      },
      getUserInfo(data) {
        if (data.length > 0) {
          this.formData.tenderResponsibleBy = data[0].id
          this.formData.tenderResponsibleByName = data[0].userName
        }
      },
      async showDialog(row) {
        this.formData = { ...row }
        if (this.formData.isAdd) {
          this.init()
        } else {
          //获取子数组
          getDictList({ parentId: this.formData.businessType }).then(
            (response) => {
              // this.businessType = response.result[0].children
              this.vestedItems = response.result
              // console.log(response.result)
            }
          )
          this.selectedOptions = []
          this.selectedOptions2.push(
            row.destinationCity.split('/')[0],
            row.destinationCity.split('/')[1],
            row.destinationCity.split('/')[2]
          )
        }
        this.dialogFormVisible = true

        this.$nextTick(() => {
          this.$refs.UploadLargeFileFdfsPopup.getParamsData({
            bizId: this.formData.id,
            bizCode: this.$route.name,
          })
        })
      },
      // 初始化表单
      init() {
        const newId = genUUID()
        this.formData = {
          id: newId,
          newId: newId,
          serialNumber: '',
          createDepart: this.departList[0].id,
          createDepartName: this.departList[0].departName,
          registerTime: parseTime(new Date()),
          depart: this.departList[0].id,
          departName: this.departList[0].departName,
          departCode: this.departList[0].departCode,
          registerBy: this.userId, //登记人
          createByName: this.userName,
          viewUser: this.userId,
          operationCode: 'TBGL',
          serialNumberTable: 'bid_management',
          tenderResponsibleByName: '',
          tenderResponsibleBy: '',
          vestedItems: '',
          constructionUnitName: '',
          destinationCity: '', //目的地城市
          isAdd: true,
        }
      },
      save() {
        this.$refs.dataForm.validate((valid) => {
          if (valid) {
            this.formLoading = true
            const formData = {
              ...this.formData,
            }
            saveData(formData).then((res) => {
              if (res.code === 200) {
                this.$baseMessage(
                  '保存成功!',
                  'success',
                  'vab-hey-message-success'
                )
                this.$emit('refreshPage')
                this.formLoading = false
                this.dialogFormVisible = false
              }
            })
          }
        })
      },
      close() {
        this.$refs.UploadLargeFileFdfsPopup.closeDelImg()
        this.formLoading = false
        this.dialogFormVisible = false
        this.$refs.dataForm.resetFields()
        this.init()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .dropTree {
    width: 100%;
    max-height: 300px;
    overflow: auto;
  }
</style>
