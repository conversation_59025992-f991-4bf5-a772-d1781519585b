<template>
  <div>
    <el-row :gutter="20">
      <el-card shadow="false">
        <vab-query-form>
          <vab-query-form-left-panel :span="16">
            <label>位置：</label>
            <el-input
              v-model="input"
              clearable
              placeholder="请输关键字搜索"
              style="width: 350px"
            />
            <span>
              <span style="color: #fd5353">*</span>
              点击地图选择打卡位置，可拖拽标点位置。
            </span>
          </vab-query-form-left-panel>
        </vab-query-form>
        <el-row :gutter="20">
          <el-col :span="17">
            <baidu-map
              v-if="showBMap"
              ak="FI5SNgp55tP14gnti7s4e9948frCA0t2"
              :center="center"
              class="map"
              :scroll-wheel-zoom="true"
              :zoom="zoom"
              @click="clickBmap"
            >
              <bm-map-type
                anchor="BMAP_ANCHOR_TOP_RIGHT"
                :map-types="['BMAP_NORMAL_MAP', 'BMAP_HYBRID_MAP']"
              />
              <bm-city-list anchor="BMAP_ANCHOR_TOP_LEFT" />
              <bm-geolocation
                anchor="BMAP_ANCHOR_BOTTOM_RIGHT"
                :auto-location="true"
                :show-address-bar="true"
              />
              <bm-marker
                :dragging="true"
                :position="markerPoint"
                @dragend="dragend"
              >
                <bm-info-window
                  :height="0"
                  :position="markerPoint"
                  :show="true"
                  :width="0"
                >
                  <div style="padding: 15px 0px">
                    位置：{{ formData.address }}
                  </div>
                </bm-info-window>
              </bm-marker>
              <bm-circle
                :center="markerPoint"
                :radius="formData.distance"
                stroke-color="blue"
                :stroke-opacity="0.5"
                :stroke-weight="1"
              />
              <bm-local-search
                :auto-viewport="true"
                :keyword="input"
                :panel="false"
              />
            </baidu-map>
          </el-col>
          <el-col :span="7">
            <el-form
              ref="form"
              :model="formData"
              :rules="rules"
              style="margin-top: 80px"
            >
              <el-form-item label="当前考勤点" prop="bizId">
                <b>{{ formData.projectName }}</b>
              </el-form-item>
              <el-form-item label="经度" prop="lat">
                <el-input
                  v-model="formData.lat"
                  :disabled="true"
                  style="width: 300px"
                />
              </el-form-item>
              <el-form-item label="纬度" prop="lng">
                <el-input
                  v-model="formData.lng"
                  :disabled="true"
                  style="width: 300px"
                />
              </el-form-item>
              <el-form-item label="位置" prop="address">
                <span>{{ formData.address }}</span>
              </el-form-item>
              <el-form-item label="范围" prop="distance">
                <el-input-number
                  v-model="formData.distance"
                  :step="100"
                  :step-strictly="true"
                />
                米
                <br />
                <span style="margin-left: 50px">
                  <span style="color: #fd5353">*</span>
                  请输入整百数
                </span>
              </el-form-item>
              <el-button type="primary" @click="definePunchInRange">
                保存配置
              </el-button>
              <el-button type="success" @click="showDate">考勤日期</el-button>
              <el-button type="warning" @click="showPeople">考勤人员</el-button>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
    </el-row>
    <oaAttendanceDate
      ref="oaAttendanceDate"
      :biz-id="formData.bizId"
      :name="projectName"
    />
    <oaPeople ref="oaPeople" :biz-id="formData.bizId" :name="projectName" />
  </div>
</template>

<script>
  import {
    getPunchInRangeByBizId,
    definePunchInRange,
  } from '@/api/oa/oaAttendance-api'
  import { bMapToQQMap, qqMapToBMap } from '@/utils/util.js'
  import oaAttendanceDate from '/src/views/administrativeManagement/components/oaAttendanceDate.vue'
  import oaPeople from '/src/views/administrativeManagement/components/oaPeople.vue'
  import BaiduMap from 'vue-baidu-map/components/map/Map.vue'
  import BmMarker from 'vue-baidu-map/components/overlays/Marker' // 点标注
  import BmInfoWindow from 'vue-baidu-map/components/overlays/InfoWindow' // 标注弹窗
  import BmCityList from 'vue-baidu-map/components/controls/CityList.vue' // 城市列表
  import BmMapType from 'vue-baidu-map/components/controls/MapType.vue' // 地图类型
  import BmGeolocation from 'vue-baidu-map/components/controls/Geolocation.vue' // 定位
  import BmLocalSearch from 'vue-baidu-map/components/search/LocalSearch' // 搜索
  import BmCircle from 'vue-baidu-map/components/overlays/Circle.vue'
  import tableMix from '@/views/mixins/table'
  import { parseTime } from '@/utils/index'
  import { mapGetters } from 'vuex'

  export default {
    name: 'OaAttendance',
    components: {
      BaiduMap,
      BmMarker,
      BmInfoWindow,
      BmCityList,
      BmMapType,
      BmGeolocation,
      BmLocalSearch,
      BmCircle,
      oaAttendanceDate,
      oaPeople,
    },
    mixins: [tableMix],
    data() {
      return {
        hasCard: true,
        listLoading: false,
        projectId: '',
        selectedBidSection: [],
        showBMap: false,
        center: {
          // 经纬度
          lng: 115.864913,
          lat: 28.695523,
        },
        formData: {
          bizId: '', // 项目或标段id
          address: '', // 地址信息
          lat: 28.720239, // 经度
          lng: 115.802946, // 纬度
          distance: 300, // 范围 （整数）
        },
        markerPoint: {
          // 经纬度
          lng: 115.864913,
          lat: 28.695523,
        },
        zoom: 16, // 地图展示级别
        rules: {
          bizId: [{ required: true, message: '维护公司', trigger: 'change' }],
          address: [{ required: true, message: '地址必选', trigger: 'change' }],
          lat: [{ required: true, message: '经度必选', trigger: 'change' }],
          lng: [{ required: true, message: '纬度必选', trigger: 'change' }],
          distance: [{ required: true, message: '范围必填', trigger: 'blur' }],
        },
        projectName: '',
        input: '',
      }
    },
    computed: {
      ...mapGetters({
        userId: 'user/userId',
      }),
    },
    mounted() {},
    methods: {
      async show(data) {
        this.formData = {
          address: data.siteStart,
          lat: data.siteStartX.split(',')[1],
          lng: data.siteStartX.split(',')[0],
          projectName: data.projectName,
          bizId: data.id,
        }
        if (data.siteStartX !== '') {
          this.center = {
            lng: this.formData.lng,
            lat: this.formData.lat,
          }
          this.markerPoint = this.center
        }
        this.showBMap = true
        this.getPunchInRangeByBizId(data.id)
      },
      showPeople() {
        if (this.formData.bizId) {
          this.$refs.oaPeople.showDateDialog()
        } else {
          this.$notify({
            title: '信息',
            message: '请选择维护公司/项目！',
            type: 'error',
            duration: 2000,
          })
        }
      },
      showDate() {
        if (this.formData.bizId) {
          this.$refs.oaAttendanceDate.showDateDialog()
        } else {
          this.$notify({
            title: '信息',
            message: '请先维护考勤位置信息',
            type: 'error',
            duration: 2000,
          })
        }
      },
      clickBmap(val) {
        // 鼠标点击放标注
        this.markerPoint = val.point
        this.formData.lng = this.markerPoint.lng
        this.formData.lat = this.markerPoint.lat
        const geocoder = new BMap.Geocoder() // 创建地址解析器的实例
        geocoder.getLocation(this.markerPoint, (rs) => {
          this.formData.address = rs.address
        })
      },
      dragend(val) {
        // 标注拖拽完成获取坐标信息
        this.markerPoint = val.point
        this.formData.lng = this.markerPoint.lng
        this.formData.lat = this.markerPoint.lat
        const geocoder = new BMap.Geocoder() // 创建地址解析器的实例
        geocoder.getLocation(this.markerPoint, (rs) => {
          this.formData.address = rs.address
        })
      },
      getPunchInRangeByBizId(id) {
        getPunchInRangeByBizId(id).then((response) => {
          if (response.result) {
            const obj = response.result
            this.formData.bizId = obj.bizId
            this.formData.address = obj.address
            this.formData.distance = obj.distance
            var point = qqMapToBMap(obj.longitude, obj.latitude)
            this.formData.lng = point[0]
            this.formData.lat = point[1]
            this.markerPoint.lng = point[0]
            this.markerPoint.lat = point[1]
            this.center = this.markerPoint
          } else {
            this.$notify({
              title: '信息',
              message: '未找到定义内容，请定义打卡位置及范围！',
              type: 'info',
              duration: 2000,
            })
          }
        })
      },
      definePunchInRange() {
        this.$refs['form'].validate((valid) => {
          var point = bMapToQQMap(this.formData.lng, this.formData.lat)
          var data = JSON.parse(JSON.stringify(this.formData))
          data.lng = point[0]
          data.lat = point[1]
          data.userId = this.userId
          data.createTime = parseTime(new Date())
          if (valid) {
            definePunchInRange(data).then((response) => {
              const result = response.result.result
              if (result) {
                this.$notify({
                  title: '信息',
                  message: '定义成功！',
                  type: 'success',
                  duration: 2000,
                })
              } else {
                this.$notify({
                  title: '信息',
                  message: '定义失败！',
                  type: 'error',
                  duration: 2000,
                })
              }
            })
          }
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .map {
    width: 100%;
    height: 650px;
  }
</style>
