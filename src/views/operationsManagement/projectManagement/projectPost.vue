<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="80px"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item
            label="岗位名称"
            prop="name"
          >
            <el-input
              v-model="queryForm.name"
              placeholder= '请输入岗位名称'
            />
          </el-form-item>
          <el-form-item>
            <el-button
              icon="el-icon-search"
              native-type="submit"
              type="primary"
              @click="handleQuery"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-right"
              @click.native="resetForm('form')"
            >
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
      <vab-query-form-left-panel :span="12">
        <el-button icon="el-icon-plus" type="primary" @click="handleAdd">
          添加
        </el-button>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="12">
        <el-button
          style="margin: 0 10px 10px 0 !important"
          type="primary"
          @click="clickFullScreen"
        >
          <vab-icon
            :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'"
          />
          表格全屏
        </el-button>
        <el-popover
          ref="popover"
          popper-class="custom-table-checkbox"
          trigger="hover"
        >
          <el-radio-group v-model="lineHeight">
            <el-radio label="medium">大</el-radio>
            <el-radio label="small">中</el-radio>
            <el-radio label="mini">小</el-radio>
          </el-radio-group>
          <template #reference>
            <el-button style="margin: 0 10px 10px 0 !important" type="primary">
              <vab-icon icon="line-height" />
              表格尺寸
            </el-button>
          </template>
        </el-popover>
        <el-popover popper-class="custom-table-checkbox" trigger="hover">
          <el-checkbox-group v-model="checkList">
            <vab-draggable v-bind="dragOptions" :list="columns">
              <div v-for="(item, index) in columns" :key="item + index">
                <vab-icon icon="drag-drop-line" />
                <el-checkbox
                  :disabled="item.disableCheck === true"
                  :label="item.label"
                >
                  {{ item.label }}
                </el-checkbox>
              </div>
            </vab-draggable>
          </el-checkbox-group>
          <template #reference>
            <el-button
              icon="el-icon-setting"
              style="margin: 0 0 10px 0 !important"
              type="primary"
            >
              可拖拽列设置
            </el-button>
          </template>
        </el-popover>
      </vab-query-form-right-panel>
    </vab-query-form>

    <el-table
      ref="tableSort"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      :size="lineHeight"
      stripe
      @selection-change="setSelectRows"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        :align="item.center ? item.center : 'center'"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'"
      ></el-table-column>

      <el-table-column align="center" label="操作" width="360">
        <template #default="{ row }">
          <el-button
            v-permissions="{ permission: ['menuPermission:update'] }"
            icon="el-icon-paperclip"
            style="margin: 0 10px 10px 0 !important"
            type="success"
            @click="handleMenuEdit(row)"
          >
            菜单权限
          </el-button>
          <el-button
            icon="el-icon-edit"
            style="margin: 0 10px 10px 0 !important"
            type="primary"
            @click="handleEdit(row)"
            :disabled="filterPost[row.id]"
          >
            编辑
          </el-button>
          <el-button
            icon="el-icon-delete"
            style="margin: 0 10px 10px 0 !important"
            type="danger"
            @click="handleDelete(row)"
            :disabled="filterPost[row.id]"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <table-edit ref="edit" @fetch-data="fetchData" />
    <!--  数据权限绑定  -->
    <data-permission ref="dataPermission" />
    <table-edit-post ref="postEdit" @fetch-data="fetchData" />
  </div>
</template>

<script>
  import {queryPostByPage,deletePostData} from '@/api/project'
  import TableEdit from '/src/views/system/components/PermissionsEdit'
  import TableEditPost from './components/projectPostEdit.vue'
  import dataPermission from '/src/views/system/components/dataPermission.vue'
  import tableMix from '@/views/mixins/table'
  import { filterPost, BY_ROLE } from '@/utils/constants'

  export default {
    name: 'projectPost',
    components: {
      TableEdit,
      dataPermission,
      TableEditPost,
    },
    mixins: [tableMix],
    data() {
      return {
        checkList: ['序号','岗位名称', '创建人', '创建时间'],
        columns: [
          {
            label: '序号',
            prop: 'sort',
            width:"80"
          },
          {
            label: '岗位名称',
            prop: 'name',
            disableCheck: true,
          },
          {
            label: '创建人',
            prop: 'createByName',
          },
          {
            label: '创建时间',
            prop: 'createTime',
          },
        ],
        type: BY_ROLE,
        filterPost:filterPost,
        queryForm: {
          name: '',
          code: '',
        },
      }
    },
    computed: {
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.fetchData()
    },
    methods: {
      handleAdd() {
        this.$refs['postEdit'].showEdit()
      },
      handleEdit(row) {
        this.$refs['postEdit'].showEdit(row)
      },
      handleMenuEdit(row) {
        this.$refs['edit'].showEdit(row, this.type)
      },
      async fetchData() {
        this.listLoading = true
        let data = {
          curPage: this.pageInfo.curPage,
          pageSize: this.pageInfo.pageSize,
          roleName: this.queryForm.name,
          roleCode: this.queryForm.code,
        }
        var {
          result: { records, total },
        } = await queryPostByPage(data)
        this.list = records.map((item) => ({
          id: item.id,
          name: item.roleName,
          code: item.roleCode,
          sort: item.sort,
          remark: item.remark,
          createByName: item.createByName,
          createTime: item.createTime,
        }))
        this.pageInfo.total = Number(total)
        this.listLoading = false
      },
      // 删除
      handleDelete(row) {
        if (row.id) {
          this.$baseConfirm('你确定要删除当前数据吗', null, async () => {
            deletePostData({ id: row.id })
              .then(() => {
                this.pageInfo.curPage = 1
                this.fetchData()
                this.$baseMessage(
                  '删除成功!',
                  'success',
                  'vab-hey-message-success'
                )
              })
              .catch((err) => {
                this.$baseMessage(err.msg, 'error', 'vab-hey-message-error')
              })
          })
        }
      },
    },
  }
</script>
<style lang="scss" scoped></style>
