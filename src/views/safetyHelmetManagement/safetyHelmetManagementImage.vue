<template>
  <div
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="80px"
          :model="queryForm"
          @submit.native.prevent
        >
<!--          <el-form-item label="项目编号" prop="projectCode">-->
<!--            <el-input-->
<!--              v-model="queryForm.projectCode"-->
<!--              clearable-->
<!--              placeholder="请输入项目编号"-->
<!--              style="width: 200px"-->
<!--            />-->
<!--          </el-form-item>-->
          <el-form-item label="项目名称" prop="projectName">
            <el-input
              v-model="queryForm.projectName"
              clearable
              placeholder="请输入项目名称"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="设备名称" prop="name">
            <el-input
              v-model="queryForm.name"
              clearable
              placeholder="请输入名称"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="日期" prop="releaseDate">
              <el-date-picker v-model="queryForm.releaseDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="date" placeholder="请选择" />
            </el-form-item>
          <el-form-item>
            <el-button
              icon="el-icon-search"
              native-type="submit"
              type="primary"
              @click="handleQuery"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-right"
              @click.native="resetFormPage"
            >
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
      <vab-query-form-left-panel :span="12">
        <el-button icon="el-icon-plus" type="primary" @click="handleAdd">
          新增
        </el-button>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="12">
        <el-button
          style="margin: 0 10px 10px 0 !important"
          type="primary"
          @click="clickFullScreen"
        >
          <vab-icon
            :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'"
          />
          表格全屏
        </el-button>
        <el-popover
          ref="popover"
          popper-class="custom-table-checkbox"
          trigger="hover"
        >
          <el-radio-group v-model="lineHeight">
            <el-radio label="medium">大</el-radio>
            <el-radio label="small">中</el-radio>
            <el-radio label="mini">小</el-radio>
          </el-radio-group>
          <template #reference>
            <el-button style="margin: 0 10px 10px 0 !important" type="primary">
              <vab-icon icon="line-height" />
              表格尺寸
            </el-button>
          </template>
        </el-popover>
        <el-popover popper-class="custom-table-checkbox" trigger="hover">
          <el-checkbox-group v-model="checkList">
            <vab-draggable v-bind="dragOptions" :list="columns">
              <div v-for="(item, index) in columns" :key="item + index">
                <vab-icon icon="drag-drop-line" />
                <el-checkbox
                  :disabled="item.disableCheck === true"
                  :label="item.label"
                >
                  {{ item.label }}
                </el-checkbox>
              </div>
            </vab-draggable>
          </el-checkbox-group>
          <template #reference>
            <el-button
              icon="el-icon-setting"
              style="margin: 0 0 10px 0 !important"
              type="primary"
            >
              可拖拽列设置
            </el-button>
          </template>
        </el-popover>
      </vab-query-form-right-panel>
    </vab-query-form>
    <el-table
      ref="tableSort"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      :size="lineHeight"
      stripe
      @selection-change="setSelectRows"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column align="center" label="序号" width="60">
        <template #default="{ $index }">
          {{ getIndex($index) }}
        </template>
      </el-table-column>
      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        :align="item.center ? item.center : 'center'"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'"
      >
        <template #default="{ row }">
          <span v-if="item.label === '状态'">
            <el-tag v-if="row.status === '0'" type="info">离线</el-tag>
            <el-tag v-if="row.status === '1'" type="success">在线</el-tag>
          </span>
          <span v-else-if="item.label === '照片'">
            <div style="position: relative; width: 100%; height: 100px;">
              <el-image
                v-if="row[item.prop]"
                style="width: 100%; height: 100%; object-fit: cover; cursor: pointer; border: 2px solid #ccc; border-radius: 8px; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);"
                :src="row[item.prop]"
                :preview-src-list="[row[item.prop]]"
                fit="cover"
              />
              <div
                v-if="row.serialNumber"
                style="position: absolute; bottom: 0; left: 0; width: 100%; padding: 4px; background: rgba(0,0,0,0.5); color: white; text-align: center; font-size: 12px; border: none; border-radius: 0 0 8px 8px;"
              >
                {{ row.serialNumber }}
              </div>
            </div>
          </span>
          <span v-else>
            {{ row[item.prop] }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="320">
        <template #default="{ row }">
          <el-button
            icon="el-icon-download"
            style="margin: 0 10px 10px 0 !important"
            type="success"
            @click="downloadFile(row)"
          >
            下载
          </el-button>
          <el-button
                v-permissions="{ permission: ['safetyHelmetManagementImage:del'] }"
                style="color: #fd5353"
                @click.native.prevent="handleDelete(row)"
              >
                <i class="el-icon-delete" />
                删除
          </el-button>

          <!-- <el-dropdown>
            <el-button size="small" type="success">
              <i class="el-icon-more" />
            </el-button> -->
            <!-- <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-if="isDeleteFlag(row)"
                v-permissions="{ permission: ['safetyHelmetManagementImage:del'] }"
                style="color: #fd5353"
                @click.native.prevent="handleDelete(row)"
              >
                <i class="el-icon-delete" />
                删除
              </el-dropdown-item>
            </el-dropdown-menu> -->
          <!-- </el-dropdown> -->
        </template>
      </el-table-column>
      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <SelectContractDialog ref="selectContractRef" @getInfo="getCustomerData" />
    <CommonUploadLargeFileFdfsPopup ref="CommonUploadLargeFileFdfsPopup" @refreshUploadFileList="refreshUploadFileList" />
    <!-- 详情 -->
    <!-- <tableDetail ref="tableDetail" /> -->
    <!-- 新增修改 -->
    <!-- <tableEdit
      ref="tableEdit"
      :depart-tree-data="allDepartList"
      @refreshPage="resetFormPage"
    /> -->
  </div>
</template>
<script>
  import tableMix from '@/views/mixins/table'
  // import tableDetail from './components/safetyHelmetManagementBaseDetail.vue'
  // import tableEdit from './components/safetyHelmetManagementBaseEdit.vue'
    import {
    downLoadFileMinio,
  } from '@/api/system/uploadFile-api'
  import {
    deleteSafetyHelmetImage,
    getSafetyHelmetImageListByPage,
  } from '@/api/safetyHelmetManagement/safetyHelmetManagementBase-api'
  import SelectContractDialog from "@/views/safetyHelmetManagement/common/SelectGroupDialog.vue";

  import {genUUID} from "@/utils/th_utils";
  import {saveData} from "@/api/safetyHelmetManagement/safetyHelmetGroupDetail-api";
  import CommonUploadLargeFileFdfsPopup from "@/views/safetyHelmetManagement/common/CommonUploadLargeFileFdfsPopup.vue";
  // import CommonUploadLargeFileFdfsPopup from '@/views/system/common/CommonUploadLargeFileFdfsPopup'

  export default {
    name: 'safetyHelmetManagementImage',
    components: {
      CommonUploadLargeFileFdfsPopup,
      SelectContractDialog
    },
    mixins: [tableMix],
    data() {
      return {
        formMaxHeight: 2,
        columns: [
          {
            label: '照片',
            prop: 'urlPath',
            width: '160',
          },
          {
            label: '使用者',
            prop: 'createByName',
            width: '250',
          },
          {
            label: '项目名称',
            prop: 'projectName',
            width: '250',
          },
          {
            label: '设备名称',
            prop: 'name',
            width: '250',
          },
          {
            label: '设备编号',
            prop: 'deviceId',
            width: '200',
          },
             {
            label: '创建日期',
            prop: 'createTime',
            width: '200',
          }
        ],
        defaultProps: {
          children: 'children',
          label: 'label',
        },
        formData:{

        },
        queryForm: {
          serialNumber: '',
          recorder: '',
          createDepartName: '',
        },
      }
    },
    created() {
      // this.getDictListByCodes()
      this.fetchData()
    },
    methods: {
      refreshUploadFileList() {
        this.pageInfo.curPage = 1
        this.fetchData()
      },
      fetchData() {
        this.listLoading = true

        const queryForm = {
          ...this.queryForm,
          curPage: this.pageInfo.curPage,
          pageSize: this.pageInfo.pageSize,
          bizCode:'safetyHelmetImage'
        }
        if(queryForm.releaseDate !== undefined){
          queryForm.queryDateStart = this.queryForm.releaseDate + ' 00:00:00'
          queryForm.queryDateEnd = this.queryForm.releaseDate + ' 23:59:59'
        }
        getSafetyHelmetImageListByPage(queryForm).then((res) => {
          this.listLoading = false
          const {
            result: { total, records },
          } = res
          this.list = records
          this.pageInfo.total = Number(total)
        })
      },
           downloadFile(row) {
        downLoadFileMinio(row.fileId).then((response) => {
          const data = response
          if (!data) {
            return
          }
          const url = window.URL.createObjectURL(new Blob([data]))
          console.log(url, 'url')
          const a = document.createElement('a')
          a.style.display = 'none'
          a.href = url
          a.setAttribute('download', row.fileName + '.' + row.fileExt)
          document.body.appendChild(a)
          a.click()
          document.body.removeChild(a)
          window.URL.revokeObjectURL(url)
          this.tempBtn = false
          this.tempDownText = '下载'
        })
      },
      changeStatus(row) {
        changeStatus({ id: row.id, status: row.status })
          .then(() => {
            this.pageInfo.curPage = 1
            this.fetchData()
            this.$baseMessage('操作成功!', 'success', 'vab-hey-message-success')
          })
          .catch(() => {
            this.$baseMessage('操作失败!', 'error', 'vab-hey-message-error')
          })
      },
      getCustomerData(data) {
        if (data.length > 0) {
          const safetyHelmetBy = data[0].safetyHelmetBy
          const deviceId = data[0].deviceId
          const id = data[0].id
          this.formData = data[0]
          this.formData.bizId = id
          this.formData.bizCode = 'safetyHelmetImage'
          this.formData.createBy = safetyHelmetBy
          this.formData.deviceId = deviceId
          this.formData.type = 'uploadImage'
          this.$refs.CommonUploadLargeFileFdfsPopup.showUploadLargeDialog(this.formData)
        }
      },
      handleAdd() {

        //  this.$refs['tableEdit'].showDialog({ isAdd: true })
        this.$refs.selectContractRef.showDialog({
          selectType: 'organ_purchase',
          isNewVersion: '0',
        })
      },
      //详情
      handleDetail(row) {
        this.$refs['tableDetail'].showDialog(row)
      },
      // 删除
      handleDelete(row) {
        console.info(row)
        if (row.fileId) {
          this.$baseConfirm('你确定要删除当前数据吗', null, async () => {
            deleteSafetyHelmetImage({ id: row.fileId })
              .then(() => {
                this.pageInfo.curPage = 1
                // this.batchDeleteImg(row.fileId, this.$route.name)
                this.fetchData()
                this.$baseMessage(
                  '删除成功!',
                  'success',
                  'vab-hey-message-success'
                )
              })
              .catch(() => {
                this.$baseMessage('删除失败!', 'error', 'vab-hey-message-error')
              })
          })
        }
      },
      // 是否可编辑
      isDisabledEditFun(row) {
        if (this.isCreater(row.createBy)) {
          // 是创建人 可编辑
          return false
        }
        // 其他情况不可编辑
        return true
      },
      // 是否可删除
      isDeleteFlag(row) {
        if (this.isCreater(row.createBy)) {
          // 是创建人 可删除
          return true
        }
        // 其他情况不可删除
        return false
      },
      // 获取数据字典
      getDictListByCodes() {},
    },
  }
</script>

<style lang="scss" scoped>
  $base: '.depart-management';
  #{$base}-container {
    padding: 0 !important;
    background: $base-color-background !important;

    &.vab-fullscreen {
      padding: 20px !important;
    }
  }
</style>
