<template>
  <div class="depart-management-container" :class="{ 'vab-fullscreen': isFullscreen }">
    <el-row :gutter="20">
      <el-col :span="5">
        <safeHelmetGroupTree @change="departChange" @gua="gua" @call="call" @delete="handleDelete"
                             @handleAdd="handleAdd" />
      </el-col>
      <el-col :span="19">
        <el-card shadow="hover">
          <vab-query-form>
            <vab-query-form-left-panel>
              <el-button type="primary" @click="joinConference('')" class="step-button">
                开始会议
              </el-button>
              <el-button type="primary" @click="gua('')" class="step-button">
                退出会议
              </el-button>
              <el-button type="primary" @click="guaEnd()" class="step-button">
                解散会议
              </el-button>
            </vab-query-form-left-panel>
          </vab-query-form>
          <!-- 视频组件容器，使用flex布局 -->
          <div class="video-container-wrapper">
            <div hidden id="id_msg"></div>
            <!-- 主视频组件 -->
            <div class="video-wrapper" id="main-video-wrapper">
              <video id="remoteVideo" autoplay playsinline @dblclick="toggleVideoFullscreen($event)"></video>
              <!-- 本地视频组件已移除，但保留元素以便JavaScript代码仍能引用 -->
            </div>
            <div class="video-wrapper" id="main-video-wrapper">
              <!-- 本地视频组件已移除，但保留元素以便JavaScript代码仍能引用 -->
              <video id="localVideo" muted="muted" @dblclick="toggleVideoFullscreen($event)"></video>
            </div>
          </div>
          <!-- 动态生成的额外视频组件 - 只有远程视频 -->
          <div class="additional-video-container">
            <div v-for="(video, index) in additionalVideos" :key="index" class="additional-video">
              <video :id="'remoteVideo-'+index" autoplay playsinline @dblclick="toggleVideoFullscreen($event)"></video>
              <button class="close-video-btn" @click="closeAdditionalVideo(index)">挂断</button>
            </div>
          </div>
          <Map></Map>
        </el-card>
      </el-col>
    </el-row>
    <SelectContractDialog ref="selectContractRef" @getInfo="getCustomerData" />
  </div>
</template>

<script>
// import { getSharedDataList, deleteData,saveSharedDataData, getSharedDataPageData} from '@/api/sharedData/index'
import {
  deleteData,
  getDataListByPage,
  saveData,
} from '@/api/safetyHelmetManagement/safetyHelmetGroupDetail-api'
import {
  changeStatus,
  joinMeeting,
} from '@/api/safetyHelmetManagement/safetyHelmetManagementBase-api'
import SelectContractDialog from '@/views/safetyHelmetManagement/common/SelectGroupDialog.vue'
import { genUUID } from '@/utils/th_utils'
import { getSysConfig } from '@/api/system/sysConfig-api'
import safeHelmetGroupTree from './components/safeHelmetGroupTree.vue'
import tableMix from '@/views/mixins/table'
import { mapGetters } from 'vuex'
import Map from './common/map.vue'

export default {
  name: 'safetyHelmetManagemenTest',
  components: {
    SelectContractDialog,
    safeHelmetGroupTree,
    Map
  },
  mixins: [tableMix],
  data() {
    return {
      showVideoContainer: false, // 新增控制视频容器显示的变量
      callFlg: false,
      logFlag: false, // 是否打开日志
      userExtension: "", // 当前用户分机号
      targetExtension: "", // 目标用户分机号
      stunUrl: "",
      turnsUrl: "",
      turnsUser: "",
      turnsPassword: "",
      wssNewUrl: "",//wwss wss://status.acowbo.fun:10014 wss://fs.jxth.com.cn:55744 wss://fs.acowbo.com:7443
      userAgent: null, // 用户代理实例
      password: "", // 密码 1234 jxth666777 1007@1007  telnet **************:5066 ************** acowbo
      serverIp: "", // 服务器ip *************:5060 ************:10011 ************:55560 ************ ************** ************** freeSwicth.jxth.com.cn **************
      isRegisted: false, // 是否已注册
      localStream: null, // 本地流
      incomingSession: null, // 呼入的会话
      outgoingSession: null, // 呼出的会话
      currentSession: null, // 当前会话
      myHangup: false, // 是否我方挂断
      sipsession: null,
      sipsessionTwo: null,
      audio: null, // 音频
      meVideo: null, // 我方视频
      remoteVideo: null, // 对方视频
      remoteVideoTwo: null,
      localVideo: null,
      notIdList: [],
      currentCallRow: null, // 新增：保存当前通话的行
      additionalVideos: [], // 新增：存储额外的视频组件
      videoSessions: [], // 新增：存储额外的会话
    }
  },
  computed: {
    ...mapGetters({
      userId: 'user/userId'
    })
  },
  created() {
    this.queryForm.departId = '-1'
    this.queryForm.fileName = ''
    this.queryForm.bizId = '-1'
    this.queryForm.bizCode = 'sharedData'
    this.pageInfo.curPage = 1
    this.getSafetyConfig()
  },
  mounted() {
    this.remoteVideo = document.getElementById('remoteVideo');
    this.localVideo = document.getElementById('localVideo');
  },
  methods: {
    async getSafetyConfig() {
      const res = await getSysConfig({
        bizType: 'safetyHelmetManagemenConfig',
      })
      this.userExtension = res.result.value1
      this.wssNewUrl = res.result.value2
      this.password = res.result.value3
      this.serverIp = res.result.value4
      this.stunUrl = res.result.value5
      this.turnsUrl = res.result.value6
      this.turnsUser = res.result.value7
      this.turnsPassword = res.result.value8
      this.reg()
    },
    // 添加查看详情方法
    viewDetail(row) {
      // 将设备ID作为查询参数传递
      this.$router.push({
        path: '/onSiteManagement/safetyHelmetManagement/safetyHelmetManagemenTestDetail',
        query: {
          managementId: row.managementId,
          _t: Date.now() // 添加时间戳参数，强制页面刷新
        }
      }).then(() => {
        // 路由跳转成功后，通过事件总线触发页面刷新
        this.$nextTick(() => {
          this.$baseEventBus.$emit('reload-router-view', 'safetyHelmetManagemenTestDetail');
        });
      });
    },

    call(row) {
      // 检查是否已经有通话在进行
      if (this.callFlg) {
        // 已有通话，创建新的视频组件
        const newIndex = this.additionalVideos.length;
        this.additionalVideos.push({
          index: newIndex,
          row: row
        });

        // 设置当前行为通话状态
        this.$set(row, 'isCallActive', true);
        this.showVideoContainer = true;

        // 创建新的会话
        var host = this.serverIp;
        var to = row.sipId;
        console.info("拨打号码(额外视频)", to);

        // 使用setTimeout确保DOM已更新
        setTimeout(() => {
          const remoteVideoEl = document.getElementById(`remoteVideo-${newIndex}`);

          if (!remoteVideoEl) {
            console.error('视频元素未找到');
            return;
          }

          const session = this.userAgent.invite(to + '@' + host, {
            sessionDescriptionHandlerOptions: {
              constraints: {
                audio: true,
                video: true
              }
            }
          });

          // 保存会话
          this.videoSessions[newIndex] = session;

          const _this = this;
          this.videoSessions[newIndex].on('accepted', function (e) {
            console.info(`额外视频 ${newIndex} 会话已接受`, session);
            var pc = _this.videoSessions[newIndex].sessionDescriptionHandler.peerConnection;

            // 获取远程轨道
            var remoteStream = new MediaStream();
            pc.getReceivers().forEach(function (receiver) {
              remoteStream.addTrack(receiver.track);
            });

            console.info(`额外视频 ${newIndex} 远程流`, remoteStream);
            remoteVideoEl.srcObject = remoteStream;
            remoteVideoEl.play();
          });

          // 添加失败和终止事件处理
          session.on('failed', function (response, cause) {
            console.error(`额外视频 ${newIndex} 呼叫失败:`, cause, response);
            // _this.closeAdditionalVideo(newIndex);
          });

          session.on('terminated', function () {
            console.log(`额外视频 ${newIndex} 呼叫已终止`);
            // _this.closeAdditionalVideo(newIndex);
          });
        }, 100);

      } else {
        // 没有通话，使用主视频组件
        this.$set(row, 'isCallActive', true);
        this.showVideoContainer = true;
        this.callFlg = true;
        this.currentCallRow = row;

        var host = this.serverIp;
        var to = row.sipId;
        console.info("拨打号码(主视频)", to);

        this.sipsession = this.userAgent.invite(to + '@' + host, {
          sessionDescriptionHandlerOptions: {
            constraints: {
              audio: true,
              video: true
            }
          }
        });

        const _this = this;
        this.sipsession.on('accepted', function (e) {
          console.info('主视频会话已接受', _this.sipsession);
          var pc = _this.sipsession.sessionDescriptionHandler.peerConnection;

          // 获取远程轨道
          var remoteStream = new MediaStream();
          pc.getReceivers().forEach(function (receiver) {
            remoteStream.addTrack(receiver.track);
          });

          console.info("主视频远程流", remoteStream);
          _this.remoteVideo.srcObject = remoteStream;
          _this.remoteVideo.play();
          console.info("pc.getSenders()", pc.getSenders())
          // 获取本地轨道
          if (pc.getSenders()) {
            var localStream = new MediaStream();
            pc.getSenders().forEach(function (sender) {
              localStream.addTrack(sender.track);
            });

            console.info("主视频本地流", localStream);
            _this.localVideo.srcObject = localStream;
            _this.localVideo.play();
          }
        });

        // 添加失败和终止事件处理
        this.sipsession.on('failed', function (response, cause) {
          console.error("主视频呼叫失败:", cause, response);
          _this.gua(_this.currentCallRow);
        });

        this.sipsession.on('terminated', function () {
          console.log("主视频呼叫已终止");
          if (_this.currentCallRow) {
            _this.$set(_this.currentCallRow, 'isCallActive', false);
          }
          _this.callFlg = false;
        });
      }
    },
    callNew(row) {
      this.callFlg = true;
      var host = this.serverIp;
      var to = row.sipId;
      console.info("拨打号码", to);

      const _this = this;

      // 先获取本地媒体流，确保有权限和设备可用
      navigator.mediaDevices.getUserMedia({ audio: true, video: true })
        .then(function (stream) {
          // 显示本地视频预览
          _this.localVideo.srcObject = stream;
          _this.localVideo.play();

          // 保存本地流以便后续使用
          _this.localStream = stream;

          // 发起SIP呼叫，使用UDP传输
          _this.sipsession = _this.userAgent.invite(to + '@' + host + ';transport=udp', {
            sessionDescriptionHandlerOptions: {
              constraints: {
                audio: true,
                video: true
              },
              // 确保ICE连接顺利建立
              peerConnectionOptions: {
                iceCheckingTimeout: 5000,
                rtcConfiguration: {
                  iceServers: [
                    { urls: 'stun:tools.acowbo.fun:40997' },
                    {
                      urls: 'turns:tools.acowbo.fun:40998',
                      username: 'jxth',
                      credential: 'DSf43l@#3ksPplsdA'
                    }
                  ]
                }
              }
            }
          });

          // 处理呼叫进展状态
          _this.sipsession.on('progress', function (response) {
            console.log('呼叫进行中...', response);
          });

          // 处理呼叫被接受
          _this.sipsession.on('accepted', function (e) {
            console.info('通话已接通', e);

            try {
              var pc = _this.sipsession.sessionDescriptionHandler.peerConnection;

              // 确保连接已建立
              if (pc.connectionState === 'connected' || pc.iceConnectionState === 'connected') {
                console.log('PeerConnection已连接');
              } else {
                console.log('等待PeerConnection连接...', pc.connectionState, pc.iceConnectionState);
              }

              // 获取远程流
              var remoteStream = new MediaStream();
              pc.getReceivers().forEach(function (receiver) {
                if (receiver.track) {
                  console.log('添加远程轨道:', receiver.track.kind, receiver.track.id);
                  remoteStream.addTrack(receiver.track);
                }
              });

              if (remoteStream.getTracks().length > 0) {
                console.info("获取到远程流，轨道数:", remoteStream.getTracks().length);
                _this.remoteVideo.srcObject = remoteStream;
                _this.remoteVideo.play().catch(e => console.error('远程视频播放失败:', e));
              } else {
                console.warn("未获取到远程轨道");
              }
            } catch (error) {
              console.error("处理媒体流时出错:", error);
            }
          });

          // 处理呼叫失败
          _this.sipsession.on('failed', function (response, cause) {
            console.error("呼叫失败:", cause, response);
            _this.callFlg = false;
            _this.$baseMessage('呼叫失败: ' + cause, 'error', 'vab-hey-message-error');
          });

          // 处理呼叫终止
          _this.sipsession.on('terminated', function () {
            console.log("呼叫已终止");
            _this.callFlg = false;
          });
        })
        .catch(function (err) {
          console.error("获取媒体设备失败:", err);
          _this.callFlg = false;
          _this.$baseMessage('无法访问摄像头或麦克风: ' + err.message, 'error', 'vab-hey-message-error');
        });
    },
    joinMeeting(){
      // this.queryForm.roomNumber
      joinMeeting({ groupId: this.queryForm.groupId,roomNumber: this.queryForm.roomNumber })
        .then(() => {
          this.$baseMessage('操作成功!', 'success', 'vab-hey-message-success')
        })
        .catch(() => {
          this.$baseMessage('操作失败!', 'error', 'vab-hey-message-error')
        })
    },
    joinConference(numberOne) {

      this.callFlg = true;
      this.showVideoContainer = true; // 显示视频容器
      var host = this.serverIp;
      // var confNumber = '3000'
      if (this.queryForm.roomNumber === undefined) {
        this.$baseMessage('请选择群组', 'error', 'vab-hey-message-error')
        return
      }
      this.joinMeeting()
      var confNumber = numberOne + this.queryForm.roomNumber

      // 确保已注册
      if (!this.userAgent || !this.userAgent.isRegistered()) {
        this.$baseMessage('平台sip未成功注册请刷新页面', 'error', 'vab-hey-message-error')
        // alert("请先注册SIP账号");
        return;
      }

      // 如果已有会话，先终止
      if (this.sipsession) {
        // 清除之前的保活定时器
        if (this.sipsession._keepAliveTimer) {
          clearInterval(this.sipsession._keepAliveTimer);
          this.sipsession._keepAliveTimer = null;
        }

        this.sipsession.terminate();
        this.sipsession = null;
      }
      const _this = this
      // 先获取媒体流
      navigator.mediaDevices.getUserMedia({ audio: true, video: true })
        .then(function (stream) {
          // 显示本地视频
          _this.localVideo.srcObject = stream;
          _this.localVideo.play();

          // 呼叫会议号码
          document.getElementById('id_msg').innerText = "正在加入会议...";

          // 从host中提取纯域名/IP，不包含端口
          var sipDomain = host.split(':')[0];

          // 检查会议号码格式
          // if (!/^\d{4}$/.test(confNumber)) {
          //   document.getElementById('id_msg').innerText = "会议号格式错误，请使用4位数字";
          //   return;
          // }

          console.log("尝试加入会议:", confNumber);

          // 根据会议号码前缀确定使用哪个会议配置文件
          var confProfile = "default";

          // 根据default.xml中的配置，不同前缀的会议号码使用不同的会议配置文件
          if (confNumber.startsWith('30')) {
            confProfile = "default";        // 30xx 使用 default 配置
          } else if (confNumber.startsWith('31')) {
            confProfile = "wideband";       // 31xx 使用 wideband 配置
          } else if (confNumber.startsWith('32')) {
            confProfile = "ultrawideband";  // 32xx 使用 ultrawideband 配置
          } else if (confNumber.startsWith('33')) {
            confProfile = "cdquality";      // 33xx 使用 cdquality 配置
          } else if (confNumber.startsWith('35')) {
            confProfile = "video-mcu-stereo"; // 35xx 使用 video-mcu-stereo 配置
          } else if (confNumber.startsWith('36')) {
            confProfile = "video-mcu-stereo-720"; // 36xx 使用 video-mcu-stereo-720 配置
          } else if (confNumber.startsWith('37')) {
            confProfile = "video-mcu-stereo-480"; // 37xx 使用 video-mcu-stereo-480 配置
          } else if (confNumber.startsWith('38')) {
            confProfile = "video-mcu-stereo-320"; // 38xx 使用 video-mcu-stereo-320 配置
          } else if (confNumber.startsWith('99')) {
            confProfile = "default";        // 30xx 使用 default 配置
          }

          // 使用正确的会议房间格式：confNumber-${domain_name}@profile
          // 注意：确保域名部分不包含端口或特殊字符
          var cleanDomain = sipDomain.replace(/[^a-zA-Z0-9.-]/g, ''); // 只保留字母、数字、点和连字符
          var confUri = confNumber + '-' + cleanDomain + '@' + confProfile;
          console.log("会议URI:", confUri);

          // 如果域名是IP地址，尝试另一种格式（有些FreeSWITCH配置可能使用不同的格式）
          var isIpAddress = /^\d+\.\d+\.\d+\.\d+$/.test(sipDomain);
          if (isIpAddress) {
            console.log("域名是IP地址，尝试另一种格式");
            confUri = confNumber + '@' + confProfile;
          }

          console.log("最终会议URI:", confUri);

          _this.sipsession = _this.userAgent.invite(confUri, {
            sessionDescriptionHandlerOptions: {
              constraints: {
                audio: true,
                video: true
              }
            }
          });

          _this.sipsession.on('accepted', function () {
            document.getElementById('id_msg').innerText = "已加入会议: " + confNumber + " (使用" + confProfile + "配置)";

            var pc = _this.sipsession.sessionDescriptionHandler.peerConnection;
            // 显示会议视频流
            var remoteStream = new MediaStream();
            pc.getReceivers().forEach(function (receiver) {
              if (receiver.track) {
                remoteStream.addTrack(receiver.track);
                console.log("添加远程轨道:", receiver.track.kind, receiver.track.id);
              }
            });

            // 设置远程视频
            _this.remoteVideo.srcObject = remoteStream;
            _this.remoteVideo.play();

            // 设置视频显示样式
            _this.remoteVideo.style.objectFit = "contain";

            // 不再自动切换布局，让用户手动控制

            // 监控连接状态
            _this.monitorConnection(pc);
          });

          _this.sipsession.on('failed', function (response, cause) {
            console.error("会议加入失败:", cause, response);
            _this.gua()
            document.getElementById('id_msg').innerText = "加入会议失败: " + cause +
              "。请确认会议号码正确且服务器已配置。尝试使用30开头的会议号码(例如3000)。";
          });

          _this.sipsession.on('terminated', function (message, cause) {
            console.log("会议终止:", cause, message);
            document.getElementById('id_msg').innerText = "已离开会议";
            // 清除保活定时器
            if (_this.sipsession && _this.sipsession._keepAliveTimer) {
              clearInterval(_this.sipsession._keepAliveTimer);
              _this.sipsession._keepAliveTimer = null;
            }
            // _this.guaNew()
          });
        })
        .catch(function (err) {
          document.getElementById('id_msg').innerText = "无法获取媒体设备: " + err.message;
        });
    },
    // 监控连接状态
    monitorConnection(pc) {
      const _this = this
      pc.addEventListener('iceconnectionstatechange', function () {
        console.log('ICE连接状态:', pc.iceConnectionState);
        if (pc.iceConnectionState === 'failed' || pc.iceConnectionState === 'disconnected' || pc.iceConnectionState === 'closed') {
          document.getElementById('id_msg').innerText = "连接问题，尝试重新连接...";
          // 记录当前状态，便于重连
          var currentSessionData = {
            type: _this.sipsession._dialog?.remoteTarget?.uri?.user?.includes('-') ? 'conference' : 'call',
            target: _this.sipsession._dialog?.remoteTarget?.uri?.user
          };
          console.log("尝试重新连接，会话类型:", currentSessionData.type, "目标:", currentSessionData.target);
          // 尝试ICE重启
          if (_this.sipsession && _this.sipsession.status !== 9) { // 9是终止状态
            _this.sipsession.renegotiate({
              sessionDescriptionHandlerOptions: {
                peerConnectionOptions: {
                  iceRestart: true
                }
              }
            }).catch(function (err) {
              console.error("ICE重启失败:", err);

              // 如果重启失败且是会议，尝试重新加入会议
              if (currentSessionData.type === 'conference') {
                setTimeout(function () {
                  if (confirm("连接已断开，是否尝试重新加入会议？")) {
                    joinConference();
                  }
                }, 2000);
              }
            });
          } else if (currentSessionData.type === 'conference') {
            // 如果会话已结束但是是会议，提示重新加入
            setTimeout(function () {
              if (confirm("会议连接已断开，是否尝试重新加入？")) {
                joinConference();
              }
            }, 2000);
          }
        }
      });
      // 添加会议保活功能 - 每30秒发送一个DTMF信号
      if (_this.sipsession && _this.sipsession._dialog?.remoteTarget?.uri?.user?.includes('-')) {
        // 这是会议会话，添加保活
        var keepAliveInterval = setInterval(function () {
          if (_this.sipsession && _this.sipsession.status !== 9) { // 9是终止状态
            try {
              // 发送一个静音DTMF信号作为保活
              console.log("发送会议保活信号...");
              _this.sipsession.dtmf('*', { duration: 100 });
            } catch (e) {
              console.error("发送保活信号失败:", e);
              clearInterval(keepAliveInterval);
            }
          } else {
            // 会话已结束，清除定时器
            clearInterval(keepAliveInterval);
          }
        }, 30000); // 每30秒发送一次
        // 保存定时器引用以便在会话结束时清除
        _this.sipsession._keepAliveTimer = keepAliveInterval;
      }
    },
    // 关闭额外视频组件
    closeAdditionalVideo(index) {
      // 终止会话
      if (this.videoSessions[index]) {
        this.videoSessions[index].terminate();
        // 清理媒体
        const remoteVideoEl = document.getElementById(`remoteVideo-${index}`);
        if (remoteVideoEl && remoteVideoEl.srcObject) {
          remoteVideoEl.srcObject.getTracks().forEach(track => track.stop());
          remoteVideoEl.srcObject = null;
        }
        // 更新行状态 - 先检查this.additionalVideos[index]是否存在
        if (this.additionalVideos[index]) {
          const row = this.additionalVideos[index].row;
          if (row) {
            this.$set(row, 'isCallActive', false);
          }
        }

        // 保存要删除的会话和视频信息
        const sessionToRemove = this.videoSessions[index];
        const videoToRemove = this.additionalVideos[index];

        // 从数组中移除
        this.additionalVideos.splice(index, 1);
        this.videoSessions.splice(index, 1);

        // 重新索引剩余的视频组件并更新DOM元素ID
        this.additionalVideos.forEach((video, i) => {
          video.index = i;
          // 查找当前索引的视频元素
          const videoEl = document.getElementById(`remoteVideo-${i}`);
          if(i >= index){
            var pc = this.videoSessions[i].sessionDescriptionHandler.peerConnection;
            // 获取远程轨道
            var remoteStream = new MediaStream();
            pc.getReceivers().forEach(function (receiver) {
              remoteStream.addTrack(receiver.track);
            });
            console.info(`额外视频 ${i} 远程流`, remoteStream);
            videoEl.srcObject = remoteStream;
            videoEl.play();
          }
          console.info('videoEl',videoEl)
          // 如果找不到当前索引的元素，说明DOM结构与数据不同步
          if (!videoEl) {
            // 查找可能存在的旧索引元素
            for (let j = i + 1; j < this.additionalVideos.length + 1; j++) {
              const oldEl = document.getElementById(`remoteVideo-${j}`);
              if (oldEl) {
                // 更新ID
                oldEl.id = `remoteVideo-${i}`;
                console.log(`更新视频元素ID: remoteVideo-${j} -> remoteVideo-${i}`);
                break;
              }
            }
          }
        });
      }
    },
    guaEnd() {
      var kickNumber = '*88'
      this.joinConference(kickNumber)
      this.gua()
    },
    gua(row) {
      // 如果没有传递row参数，则关闭会议
      if (!row) {
        this.callFlg = false;
        this.showVideoContainer = false; // 隐藏视频容器
        // 关闭会议房间 - 发送会议结束信号
        if (this.sipsession) {
          // 如果是会议类型的会话，发送结束会议的DTMF信号
          if (this.sipsession._dialog?.remoteTarget?.uri?.user?.includes('-')) {
            try {
              // 发送会议结束信号 (通常是 '#' 或特定的DTMF码)
              this.sipsession.dtmf('#', { duration: 300 });
              console.log("已发送会议结束信号");
            } catch (e) {
              console.error("发送会议结束信号失败:", e);
            }
          }
          // 终止会话
          this.sipsession.terminate();
          this.clearMedia("remoteVideo");
          this.clearMedia("localVideo");
          // 清除保活定时器
          if (this.sipsession._keepAliveTimer) {
            clearInterval(this.sipsession._keepAliveTimer);
            this.sipsession._keepAliveTimer = null;
          }
        }
        // 清理所有额外视频
        if (this.additionalVideos && this.additionalVideos.length > 0) {
          // 从后向前遍历数组，避免索引变化问题
          for (let i = this.additionalVideos.length - 1; i >= 0; i--) {
            this.closeAdditionalVideo(i);
          }
        }
        // 重置会议状态
        document.getElementById('id_msg').innerText = "会议已关闭";
        this.sipsession = null;
        return;
      }
      // 如果是主视频
      if (this.currentCallRow != null && this.currentCallRow.id === row.id) {
        row.isCallActive = false;
        if (this.sipsession) {
          this.sipsession.terminate();
          this.clearMedia("remoteVideo");
          this.clearMedia("localVideo");
        }
        this.callFlg = false;
        this.currentCallRow = null;
        // 如果没有额外视频，隐藏视频容器
        if (this.additionalVideos.length === 0) {
          this.showVideoContainer = false;
        }
      } else {
        // 查找是哪个额外视频
        const videoIndex = this.additionalVideos.findIndex(v => v.row.id === row.id);
        if (videoIndex !== -1) {
          this.closeAdditionalVideo(videoIndex);
        }
      }
    },
    clearMedia(mediaNameOrStream) {
      let mediaSrcObject = this[mediaNameOrStream].srcObject;
      if (mediaSrcObject) {
        let tracks = mediaSrcObject.getTracks();
        for (let i = 0; i < tracks.length; i++) {
          tracks[i].stop();
        }
      }
      this[mediaNameOrStream].srcObject = null;
    },
    reg() {
      var host = this.serverIp;
      var user = this.userExtension;
      var pwd = this.password;
      var wss = this.wssNewUrl;
      var config = {
        // Replace this IP address with your FreeSWITCH IP address
        uri: user + '@' + host,
        // Replace this IP address with your FreeSWITCH IP address
        // and replace the port with your FreeSWITCH ws port
        transportOptions: {
          wsServers: [wss]
        },
        // FreeSWITCH Default Username
        authorizationUser: user,

        // FreeSWITCH Default Password
        password: pwd,
        sessionDescriptionHandlerFactoryOptions: {
          peerConnectionOptions: {
            rtcConfiguration: {
              iceServers: [
                { urls: this.stunUrl },  // STUN服务器，帮助发现公网地址
                {
                  urls: this.turnsUrl,    // TURN服务器，带TLS加密
                  username: this.turnsUser,                    // 你配置的用户名
                  credential: this.turnsPassword    // 你配置的密码
                }
              ]
            }
          },
          alwaysAcquireMediaFirst: true
        }
      };
      this.userAgent = new SIP.UA(config);
      const _this = this
      this.userAgent.on('registered', function () {
        console.info("注册成功,注册用户：" + _this.userExtension)
        document.getElementById('id_msg').innerText = "reg ok";
      });
      this.userAgent.on('invite', function (session) {
        var url = session.remoteIdentity.uri.toString() + "--->call";
        var isaccept = confirm(url);
        if (isaccept) {
          //接受来电
          session.accept({
            sessionDescriptionHandlerOptions: {
              constraints: {
                audio: true,
                video: true
              }
            }
          });
          console.info("session", session)
          this.sipsession = session;
          session.on('accepted', function () {//trackAdded
            // We need to check the peer connection to determine which track was added
            var pc = session.sessionDescriptionHandler.peerConnection;
            console.log("pc", pc);
            // console.log(pc.getLocalStreams());
            // Gets remote tracks
            var remoteStream = new MediaStream();
            pc.getReceivers().forEach(function (receiver) {
              remoteStream.addTrack(receiver.track);
            });
            console.info("remoteStream1", remoteStream)
            _this.remoteVideo.srcObject = remoteStream;
            _this.remoteVideo.play();
            console.info("pc.getSenders()", pc.getSenders())
            if (pc.getSenders()) {
              var localStream = new MediaStream();
              pc.getSenders().forEach(function (sender) {
                localStream.addTrack(sender.track);
              });
              _this.localVideo.srcObject = localStream;
              _this.localVideo.play();
            }
          });
        }
        else {
          //拒绝来电
          session.reject();
        }
      });
    },
    getCustomerData(data) {
      if (data.length > 0) {
        const id = data[0].id
        this.formData = data[0]
        this.formData.isAdd = true
        this.formData.id = genUUID()
        this.formData.groupId = this.queryForm.groupId
        this.formData.managementId = id
        this.formData.sort = 0
        this.formData.createBy = this.userId
        saveData(this.formData).then((res) => {
          if (res.code === 200) {
            this.$baseMessage(
              '添加成功!',
              'success',
              'vab-hey-message-success'
            )
            this.formLoading = false
            this.dialogFormVisible = false
          }
        })
      }
    },
    departChange(data) {
      //this.$refs['form'].resetFields()
      this.queryForm.groupId = data.id
      this.queryForm.roomNumber = data.roomNumber
      // this.queryForm.parentDepartName = data.label
    },
    handleAdd(data) {
      console.log(data)
      this.queryForm.groupId = data.id
      if (data.children.length > 0) {
        this.notIdList = data.children.map(item => item.managementId)
      }
      if (this.queryForm.groupId === undefined) {
        this.$baseMessage('请选择群组', 'error', 'vab-hey-message-error')
        return
      }
      //  this.$refs['tableEdit'].showDialog({ isAdd: true })
      this.$refs.selectContractRef.showDialog({
        notIdList: this.notIdList,
        isNewVersion: '0',
      })
      // let param = {}
      // if(this.queryForm.sharedType === '0'){
      //   param.parentId = this.queryForm.parentId
      //   param.parentName = this.queryForm.parentName
      // }else{
      //   param.parentId = '-1'
      //   param.parentName = '全部'
      // }
      //   param.id = this.queryForm.departId
      // this.$refs['edit'].showAdd(param)
    },
    handleDelete(row) {
      if (row.id) {
        this.$baseConfirm('你确定要把当前人移出群组吗', null, async () => {
          deleteData({ id: row.id }).then(response => {
            this.pageInfo.curPage = 1
            this.$baseMessage('删除成功!', 'success', 'vab-hey-message-success')
          }).catch(err => {
            this.$baseMessage('删除失败!', 'error', 'vab-hey-message-error')
          })
        })
      }
    },
    // 添加双击视频全屏方法
    toggleVideoFullscreen(event) {
      const videoElement = event.target;

      if (document.fullscreenElement) {
        // 如果已经是全屏状态，退出全屏
        document.exitFullscreen();
      } else {
        // 进入全屏
        if (videoElement.requestFullscreen) {
          videoElement.requestFullscreen();
        } else if (videoElement.webkitRequestFullscreen) { // Safari
          videoElement.webkitRequestFullscreen();
        } else if (videoElement.msRequestFullscreen) { // IE11
          videoElement.msRequestFullscreen();
        }
      }
    },
  },
}
</script>

<style lang="scss" scoped>
$base: '.depart-management';

#{$base}-container {
  padding: 0 !important;
  background: $base-color-background !important;

  &.vab-fullscreen {
    padding: 20px !important;
  }
}

/* 新增视频容器样式 */
.video-container-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 15px;
}

.video-wrapper {
  position: relative;
  width: 49.5%;
  height: 400px;
  border: 1px solid #dcdfe6;
  background-color: black;
  /* border-radius: 4px; */
  overflow: hidden;
}

.video-wrapper video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
</style>
<style scoped>
.additional-video-container {
  display: flex;
  flex-wrap: nowrap;
  /* 防止换行 */
  justify-content: space-between;
  gap: 10px;
  margin-bottom: 15px;
  /* 平均分布视频框 */
}

.additional-video {
  flex: 1;
  position: relative;
  height: 400px;
  border: 1px solid #dcdfe6;
  background-color: black;
}

.close-video-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  background: rgba(255, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 2px 6px;
  cursor: pointer;
  z-index: 10;
}

.additional-video video {
  width: 100%;
  /* 视频宽度填充容器 */
  height: 100%;
  /* 保持视频比例 */
  object-fit: cover;
}
</style>
