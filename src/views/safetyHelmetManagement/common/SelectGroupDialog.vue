<template>
  <el-dialog v-drag append-to-body :title="title" :visible.sync="dialogFormVisible" width="1100px"
    :close-on-click-modal="false" :before-close="close" top="2vh">
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form ref="form" :inline="true" label-width="80px" :model="queryForm" @submit.native.prevent>
          <el-form-item label="安全帽名称" prop="name">
            <el-input v-model="queryForm.name" clearable placeholder="请输入安全帽名称" style="width: 200px" />
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" native-type="submit" type="primary" @click="handleQuery">
              查询
            </el-button>
            <el-button icon="el-icon-refresh-right" @click.native="resetForm('form')">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
    </vab-query-form>
    <el-table ref="tableDataRef" v-loading="listLoading" border :height="450" :data="list" stripe
      @row-click="handleRowClick" @selection-change="selectionChange" @select="handleRowSelect">
      <el-table-column type="selection" label="选择" width="55" align="center" />
      <el-table-column v-for="(item, index) in columns" :key="index" :align="item.center ? item.center : 'center'"
        :label="item.label" :prop="item.prop" :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'">
        <template #default="{ row }">
          <span v-if="item.label === '费用所属部门/项目'">
            {{ row.type === 'depart' ? row.departName : row.projectName }}
          </span>
          <span v-else>{{ row[item.prop] }}</span>
        </template>
      </el-table-column>
      <template #empty>
        <el-image class="vab-data-empty" :src="require('@/assets/empty_images/data_empty.png')" />
      </template>
    </el-table>
    <el-pagination background :current-page="pageInfo.curPage" :layout="layout" :page-sizes="[5, 10, 20]"
      :page-size="pageInfo.pageSize" :total="pageInfo.total" @current-change="handleCurrentChange"
      @size-change="handleSizeChange" />
    <div class="select-content" v-if="isMulti">
      <div class="select-title">当前已选择:</div>
      <div class="select-list">
        <el-tag v-for="item in selectRows" :key="item.id" closable @close="closeTag(item)">
          {{ item.projectName }}
        </el-tag>
      </div>
    </div>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
  import {
    getDataListByPage,
  } from '@/api/safetyHelmetManagement/safetyHelmetManagementBase-api'
  // import {
  //   getDataListByPage,
  // } from '@/api/contract/index'
  export default {
    props: {
      isMulti: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        title: '安全帽列表',
        listLoading: false,
        dialogFormVisible: false,
        layout: 'total, sizes, prev, pager, next, jumper',
        pageInfo: {
          curPage: 1,
          pageSize: 10,
          total: 0,
        },
        selectRows: [],
        columns: [
          {
            label: '安全帽名称',
            prop: 'name',
          },
          {
            label: '安全帽编号',
            prop: 'deviceId',
          },
          {
            label: '使用人',
            prop: 'safetyHelmetByName',
          },
          {
            label: '项目名称',
            prop: 'projectName',
          },


        ],
        list: [],
        projectId: '',
        selectIds: [],
        isFirst: true,
        tableRowId: '',
        tableRowData: {},
        queryForm: {
          contractName: '',
          allPermissions: true,
        },
      }
    },
    computed: {},
    created() { },
    methods: {

      async showDialog(queryData = {}) {
        this.queryForm = {
          ...this.queryForm,
          ...queryData,
        }
        //console.log(140)
        this.dialogFormVisible = true
        await this.fetchData()
      },
      selectionChange(val) {
        if (!this.isMulti) {
          if (val.length > 1) {
            this.$refs.tableDataRef.clearSelection()
            this.$refs.tableDataRef.toggleRowSelection(val.pop())
          } else {
            this.selectRows = []
            const rowData = val.pop()
            !!rowData && this.selectRows.push(rowData)
          }
        }
      },
      handleRowClick(row) {
        if (this.isMulti) {
          const idx = this.selectRows.findIndex((item) => item.id === row.id)
          idx < 0 ? this.selectRows.push(row) : this.selectRows.splice(idx, 1)
          this.$refs.tableDataRef.toggleRowSelection(row)
        } else {
          this.$refs.tableDataRef.toggleRowSelection(row)
        }
      },
      handleRowSelect(selection, row) {
        if (this.isMulti) {
          const idx = this.selectRows.findIndex((item) => item.id === row.id)
          idx < 0 ? this.selectRows.push(row) : this.selectRows.splice(idx, 1)
        }
      },
      closeTag(row) {
        const idx = this.selectRows.findIndex((item) => item.id === row.id)
        this.selectRows.splice(idx, 1)
        this.$forceUpdate()
        this.tableToggleSelect()
      },
      save() {
        let data = []
        if (this.selectRows && this.selectRows.length) {
          data = data.concat(this.selectRows)
          this.$refs.tableDataRef.clearSelection()
          this.selectRows = []
        }
        this.selectIds = []
        this.$emit('getInfo', data)
        this.isFirst = true
        this.dialogFormVisible = false
      },
      close() {
        this.$refs.tableDataRef.clearSelection()
        this.list = []
        this.selectRows = []
        this.selectIds = []
        this.isFirst = true
        this.pageInfo.curPage = 1
        this.$refs.form.resetFields()
        this.dialogFormVisible = false
      },
      async fetchData() {
        const queryForm = {
          ...this.queryForm,
          curPage: this.pageInfo.curPage,
          pageSize: this.pageInfo.pageSize,
          approvalResult: '2',
        }
        this.listLoading = true
        const {
          result: { records, total },
        } = await getDataListByPage(queryForm)
        this.list = records
        this.pageInfo.total = Number(total)
        this.isFirst && this.getSelectRows()
        this.listLoading = false
        if (this.selectRows.length) {
          this.tableToggleSelect()
        }
      },
      tableToggleSelect() {
        const rowData = []
        const selectIds = this.selectRows.map((item) => item.id)
        this.list.forEach((item) => {
          if (selectIds.includes(item.id)) {
            rowData.push(item)
          }
        })
        this.$refs.tableDataRef.clearSelection()
        this.$nextTick(() => {
          if (rowData.length > 0) {
            rowData.forEach((item) => {
              this.$refs.tableDataRef.toggleRowSelection(item)
            })
          }
        })
      },
      getSelectRows() {
        this.list.forEach((item) => {
          if (this.selectIds.includes(item.id)) {
            this.selectRows.push(item)
          }
        })
      },
      handleSizeChange(val) {
        this.pageInfo.pageSize = val
        this.pageInfo.curPage = 1
        this.fetchData()
      },
      handleCurrentChange(val) {
        this.pageInfo.curPage = val
        this.fetchData()
      },
      handleQuery() {
        this.pageInfo.curPage = 1
        this.fetchData()
      },
      resetForm(formName) {
        this.pageInfo.curPage = 1
        this.$refs[formName].resetFields()
        this.fetchData()
      },
    },
  }
</script>

<style lang="scss" scoped>
  ::v-deep .el-table__header .el-checkbox {
    display: none;
  }

  .select-content {
    margin-top: 10px;

    .select-list {
      margin-top: 10px;
    }
  }
</style>