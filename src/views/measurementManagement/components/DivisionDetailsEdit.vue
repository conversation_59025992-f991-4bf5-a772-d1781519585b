<template>
  <el-dialog
    v-drag
    append-to-body
    center
    :close-on-click-modal="false"
    top="5vh"
    :visible.sync="dialogVisible"
    width="1400px"
    @close="handleClose"
    class="division-edit-dialog"
  >
    <!-- 对话框头部信息 -->
    <div class="dialog-header-info">
      <!-- 关闭按钮 -->
      <div class="close-button" @click="handleClose">
        <i class="el-icon-close"></i>
      </div>

      <div class="header-content">
        <div class="header-text">
          <h3 class="header-title">{{ dialogTitle }}</h3>
          <p class="header-description">{{ getDialogDescription() }}</p>
        </div>
        <div class="header-icon">
          {{ getDialogIcon() }}
        </div>
      </div>
    </div>

    <div v-loading="submitLoading">
      <table class="detail-table">
        <tr class="title">
          <td colspan="4">基础信息</td>
        </tr>
        <tr>
          <td class="label">编号</td>
          <td class="value">
            <el-input
              v-model="formData.code"
              disabled
              placeholder="系统自动生成"
              readonly
              class="readonly-input"
            />
          </td>
          <td class="label">名称</td>
          <td class="value">
            <el-input
              v-model="formData.name"
              placeholder="请输入名称"
              clearable
            />
          </td>
        </tr>
        <tr>
          <td class="label">层级</td>
          <td class="value">
            <el-tag :type="getLevelTagType(formData.level)" size="medium">
              {{ getLevelName(formData.level) }}
            </el-tag>
            <span class="level-hint">
              系统根据当前选中节点自动确定
            </span>
          </td>
          <td class="label">排序</td>
          <td class="value">
            <el-input-number
              v-model="formData.sortOrder"
              :min="1"
              :max="9999"
              class="full-width"
              placeholder="请输入排序号"
              @change="updateCode"
            />
          </td>
        </tr>
        <tr>
          <td class="label">备注</td>
          <td colspan="3" class="value">
            <el-input
              v-model="formData.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息（可选）"
              maxlength="500"
              show-word-limit
              class="full-width"
            />
          </td>
        </tr>

        <tr class="title">
          <td colspan="4">附件信息</td>
        </tr>
        <tr>
          <td colspan="4">
            <!-- 附件上传 -->
            <div class="attachment-container">
              <UploadLargeFileFdfsPopup ref="UploadLargeFileFdfsPopup" />
            </div>
          </td>
        </tr>
      </table>
    </div>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="submitLoading" @click="handleSubmit">
        {{ isAdd ? '新增' : '保存' }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script>
import { saveData } from '@/api/measurementManagement/divisionDetails'
import { genUUID } from '@/utils/th_utils'
import UploadLargeFileFdfsPopup from '@/views/common/UploadLargeFileFdfsPopup.vue'

export default {
  name: 'DivisionDetailsEdit',
  components: {
    UploadLargeFileFdfsPopup
  },
  data() {
    return {
      dialogVisible: false,
      submitLoading: false,
      isAdd: true,
      formData: {
        id: '',
        code: '',
        name: '',
        level: null,
        parentId: '',
        bizId: '',
        remark: '',
        sortOrder: 0,
        isAdd: true
      },


      currentParentInfo: null // 当前父级信息
    }
  },
  computed: {
    dialogTitle() {
      if (this.isAdd) {
        return `新增${this.getLevelName(this.formData.level)}`
      } else {
        return `编辑${this.getLevelName(this.formData.level)}`
      }
    }
  },
  methods: {
    // 显示对话框
    show(data = {}, parentInfo = null) {
      this.currentParentInfo = parentInfo
      this.isAdd = data.isAdd !== false // 默认为新增

      if (this.isAdd) {
        // 新增模式
        this.initAddForm(parentInfo)
      } else {
        // 编辑模式
        this.initEditForm(data)
      }

      this.dialogVisible = true

      // 初始化文件上传组件
      this.$nextTick(() => {

        // 初始化附件上传
        if (this.$refs.UploadLargeFileFdfsPopup) {
          this.$refs.UploadLargeFileFdfsPopup.getParamsData({
            bizId: this.formData.id,
            bizCode: this.formData.bizId, // 使用第三级的ID
            isShow: true,
            title: this.isAdd ? '新增附件上传' : '编辑附件上传',
            fileType: 'all',
            onlyOneFile: false
          })
        }
      })
    },

    // 初始化新增表单
    initAddForm(parentInfo) {
      const nextLevel = parentInfo ? parentInfo.level + 1 : 1
      const defaultSortOrder = 1

      this.formData = {
        id: genUUID(),
        code: `L${nextLevel}_${String(defaultSortOrder).padStart(4, '0')}`,
        name: '',
        level: nextLevel,
        parentId: parentInfo ? parentInfo.id : '-1', // 第四层的父级id为-1
        bizId: parentInfo ? parentInfo.bizId || parentInfo.id : '', // 第三层的Id
        remark: '',
        sortOrder: defaultSortOrder,
        isAdd: true
      }

      // 如果是第四层，父级id设为-1
      if (nextLevel === 1) {
        this.formData.parentId = '-1'
      }

      console.log('新增表单初始化:', this.formData, '父级信息:', parentInfo)
    },

    // 初始化编辑表单
    initEditForm(data) {
      this.formData = {
        id: data.id || '',
        code: data.code || '',
        name: data.name || '',
        level: data.level || null,
        parentId: data.parentId || '',
        bizId: data.bizId || '',
        remark: data.remark || '',
        sortOrder: data.sortOrder || 0,
        isAdd: false
      }

      console.log('编辑表单初始化:', this.formData)
    },

    // 获取层级名称
    getLevelName(level) {
      const levelNames = {
        1: '单位工程',
        2: '分部工程',
        3: '子分部工程',
        4: '工程部位',
        5: '分项工程'
      }
      return levelNames[level] || '工程结构'
    },
    // 获取层级标签类型
    getLevelTagType(level) {
      const tagTypes = {
        1: 'primary',
        2: 'success',
        3: 'warning',
        4: 'info',
        5: 'danger'
      }
      return tagTypes[level] || 'info'
    },
    // 获取对话框描述
    getDialogDescription() {
      if (this.isAdd) {
        return `新增${this.getLevelName(this.formData.level)}，系统将自动生成编号`
      } else {
        return `编辑${this.getLevelName(this.formData.level)}信息`
      }
    },
    // 获取对话框图标
    getDialogIcon() {
      return this.isAdd ? '🆕' : '✏️'
    },

    // 更新编号
    updateCode() {
      if (this.formData.level && this.formData.sortOrder) {
        this.formData.code = `L${this.formData.level}_${String(this.formData.sortOrder).padStart(4, '0')}`
      }
    },

    // 提交表单
    async handleSubmit() {
      try {
        // 基础验证
        if (!this.formData.name) {
          this.$message.error('请输入名称')
          return
        }

        if (!this.formData.sortOrder) {
          this.$message.error('请输入排序号')
          return
        }

        this.submitLoading = true

        // 构造提交数据
        const submitData = {
          ...this.formData
        }

        console.log('提交数据:', submitData)

        // 调用保存接口
        const response = await saveData(submitData)

        if (response.code === 200) {
          this.$message.success(this.isAdd ? '新增成功' : '保存成功')
          this.dialogVisible = false

          // 触发父组件刷新
          this.$emit('success', {
            isAdd: this.isAdd,
            data: submitData
          })
        } else {
          this.$message.error(response.msg || (this.isAdd ? '新增失败' : '保存失败'))
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('提交失败:', error)
          this.$message.error(this.isAdd ? '新增失败' : '保存失败')
        }
      } finally {
        this.submitLoading = false
      }
    },

    // 关闭对话框
    handleClose() {
      this.dialogVisible = false
      // 重置表单数据
      this.formData = {
        id: '',
        code: '',
        name: '',
        level: null,
        parentId: '',
        bizId: '',
        remark: '',
        sortOrder: 1,
        isAdd: true
      }
    }
  }
}
</script>

<style lang="scss" scoped>
/* 对话框整体样式 */
.division-edit-dialog {
  ::v-deep .el-dialog__header {
    display: none !important;
  }

  ::v-deep .el-dialog__title {
    display: none !important;
  }

  ::v-deep .el-dialog__headerbtn {
    display: none !important;
  }

  ::v-deep .el-dialog__body {
    padding-top: 20px;
  }
}

/* 对话框头部信息 */
.dialog-header-info {
  margin-bottom: 24px;
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;
  position: relative;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 50px;
}

.header-text {
  flex: 1;
}

.header-title {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
}

.header-description {
  margin: 0;
  font-size: 13px;
  opacity: 0.9;
}

.header-icon {
  font-size: 32px;
  opacity: 0.8;
}

/* 自定义关闭按钮 */
.close-button {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;

  i {
    font-size: 16px;
    color: white;
    transition: all 0.3s;
  }

  &:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);

    i {
      transform: rotate(90deg);
    }
  }

  &:active {
    transform: scale(0.95);
  }
}

/* 表格样式 */
.detail-table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;

  td {
    padding: 12px;
    border: 1px solid #e6e6e6;
    vertical-align: top;
  }

  .title {
    background-color: #f5f5f5;
    font-weight: bold;
    text-align: center;
    color: #333;

    td {
      padding: 15px;
      font-size: 16px;
    }
  }

  .label {
    background-color: #fafafa;
    font-weight: 500;
    color: #606266;
    width: 120px;
    text-align: right;
  }

  .value {
    color: #303133;
    word-break: break-all;
  }
}

/* 表单控件样式 */
.full-width {
  width: 100%;
}

.readonly-input {
  ::v-deep .el-input__inner {
    background-color: #f5f7fa;
  }
}

.level-hint {
  margin-left: 8px;
  color: #909399;
  font-size: 12px;
}

/* 附件容器样式 */
.attachment-container {
  max-height: 300px;
  overflow-y: auto;
  padding: 10px;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  background-color: #fafafa;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}
</style>
