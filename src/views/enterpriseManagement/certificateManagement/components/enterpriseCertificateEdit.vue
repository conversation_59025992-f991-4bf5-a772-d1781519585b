<template>
  <el-dialog
    v-drag
    :close-on-click-modal="false"
    append-to-body
    :title="pageTitle"
    :visible.sync="dialogFormVisible"
    width="1420px"
:before-close="closeBtn"
    top="5vh"
    center
  >
    <div style="max-height: 75vh; overflow-y: scroll; overflow-x: hidden">
      <el-form
        ref="dataForm"
        v-loading="formLoading"
        :inline="true"
        :rules="rules"
        :model="formData"
        label-position="right"
        label-width="140px"
      >
        <table class="form-table">
          <tr class="title">
            <td colspan="2">企业资质证书登记</td>
          </tr>
           <tr>
             <!-- <td>
              <el-form-item label="证书状态" prop="documentStatus">
                <el-input
                  v-model="formData.documentStatus"
                  disabled
                  style="width: 250px"
                ></el-input>
              </el-form-item>
            </td> -->
             <td >
              <el-form-item label="资质所属单位" prop="certificateOwnershipUnit">
                <t-form-tree
                style="width: 250px"
                v-model="formData.certificateOwnershipUnit"
                :default-props="{
                        children: 'children',
                        label: 'departFullName',
                      }"
                :show-top="false"
                :tree-data="selectedInvoicingOrganization"
              />
             
              </el-form-item>
            </td>
              <td>
              <el-form-item label="资质管理部门" prop="depart">
                <t-form-tree v-model="formData.depart"
                  :default-props="{ children: 'children', label: 'departName' }"  :show-top="false"
                  :tree-data="departData" />
              </el-form-item>
            </td>
          </tr>
          <!-- <tr>
            <td>
              <el-form-item label="单号" prop="serialNumber">
                <el-input
                  v-model="formData.serialNumber"
                  placeholder="自动生成"
                  disabled
                  style="width: 250px"
                ></el-input>
              </el-form-item>
            </td>
            <td>
              <el-form-item label="登记人" prop="createBy">
                <el-input
                  v-model="formData.createByName"
                  disabled
                  style="width: 250px"
                ></el-input>
              </el-form-item>
            </td>
          
          </tr> -->
          <tr>

            <!-- <td>
              <el-form-item label="证书类别" prop="documentType">
                <el-select
                  v-model="formData.documentType"
                  placeholder="请选择证书类别"
                      style="width: 250px"
                >
                  <el-option
                    v-for="item in clienteleTypeList"
                    :key="item.id"
                    :label="item.dictName"
                    :value="item.id"
                
                  />
                </el-select>
              </el-form-item>
            </td> -->
          <td >
              <el-form-item label="证书名称" prop="documentType">
                <el-select
                  v-model="formData.documentType"
                  placeholder="请选择证书"
                      style="width: 250px"
                >
                  <el-option
                    v-for="item in clienteleTypeList"
                    :key="item.id"
                    :label="item.dictName"
                    :value="item.id"
                
                  />
                </el-select>
              </el-form-item>
               <!-- <el-form-item label="证书名称" prop="documentName">
                <el-input
                  v-model="formData.documentName"
                  placeholder="请输入证书名称"
                  style="width: 250px"
                />
              </el-form-item> -->
            </td>
             <td >
               <el-form-item label="证书编号" prop="documentCode">
                <el-input
                  v-model="formData.documentCode"
                  placeholder="请输入证书编号"
                  style="width: 250px"
                />
              </el-form-item>
            </td>
          </tr>
          <tr>
            <td>
              <el-form-item label="发证机关" prop="issuingAuthority">
                <el-select
                  v-model="formData.issuingAuthority"
                  placeholder="请选择发证机关"
                      style="width: 250px"
                >
                  <el-option
                    v-for="item in issuingAuthorityList"
                    :key="item.id"
                    :label="item.dictName"
                    :value="item.id"
                
                  />
                </el-select>
              </el-form-item>
            </td>
             <td>
              <el-form-item label="颁证日期" prop="recordDate">
                <el-date-picker
                  v-model="formData.recordDate"
                  type="date"
                  placeholder="请选择颁证日期"
                  style="width: 250px"
                />
              </el-form-item>
            </td>
          </tr>
          <tr>

          </tr>
          <tr>
          <td>
              <el-form-item label="有效开始时间" prop="effectiveStartTime">
                <el-date-picker
                  v-model="formData.effectiveStartTime"
                  type="date"
                  placeholder="请选择有效开始时间"
                  style="width: 250px"
                />
              </el-form-item>
            </td>
          <td>
              <el-form-item label="有效截止时间" prop="effectiveEndTime">
                <el-date-picker
                  v-model="formData.effectiveEndTime"
                  @change="changeTime"
                  type="date"
                  placeholder="请选择有效截止时间"
                  style="width: 250px"
                />
              </el-form-item>
            </td>
            <!-- <td>
              <el-form-item
                label="原件存放地"
                prop="originalStorageLocation"
              >
                <el-input
                  v-model="formData.originalStorageLocation"
                  placeholder="请输入原件存放地"
                  style="width: 250px"
                />
              </el-form-item>
            </td> -->
          </tr>
         <tr>
           <td colspan="2">
               <el-form-item label="预警人员" prop="warningPerson">
                 <t-form-user
                   v-model="formData.warningPerson"
                   placeholder="请选择"
                 />
               </el-form-item>
            </td>
         </tr>

          <tr>
            <td colspan="2">
              <el-form-item label="备注" prop="remark">
                <el-input
                  v-model="formData.remark"
                  type="textarea"
                  placeholder="请输入备注"
                  maxlength="1000"
                  style="width: 1158px"
                ></el-input>
              </el-form-item>
            </td>
          </tr>
        </table>
        <UploadLargeFileFdfsPopup ref="UploadLargeFileFdfsPopup" />
      </el-form>
    </div>
    <template #footer>
      <el-button type="danger" @click="closeBtn">关闭</el-button>
      <el-button type="primary" @click="saveBtn">保存</el-button>
    </template>
  </el-dialog>
</template>

<script>
  import { mapGetters } from 'vuex'
  import {
    saveData,
    checkData,
  } from '@/api/certificateManagement/enterpriseCertificate'
  import { genUUID, parseTime } from '@/utils/th_utils.js'
  import UploadLargeFileFdfsPopup from '@/views/common/UploadLargeFileFdfsPopup.vue'
  import { getDepartList } from "@/api/system/depart-api";
  export default {
    components: {
      UploadLargeFileFdfsPopup,
    },
    props: {
    issuingAuthorityList:{
      type: Array,
      default() {
        return []
      },
    },
    departData: {
      type: Array,
      default() {
        return []
      },
    },
      clienteleTypeList: {
        type: Array,
        default() {
          return []
        },
      },
      industryList: {
        type: Array,
        default() {
          return []
        },
      },
    },
    data() {
        const validateDate = (rule, value, callback) => {
        let startDate = Date.parse(this.formData.effectiveStartTime)
        let endDate = Date.parse(this.formData.effectiveEndTime)
        if (endDate && endDate && startDate > endDate) {
          this.formData.duration = '自动计算'
          callback(new Error('结束时间不能小于开始时间，请修改!'))
        }
        callback()
      }
      const validateDocumentName = (rule, value, callback) => {
        const data = {
          isAdd: this.formData.isAdd,
          documentName: this.formData.documentName,
        }
        if (!this.formData.isAdd) {
          data.id = this.formData.id
        }
        checkData(data).then((response) => {
          if (response.result === 200) {
            callback()
          } else {
            callback(new Error('证书名称已存在，请修改!'))
          }
        })
      }
      //   const validateDocumentCode = (rule, value, callback) => {
      //   const data = {
      //     isAdd: this.formData.isAdd,
      //     documentCode: this.formData.documentCode,
      //   }
      //   if (!this.formData.isAdd) {
      //     data.id = this.formData.id
      //   }
      //   checkData(data).then((response) => {
      //     if (response.result === 200) {
      //       callback()
      //     } else {
      //       callback(new Error('证书编号已存在，请修改!'))
      //     }
      //   })
      // }
      return {
        departTreeData: [],
        selectedInvoicingOrganization: [],
        dialogFormVisible: false,
        formLoading: false,
        formData: {},
        rules: {
          recordDate: [
            { required: true, message: '颁证日期为必选', trigger: 'change' },
          ],
          depart :[
            { required: true, message: '资质管理部门为必填', trigger: 'blur' },
          ],
          documentType: [
            { required: true, message: '证书名称为必填', trigger: 'change' },
            // { validator: validateDocumentName, trigger: 'blur' },
          ],
            documentCode: [
            { required: true, message: '证书编号为必填', trigger: 'blur' },
          ],
            effectiveStartTime: [
            { required: true, message: '请选择有效开始时间', trigger: 'change' },
            { validator: validateDate, trigger: 'change' },
          ],
          effectiveEndTime: [
            // { required: true, message: '请选择有效截止时间', trigger: 'change' },
            { validator: validateDate, trigger: 'change' },
          ],
        },
      }
    },
    computed: {
      ...mapGetters({
        userId: 'user/userId',
        userName: 'user/userName',
        departList: 'acl/departList',
      }),
      pageTitle() {
        return this.formData.isAdd ? '新增证书' : '编辑证书'
      },
    },
    created() {
    this.getDepartList()
  },
    methods: {
      async showDialog(row) {
        this.formData = { ...row }
        if (this.formData.isAdd) {
          this.init()
        }
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs.UploadLargeFileFdfsPopup.getParamsData({
            bizId: this.formData.id,
            bizCode: this.$route.name,
          })
        })
      },
      init() {
        this.formData = {
          id: genUUID(),
          serialNumber: '',
          createDepart: this.departList[0].id,
          createDepartName: this.departList[0].departName,
          recordDate: parseTime(new Date()),
          departCode: this.departList[0].departCode,
          createBy: this.userId,
          createByName: this.userName,
          viewUser: this.userId,
          operationCode: 'ZJGL',
          serialNumberTable: 'enterprise_certificate',
          documentType: '',
          documentStatus:'',
          documentTypeName: '',
          documentCode: '',
          documentName: '',
          effectiveStartTime: '',
          effectiveEndTime: '',
          originalStorageLocation: '',
          certificateOwnershipUnit: '',
          remark: '',
          isAdd: true,
        }
      },

      closeBtn() {
        this.$refs.UploadLargeFileFdfsPopup.closeDelImg()
        this.formLoading = false
        this.dialogFormVisible = false
        this.$refs.dataForm.resetFields()
        this.init()
      },
      saveBtn() {
        this.$refs.dataForm.validate((valid) => {
          if (valid) {
            this.formLoading = true
            const formData = {
              ...this.formData,
            }
            saveData(formData).then((res) => {
              if (res.code === 200) {
                this.$baseMessage(
                  '保存成功!',
                  'success',
                  'vab-hey-message-success'
                )
                this.$emit('refreshPage')
                this.formLoading = false
                this.dialogFormVisible = false
              }
            })
          }
        })
      },
       getDepartList() {

      getDepartList({}).then((response) => {
        this.departTreeData = response.result
        this.selectedInvoicingOrganization = this.departTreeData.filter(
          (departInfo) => {
            return departInfo.departType.indexOf('2') === -1
          }
        )
      })
    },
     changeTime(){
         var newDate = parseTime(new Date(),'{y}-{m}-{d}')
         var effectiveEndTime = parseTime(this.formData.effectiveEndTime,'{y}-{m}-{d}')
         effectiveEndTime = effectiveEndTime + ' 23:59:59'
        var diff = new Date(newDate).getTime() - new Date(effectiveEndTime).getTime()
        //var day =  Math.floor(diff/(1000*3600*24))
        this.formData.effectiveEndTime = new Date(effectiveEndTime)
        if(diff <= 0){
          this.formData.documentStatus = '正常'
        }else{
           this.formData.documentStatus = '失效'
        }

       },
    },
  }
</script>

<style lang="scss" scoped>
  .dropTree {
    width: 100%;
    max-height: 300px;
    overflow: auto;
  }
</style>
