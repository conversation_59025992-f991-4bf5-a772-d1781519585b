<template>
  <div
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="80px"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item label="名称" prop="personnel">
            <el-input
              v-model="queryForm.personnel"
              clearable
              placeholder="请输入证书所属单位/姓名"
              style="width: 250px"
            />
          </el-form-item>
          <el-form-item label="证书名称" prop="certificateName">
            <el-input
              v-model="queryForm.certificateName"
              clearable
              placeholder="请输入证书名称"
              style="width: 250px"
            />
          </el-form-item>
          <el-form-item label="证书状态" prop="documentStatus">
            <el-select
              ref="selectStatus"
              v-model="queryForm.documentStatus"
              filterable
              placeholder="请选择证书状态"
              style="width: 250px"
            >
              <el-option option value="">所有状态</el-option>
              <el-option
                v-for="item in queryDocumentStatus"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="使用情况" prop="usageSituation">
            <el-select
              ref="selectUsageSituation"
              v-model="queryForm.usageSituation"
              filterable
              placeholder="请选择证书状态"
              style="width: 250px"
            >
              <el-option
                v-for="item in usageSituationList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="有效日期" prop="expirationTime">
            <el-date-picker
              v-model="queryForm.expirationTime"
              end-placeholder="结束时间"
              range-separator="至"
              start-placeholder="开始时间"
              style="width: 400px"
              type="daterange"
            />
          </el-form-item>

          <el-form-item>
            <el-button
              icon="el-icon-search"
              native-type="submit"
              type="primary"
              @click="handleQuery"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-right"
              @click.native="resetFormPage"
            >
              重置
            </el-button>
            <el-button
              style="margin: 0 0 0 10px !important"
              type="text"
              @click="handleFold"
            />
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
      <vab-query-form-left-panel :span="12">
        <el-button
          v-permissions="{ permission: ['certificateUse:add'] }"
          icon="el-icon-plus"
          type="primary"
          @click="handleAdd"
        >
          添加
        </el-button>
        <!-- <el-button
          icon="el-icon-delete"
          type="danger"
          @click="batchDelete"
          v-permissions="{ permission: ['certificateUse:del'] }"
        >
          批删
        </el-button> -->
        <el-button
          :disabled="exportLoading"
          icon="el-icon-refresh-right"
          type="warning"
          @click.native="exportBtn('证书使用.xlsx')"
        >
          导出
        </el-button>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="12">
        <el-button
          style="margin: 0 10px 10px 0 !important"
          type="primary"
          @click="clickFullScreen"
        >
          <vab-icon
            :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'"
          />
          表格全屏
        </el-button>
        <el-popover
          ref="popover"
          popper-class="custom-table-checkbox"
          trigger="hover"
        >
          <el-radio-group v-model="lineHeight">
            <el-radio label="medium">大</el-radio>
            <el-radio label="small">中</el-radio>
            <el-radio label="mini">小</el-radio>
          </el-radio-group>
          <template #reference>
            <el-button style="margin: 0 10px 10px 0 !important" type="primary">
              <vab-icon icon="line-height" />
              表格尺寸
            </el-button>
          </template>
        </el-popover>
        <el-popover popper-class="custom-table-checkbox" trigger="hover">
          <el-checkbox-group v-model="checkList">
            <vab-draggable v-bind="dragOptions" :list="columns">
              <div v-for="(item, index) in columns" :key="item + index">
                <vab-icon icon="drag-drop-line" />
                <el-checkbox
                  :disabled="item.disableCheck === true"
                  :label="item.label"
                >
                  {{ item.label }}
                </el-checkbox>
              </div>
            </vab-draggable>
          </el-checkbox-group>
          <template #reference>
            <el-button
              icon="el-icon-setting"
              style="margin: 0 0 10px 0 !important"
              type="primary"
            >
              可拖拽列设置
            </el-button>
          </template>
        </el-popover>
      </vab-query-form-right-panel>
    </vab-query-form>
    <el-table
      ref="tableSort"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      :size="lineHeight"
      stripe
      @selection-change="setSelectRows"
    >
      <el-table-column
        align="center"
        :selectable="isDeleteFlag"
        type="selection"
        width="55"
      />
      <el-table-column align="center" label="序号" width="60">
        <template #default="{ $index }">
          {{ getIndex($index) }}
        </template>
      </el-table-column>
      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        :align="item.center ? item.center : 'center'"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'"
      >
        <template #default="{ row }">
          <span v-if="item.label === '颁发日期'">
            {{ row[item.prop] | dateformat('YYYY-MM-DD') }}
          </span>
          <span v-if="item.label === '使用状态'">
            <el-tag
              :type="
                row.usageSituation === '0'
                  ? 'success'
                  : row.usageSituation === '1'
                  ? 'warning'
                  : ''
              "
            >
              {{ getStatuseName(row[item.prop]) }}
            </el-tag>
          </span>
          <span v-else-if="item.label === '开始日期'">
            {{ row[item.prop] | dateformat('YYYY-MM-DD') }}
          </span>
          <span v-else-if="item.label === '结束日期'">
            {{ row[item.prop] | dateformat('YYYY-MM-DD') }}
          </span>
          <span v-else>
            {{ row[item.prop] }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="300">
        <template #default="{ row }">
          <el-button
            v-permissions="{ permission: ['certificateUse:update'] }"
            :disabled="isDisabledEditFun(row)"
            icon="el-icon-edit"
            style="margin: 0 10px 10px 0 !important"
            type="primary"
            @click="handleEdit(row)"
          >
            编辑
          </el-button>
          <el-button
            icon="el-icon-view"
            style="margin: 0 10px 10px 0 !important"
            type="success"
            @click="handleDetail(row)"
          >
            详情
          </el-button>
          <el-dropdown>
            <el-button size="mini" type="success">
              <i class="el-icon-more" />
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <!-- <el-dropdown-item @click.native.prevent="showReport(row)"><i class="el-icon-s-order" />查看报表</el-dropdown-item> -->
              <!-- <el-dropdown-item
                v-if="isStartFlowFlag(row)"
                @click.native.prevent="startWorkFlow(row)"
              >
                <i class="el-icon-video-play" />
                启动流程
              </el-dropdown-item> -->
              <!-- <el-dropdown-item
                v-if="isDeleteFlowFlag(row)"
                @click.native.prevent="deleteWorkFlow(row)"
              >
                <i class="el-icon-refresh-left" />
                撤回流程
              </el-dropdown-item> -->
              <el-dropdown-item
                v-if="isDeleteFlag(row)"
                v-permissions="{ permission: ['certificateUse:del'] }"
                style="color: #fd5353"
                @click.native.prevent="handleDelete(row)"
              >
                <i class="el-icon-delete" />
                删除
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <tableDetail ref="tableDetail" />
    <tableEdit
      ref="tableEdit"
      :clientele-type-list="clienteleTypeList"
      :depart-data="allDepartList"
      :industry-list="industryList"
      :issuing-authority-list="issuingAuthorityList"
    />
    <tableAddType ref="tableAddType" @refreshPage="resetFormPage" />
    <VabTableExport ref="tableExport" />
    <!-- 审批部门选择 -->
    <!-- <VabWorkFlowDepart ref="VabWorkFlowDepart"  /> -->
    <!-- 流程启动动态审批条件处理 -->
    <!-- <VabStartFlowProcess ref="VabStartFlowProcess" /> -->
  </div>
</template>

<script>
  import tableMix from '@/views/mixins/table'
  import tableDetail from './components/certificateUseDetail.vue'
  import tableEdit from './components/certificateUseEdit.vue'
  import tableAddType from './components/certificateUseTypeDialog.vue'
  import { getDictList } from '@/api/system/dict-api'
  import {
    getDataListByPage,
    deleteData,
    exportData,
  } from '@/api/certificateManagement/ceritificateUse'

  export default {
    name: 'CertificateUse',
    components: {
      tableDetail,
      tableEdit,
      tableAddType,
    },
    mixins: [tableMix],
    data() {
      return {
        formMaxHeight: 1,
        columns: [
          {
            label: '证书所属单位或人员',
            prop: 'personnel',
            width: '250',
          },
          {
            label: '证书名称',
            prop: 'certificateName',
            width: '250',
          },
          {
            label: '证书编号',
            prop: 'certificateCode',
            width: '250',
          },
          {
            label: '使用事由',
            prop: 'affair',
            width: '250',
          },
          {
            label: '使用部门',
            prop: 'certificateDepartName',
            width: '250',
          },
          {
            label: '开始日期',
            prop: 'startTime',
            width: '120',
          },
          {
            label: '结束日期',
            prop: 'endTime',
            width: '120',
          },
          {
            label: '使用状态',
            prop: 'usageSituation',
          },
        ],
        departData: [],
        queryForm: {
          certificateDepartName: '',
          certificateName: '',
          documentStatus: '',
          usageSituation: '',
          queryDate: '',
          expirationTime: '',
        },
        clienteleTypeList: [],
        issuingAuthorityList: [],
        industryList: [],
        queryDocumentStatus: [
          { id: 'effective', name: '有效' },
          { id: 'inVain', name: '无效' },
        ],
        usageSituationList: [
          { id: 'effective', name: '闲置' },
          { id: 'inVain', name: '使用' },
        ],
      }
    },
    created() {
      this.initDefaultCheck()
      this.getDictListByCodes()
      this.fetchData()
    },
    methods: {
      getStatuseName(status) {
        if (status === '1') {
          return '使用中'
        } else {
          return '闲置'
        }
      },
      fetchData() {
        this.listLoading = true
        if (this.queryForm.queryDate.length >= 2) {
          this.queryForm.queryDateStart = this.queryForm.queryDate[0]
          this.queryForm.queryDateEnd = this.queryForm.queryDate[1]
        } else {
          this.queryForm.queryDateStart = ''
          this.queryForm.queryDateEnd = ''
        }
        this.listLoading = true
        if (this.queryForm.expirationTime.length >= 2) {
          this.queryForm.expirationTimeStart = this.queryForm.expirationTime[0]
          this.queryForm.expirationTimeEnd = this.queryForm.expirationTime[1]
        } else {
          this.queryForm.expirationTimeStart = ''
          this.queryForm.expirationTimeEnd = ''
        }
        const queryForm = {
          ...this.queryForm,
          curPage: this.pageInfo.curPage,
          pageSize: this.pageInfo.pageSize,
        }

        getDataListByPage(queryForm).then((res) => {
          this.listLoading = false
          const {
            result: { total, records },
          } = res
          this.list = records
          this.pageInfo.total = Number(total)
        })
      },
      // 添加
      handleAdd() {
        this.$refs['tableAddType'].showDialog({ isAdd: true })
      },
      // 编辑
      handleEdit(row) {
        this.$refs['tableEdit'].showDialog({
          isAdd: false,
          ...row,
        })
      },
      //详情
      handleDetail(row) {
        this.$refs['tableDetail'].showDialog(row)
      },
      // 删除
      handleDelete(row) {
        if (row.id) {
          this.$baseConfirm('你确定要删除当前数据吗', null, async () => {
            deleteData({ id: row.id })
              .then(() => {
                this.pageInfo.curPage = 1
                this.batchDeleteImg(row.id, this.$route.name)
                this.fetchData()
                this.$baseMessage(
                  '删除成功!',
                  'success',
                  'vab-hey-message-success'
                )
              })
              .catch(() => {
                this.$baseMessage('删除失败!', 'error', 'vab-hey-message-error')
              })
          })
        }
      },
      // 批删
      batchDelete() {
        if (this.selectRows.length === 0) {
          this.$baseMessage(
            '请选中最少一条记录!',
            'error',
            'vab-hey-message-error'
          )
          return
        }
        const idArr = this.selectRows.map((item) => item.id).join(',')
        this.$baseConfirm('你确定要删除选中数据 吗', null, async () => {
          deleteData({ id: idArr })
            .then(() => {
              this.pageInfo.curPage = 1
              this.batchDeleteImg(idArr, this.$route.name)
              this.fetchData()
              this.$baseMessage(
                '删除成功!',
                'success',
                'vab-hey-message-success'
              )
            })
            .catch(() => {
              this.$baseMessage('删除失败!', 'error', 'vab-hey-message-error')
            })
        })
      },
      exportBtn(excelName) {
        this.queryForm.excelName = excelName
        this.$refs['tableExport'].showDialog(this.columns)
      },
      exportData(columns) {
        this.exportLoading = true
        exportData({ ...this.queryForm, ...columns }).then((response) => {
          const link = document.createElement('a')
          link.download = this.queryForm.excelName
          link.href = window.URL.createObjectURL(new Blob([response]))
          document.body.appendChild(link)
          link.click()
          link.download = ''
          document.body.removeChild(link)
          URL.revokeObjectURL(response)
        })
        this.exportLoading = false
      },
      resetFormPage() {
        this.pageInfo.curPage = 1
        this.fetchData()
      },
      // 获取数据字典
      getDictListByCodes() {
        getDictList({
          dictCode: 'personnelCertificateManagement,issuingAuthority',
        }).then((res) => {
          const { result } = res
          this.clienteleTypeList = result[0].children.map((item) => {
            return {
              dictName: item.dictName,
              id: item.id,
            }
          })
          this.issuingAuthorityList = result[1].children.map((item) => {
            return {
              dictName: item.dictName,
              id: item.id,
            }
          })
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  $base: '.depart-management';
  #{$base}-container {
    padding: 0 !important;
    background: $base-color-background !important;

    &.vab-fullscreen {
      padding: 20px !important;
    }
  }
</style>
