<template>
  <div
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="100px"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item label="资质类别名称" prop="dictName">
            <el-input
              v-model="queryForm.dictName"
              clearable
              placeholder="请输入资质类别名称"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              icon="el-icon-search"
              native-type="submit"
              type="primary"
              @click="handleQuery"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-right"
              @click.native="resetFormPage"
            >
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
      <vab-query-form-left-panel :span="12">
        <el-button
          v-permissions="{ permission: ['personnelCertificateManagement:add'] }"
          icon="el-icon-plus"
          type="primary"
          @click="handleAdd"
        >
          添加
        </el-button>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="12">
        <el-button
          style="margin: 0 10px 10px 0 !important"
          type="primary"
          @click="clickFullScreen"
        >
          <vab-icon
            :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'"
          />
          表格全屏
        </el-button>
        <el-popover
          ref="popover"
          popper-class="custom-table-checkbox"
          trigger="hover"
        >
          <el-radio-group v-model="lineHeight">
            <el-radio label="medium">大</el-radio>
            <el-radio label="small">中</el-radio>
            <el-radio label="mini">小</el-radio>
          </el-radio-group>
          <template #reference>
            <el-button style="margin: 0 10px 10px 0 !important" type="primary">
              <vab-icon icon="line-height" />
              表格尺寸
            </el-button>
          </template>
        </el-popover>
        <el-popover popper-class="custom-table-checkbox" trigger="hover">
          <el-checkbox-group v-model="checkList">
            <vab-draggable v-bind="dragOptions" :list="columns">
              <div v-for="(item, index) in columns" :key="item + index">
                <vab-icon icon="drag-drop-line" />
                <el-checkbox
                  :disabled="item.disableCheck === true"
                  :label="item.label"
                >
                  {{ item.label }}
                </el-checkbox>
              </div>
            </vab-draggable>
          </el-checkbox-group>
          <template #reference>
            <el-button
              icon="el-icon-setting"
              style="margin: 0 0 10px 0 !important"
              type="primary"
            >
              可拖拽列设置
            </el-button>
          </template>
        </el-popover>
      </vab-query-form-right-panel>
    </vab-query-form>
    <el-table
      ref="tableSort"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      :size="lineHeight"
      stripe
      @selection-change="setSelectRows"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column align="center" label="序号" width="60">
        <template #default="{ $index }">
          {{ getIndex($index) }}
        </template>
      </el-table-column>
      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        :align="item.center ? item.center : 'center'"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'"
      >
        <template #default="{ row }">
          <span>
            {{ row[item.prop] }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="300">
        <template #default="{ row }">
          <el-button
            v-permissions="{ permission: ['personnelCertificateManagement:update'] }"
            icon="el-icon-edit"
            style="margin: 0 10px 10px 0 !important"
            type="primary"
            @click="handleEdit(row)"
          >
            编辑
          </el-button>
          <!--          <el-button-->
          <!--            v-permissions="{ permission: ['personnelCertificateManagement:del'] }"-->
          <!--            icon="el-icon-delete"-->
          <!--            style="margin: 0 10px 10px 0 !important"-->
          <!--            type="danger"-->
          <!--            @click="handleDelete(row)"-->
          <!--          >-->
          <!--            删除-->
          <!--          </el-button>-->
        </template>
      </el-table-column>
      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <!-- 新增修改 -->
    <tableEdit
      ref="tableEdit"
      :parent-id="parentId"
      :total="pageInfo.total"
      @refreshPage="resetFormPage"
    />
  </div>
</template>

<script>
  import tableMix from '@/views/mixins/table'
  import tableEdit from './components/personnelCertificateManagementEdit.vue'
  import { getDataListByPage, deleteData } from '@/api/system/dict-api'
  export default {
    name: 'PersonnelCertificateManagement',
    components: {
      tableEdit,
    },
    mixins: [tableMix],
    data() {
      return {
        columns: [
          {
            label: '资质类别名称',
            prop: 'dictName',
          },
          {
            label: '资质类别说明',
            prop: 'remark',
          },
          {
            label: '排序',
            prop: 'sort',
            width: 80,
          },
        ],
        queryForm: {
          dictName: '',
        },
        parentId: '89df03a7-a390-4c0c-8786-4534bca06c26',
        defaultProps: {
          children: 'children',
          label: 'label',
        },
      }
    },
    created() {
      this.fetchData()
    },
    mounted() {
      this.checkList = ['资质类别名称', '资质类别说明']
    },
    methods: {
      fetchData() {
        this.listLoading = true
        let queryForm = {
          ...this.queryForm,
          parentId: this.parentId,
          curPage: this.pageInfo.curPage,
          pageSize: this.pageInfo.pageSize,
        }
        getDataListByPage(queryForm).then((res) => {
          this.listLoading = false
          const {
            result: { total, records },
          } = res
          this.list = records
          this.pageInfo.total = Number(total)
        })
      },
      // 编辑
      handleEdit(row) {
        this.$refs['tableEdit'].showDialog({ isAdd: false, ...row })
      },
      // 删除
      handleDelete(row) {
        if (row.id) {
          this.$baseConfirm('你确定要删除当前数据吗', null, async () => {
            deleteData(row.id)
              .then(() => {
                this.pageInfo.curPage = 1
                this.fetchData()
                this.$baseMessage(
                  '删除成功!',
                  'success',
                  'vab-hey-message-success'
                )
              })
              .catch(() => {
                this.$baseMessage('删除失败!', 'error', 'vab-hey-message-error')
              })
          })
        }
      },
      // 添加
      handleAdd() {
        this.$refs['tableEdit'].showDialog({ isAdd: true })
      },
    },
  }
</script>

<style lang="scss" scoped>
  $base: '.depart-management';
  #{$base}-container {
    padding: 0 !important;
    background: $base-color-background !important;

    &.vab-fullscreen {
      padding: 20px !important;
    }
  }
</style>
