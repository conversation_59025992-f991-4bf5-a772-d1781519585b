<template>
  <div class="todo-box">
    <div
      class="approval-list"
      style="background: linear-gradient(298deg, #fff6ed, #fefaf6, #fff6ed)"
      @click="viewMore"
    >
      我收到的
      <span>{{ copyTotal }}</span>
      <div class="r_img"><img alt="" src="~@/assets/work_icon2.png" /></div>
    </div>
    <div class="approval-content">
      <div class="box-head">
        <span>我收到的</span>
        <a href="javascript:void(0)" @click="viewMore">更多</a>
      </div>
      <div class="box-content">
        <div v-if="copyData.length > 0" class="list">
          <div
            v-for="(item, index) in copyData"
            :key="'copySendToMe' + index + item.id"
            class="item"
            @click="pageJump(item)"
          >
            <div class="itme_flex">
              <div class="isRead">
                <el-tag v-if="item.isRead === '0'" size="mini" type="danger">
                  未读
                </el-tag>
                <el-tag v-else size="mini" type="success">已读</el-tag>
              </div>
              <div class="con">
                <!--                :class="{-->
                <!--                redTitle:-->
                <!--                item.bizCode === 'projectPaymentCollection' ||-->
                <!--                item.bizCode === 'payslip',-->
                <!--                }"-->
                <a :title="item.titleName">
                  {{ item.titleName }}
                </a>
                <div class="timeBox">
                  <div class="time">
                    {{ item.createTime | dateformat('YYYY-MM-DD') }}
                  </div>
                  <div class="date">
                    {{ item.createTime | dateformat('HH:mm:ss') }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <NoData v-else>暂无事项</NoData>
      </div>
    </div>
  </div>
</template>

<script>
  import NoData from './noData.vue'
  import {
    getCopySendToMe,
    saveWorkflowConfirmation,
  } from '@/api/workflow/workflow-api.js'
  import { mapGetters } from 'vuex'
  import { genUUID } from '@/utils/th_utils'

  export default {
    components: {
      NoData,
    },
    data() {
      return {
        copyTotal: 0,
        copyData: [],
      }
    },
    computed: {
      ...mapGetters({
        userId: 'user/userId',
        oneDimensionRoutes: 'routes/oneDimensionRoutes',
      }),
    },
    created() {
      this.getCopySendToMe()
    },
    methods: {
      getCopySendToMe() {
        const queryData = {}
        queryData.curPage = 1
        queryData.pageSize = 500
        queryData.userId = this.userId
        queryData.toDoType = 'copy'
        getCopySendToMe(queryData).then((response) => {
          this.copyData = response.result.records
          this.copyTotal = Number(response.result.total)
        })
      },
      async pageJump(item) {
        const params = {
          processInstanceId: item.processInstanceId,
          toDoType: item.toDoType,
          taskId: item.taskId,
          isAdd: true,
          id: genUUID(),
        }
        // 未读
        if (item.isRead === '0') {
          await saveWorkflowConfirmation(params)
        }
        const routePath = this.oneDimensionRoutes.find(
          (val) => val.name === item.bizCode
        )
        if (routePath) {
          const path = `${routePath.path}?id=${item.id}&&reach=detail`
          // this.$openPage(path)
          this.$router.push(path)
        } else {
          this.$baseMessage(
            '跳转失败，找不到页面路径。',
            'error',
            'vab-hey-message-error'
          )
        }
      },
      viewMore() {
        const path = '/personalCenter/myReceive'
        this.$openPage(path)
      },
    },
  }
</script>
<style lang="scss" scoped>
  .redTitle {
    color: #00cb34 !important;
  }
</style>
