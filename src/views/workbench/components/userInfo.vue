<template>
  <div class="user-info">
    <span class="setting" @click="toPersonCenter">设置</span>
    <div class="user-avatar">
      <img :src="avatar" />
    </div>
    <div class="user-name">{{ userName }}, {{ hello }}</div>
    <div class="user-role">
      <el-tag v-for="(item, index) in roleData" :key="index" size="mini">
        {{ item }}
      </el-tag>
    </div>
    <div class="user-daily-message">
      <div class="daily-time">
        <span>{{ currentMonth }}-{{ currentDay }}</span>
        <a>每日寄语</a>
      </div>
      <div class="daily-message">
        <a>{{ dailyMessage }}</a>
      </div>
    </div>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex'
  export default {
    data() {
      return {
        hello: '',
        currentMonth: '',
        currentDay: '',
        dailyMessage: '',
      }
    },
    computed: {
      ...mapGetters({
        avatar: 'user/avatar',
        userName: 'user/userName',
        rolesInfo: 'acl/rolesInfo',
      }),
      roleData() {
        return this.rolesInfo.map((item) => item.roleName)
      },
    },
    created() {
      const dailyMessages = [
        '成功源于坚持，今天也要元气满满！',
        '每一次努力都是为未来积累力量，加油！',
        '积极面对挑战，勇敢迎接新一天的工作！',
        '团队的力量无穷大，感谢有你！',
        '今天的你比昨天更强大，继续努力吧！',
        '保持热情，用心成就每一件小事！',
        '微笑是最好的名片，带着好心情工作！',
        '目标明确，脚踏实地，一步步实现梦想！',
        '每一个小目标的达成，都是伟大的开始！',
        '相信自己，团队的每一步都因为你更精彩！',
      ]
      const randomIndex = Math.floor(Math.random() * dailyMessages.length)
      this.dailyMessage = dailyMessages[randomIndex]
      this.getHelloInfo()
      this.getCurrentDate()
    },
    methods: {
      getCurrentDate() {
        const now = new Date()
        this.currentMonth = now.getMonth() + 1 // getMonth() returns 0-11, so add 1
        this.currentDay = now.getDate()
      },
      getHelloInfo() {
        // fetch('https://api.vvhan.com/api/ian/rand?type=json')
        //   .then((res) => res.json())
        //   .then((data) => {
        //     this.content = data.data.content
        //   })
        const hour = new Date().getHours()
        this.hello =
          hour < 8
            ? '早上好'
            : hour <= 11
            ? '上午好'
            : hour <= 13
            ? '中午好'
            : hour < 18
            ? '下午好'
            : '晚上好'
      },
      toPersonCenter() {
        this.$router.push({ name: 'personCenter' })
        // const routerResolve = this.$router.resolve({ name: 'personCenter' })
        // window.open(`${routerResolve.href}`,'_blank');
      },
    },
  }
</script>
