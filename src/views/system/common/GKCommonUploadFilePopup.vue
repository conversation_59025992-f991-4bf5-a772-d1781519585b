<template>
  <el-dialog
    v-drag
    :title="title"
    :visible.sync="isShowDialog"
    width="600px"
    :close-on-click-modal="false"
    append-to-body
  >
    <el-upload
      ref="fileUpload"
      :file-list="uploadFileList"
      class="upload"
      action=""
      :http-request="handleUpload"
      :on-remove="handleRemove"
      :drag="true"
      :limit="num"
      style="width: 450px;"
    >
      <i class="el-icon-upload" />
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      <div slot="tip" class="el-upload__tip">上传文件不超过1Gb</div>
    </el-upload>
    <div slot="footer" class="dialog-footer">
      <el-button type="danger" @click="isShowDialog = false">
        关闭
      </el-button>
      <el-button type="primary" :loading="saveBtn" :disabled="saveBtn" @click="batchUpload">
        {{ saveText }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { newUploadFile } from '@/api/system/uploadFile-api'
import request from '@/utils/gkRequest'
export default {
  name: 'CommonUploadFilePopup',
  data() {
    return {
      saveBtn: false,
      saveText: '上传',
      title: '上传附件',
      isShowDialog: false,
      uploadFileList: [],
      fileLists: [],
      bizCode: '',
      bizId: '',
      dictType: '',
      dictId: '',
      isOneFile: false,
      num: 99
    }
  },
  methods: {
    showUploadFileDialog(bizId, bizCode, isOneFile, dictId, dictType) {
      this.isShowDialog = true
      this.$nextTick(function() {
        this.$refs.fileUpload.clearFiles()
        this.bizId = bizId
        this.bizCode = bizCode
        this.dictId = dictId
        this.dictType = dictType
        if (isOneFile) {
          this.num = 1
        }
        this.fileLists = []
        this.uploadFileList = []
      })
    },
    handleRemove(file, fileList) {
      if (file.status === 'ready') {
        this.fileLists.splice(this.fileLists.findIndex(item => item.name === file.name), 1)
      }
    },
    handleUpload(raw) {
      this.fileLists.push(raw.file)
    },
    async uploadGk(params){
      const self = this
      await request({
        url: '/proxy/newupload/oldmultiUpload',
        method: 'post',
        data: params
      }).then(response => {
        this.$baseMessage('上传成功！', 'success', 'vab-hey-message-success')
        self.$emit('refreshUploadFileList')
        self.isShowDialog = false
        setTimeout(() => {
          this.saveBtn = false
          this.saveText = '上传'
        }, 500)
    })
      return false
    },
    batchUpload() {
      const fileList = this.fileLists
      const params = new FormData()
      if (fileList.length < 1) {
        this.$baseMessage('请选择最少一个文件！', 'error', 'vab-hey-message-error')
        return
      }
      fileList.forEach(file => {
        params.append('file', file)
      })
      params.append('bizCode', this.bizCode)
      params.append('bizId', this.bizId)
      params.append('dictId', this.dictId)
      params.append('dictType', this.dictType)
      this.uploadGk(params)
    }
  }
}
</script>

<style scoped>

</style>
