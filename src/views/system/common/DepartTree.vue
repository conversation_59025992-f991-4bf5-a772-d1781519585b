<template>
  <el-card shadow="hover" body-style="overflow:auto;">
      <div slot="header" class="clearfix">
        <span>组织机构</span>
      </div>
      <el-input v-model="filterText" clearable placeholder="输入关键字进行过滤" style="margin-bottom: 10px;" />
      <el-tree
        ref="departTree"
        v-loading="treeLoading"
        node-key="id"
        :data="treeData"
        :props="defaultProps"
        highlight-current
        default-expand-all
        :filter-node-method="filterNode"
        :expand-on-click-node="false"
        @node-click="departTreeClick"
      >
        <span slot-scope="{node,data}" class="custom-tree-node">
          <span v-if="data.children.length == 0" :title="data.label" class="show-ellipsis"><i class="el-icon-user" />{{ data.label }}</span>
          <span v-else-if="data.children.length !== 0" :title="data.label" class="show-ellipsis"><vab-icon-mix
          icon="organization"/> {{ data.label }}</span>
        </span>
      </el-tree>
  </el-card>
</template>

<script>
import { formatEleDepartTree, formatEleDropDownTree, attachTopNode } from '@/utils/util.js'
import { getDepartList } from '@/api/system/depart-api'

export default {
  name: 'TableEdit',
  components: { },
  props:{
  },
  data() {
    return {
      treeLoading: false,
      treeData: [],
      filterText: '',
      defaultProps: {
        children: 'children',
        label: 'label'
      },
    }
  },
  watch: {
    // 根据关键词过滤
    filterText(val) {
      this.$refs.departTree.filter(val)
    }
  },
  created() {
    this.initTree()
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    departTreeClick(obj, node, data) {
      this.$emit('change', obj)
    },
    async initTree() {
      this.treeLoading = true
      const { result } = await getDepartList({})
      this.treeData = formatEleDepartTree(result, '-1')
      this.treeData = attachTopNode(this.treeData, '根机构', )
      this.treeLoading = false
    }
  }
}
</script>

<style scoped lang="scss">
.custom-tree-node {
width: calc(100% - 24px);
}

.show-ellipsis {
display: block;
width: 100%;
overflow: hidden;
white-space: nowrap;
text-overflow: ellipsis;
}
</style>
