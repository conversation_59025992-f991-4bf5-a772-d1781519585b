<template>
  <div ref="custom-table"  class="custom-table-container"  :class="{ 'vab-fullscreen': isFullscreen }">
    <vab-query-form>
      <vab-query-form-left-panel>
        <el-button icon="el-icon-circle-plus" type="primary" @click="handleAdd">新增</el-button>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel>
        <el-button style="margin: 0 10px 10px 0 !important" type="primary"  @click="clickFullScreen">
          <vab-icon :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'"/>表格全屏</el-button>
        <el-popover ref="popover" popper-class="custom-table-checkbox" trigger="hover">
          <el-radio-group v-model="lineHeight">
            <el-radio label="medium">大</el-radio>
            <el-radio label="small">中</el-radio>
            <el-radio label="mini">小</el-radio>
          </el-radio-group>
          <template #reference>
            <el-button style="margin: 0 10px 10px 0 !important" type="primary">
              <vab-icon icon="line-height" />表格尺寸</el-button>
          </template>
        </el-popover>
        <el-popover popper-class="custom-table-checkbox" trigger="hover">
          <el-checkbox-group v-model="checkList">
            <vab-draggable v-bind="dragOptions" :list="columns">
              <div v-for="(item, index) in columns" :key="item + index">
                <vab-icon icon="drag-drop-line" />
                <el-checkbox :disabled="item.disableCheck === true" :label="item.label">{{ item.label }}</el-checkbox>
              </div>
            </vab-draggable>
          </el-checkbox-group>
          <template #reference>
            <el-button icon="el-icon-setting" style="margin: 0 0 10px 0 !important" type="primary">可拖拽列设置</el-button>
          </template>
        </el-popover>
      </vab-query-form-right-panel>
    </vab-query-form>

    <el-table
      ref="dataForm"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      row-key="id"
      :size="lineHeight"
    >
      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        :align="item.center ? item.center : 'center'"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'"
      ></el-table-column>

      <el-table-column align="center" label="操作" width="240"  fixed="right">
        <template #default="{ row }">
          <el-button
            type="primary"
            icon="el-icon-edit"
            @click="handleEdit(row)"
            style="margin: 0 10px 10px 0 !important"
          >编辑
          </el-button>
          <el-button
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(row)"
            style="margin: 0 10px 10px 0 !important"
          >删除
          </el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>
    <el-pagination
     background
     :current-page="pageInfo.curPage"
     :layout="layout"
     :page-size="pageInfo.pageSize"
     :total="pageInfo.total"
     @current-change="handleCurrentChange"
     @size-change="handleSizeChange"
   />
   <table-edit ref="edit" @fetch-data="fetchData" />
  </div>
</template>
<script>
  import TableEdit from './components/appVersionEdit'
  import {getAppVersionByPage,getAppVersion,saveAppVersion,delAppVersionById,delAppVersionByIds} from '@/api/system/syAppVersion-api'
  import tableMix from '@/views/mixins/table'
  export default {
    name: 'appVersion',
    components: {TableEdit},
    mixins: [tableMix],
    data() {
      return {
        formMinHeight:0,
        checkList: [
          '版本类型',
          '版本号',
          '地址',
          '发布人',
          '发布时间',
          '备注',
        ],
        columns: [
          {
            label: '版本类型',
            prop: 'type',
            width: '160'
          },
          {
            label: '版本号',
            prop: 'version',
            width: '160'
          },
          {
            label: '地址',
            prop: 'path',
          },
          {
            label: '发布人',
            prop: 'userName',
            width: '160'
          },
          {
            label: '发布时间',
            prop: 'createTime',
            width: '180'
          },
          {
            label: '备注',
            prop: 'remark',
            width: '160'
          },
        ]
      }
    },
    computed: {
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.fetchData()
    },
    methods: {
      handleAdd() {
        this.$refs['edit'].showEdit()
      },
      handleEdit(row) {
        this.$refs['edit'].showEdit(row)
      },

      async fetchData() {
        this.queryForm.curPage = this.pageInfo.curPage
        this.queryForm.pageSize = this.pageInfo.pageSize
        this.listLoading = true
        const { result: { records, total } } = await getAppVersionByPage(this.queryForm)
       this.list = records
       this.pageInfo.total = Number(total)
       this.listLoading = false
      },


      handleDelete(row) {
        if (row.id) {
          this.$baseConfirm('你确定要删除当前APP版本吗', null, async () => {
            delAppVersionById({id:row.id}).then(() => {
              this.pageInfo.curPage = 1
              this.fetchData()
              this.$baseMessage(
                '删除成功!',
                'success',
                'vab-hey-message-success'
              )
            })
            .catch(() => {
              this.$baseMessage('删除失败!', 'error', 'vab-hey-message-error')
            })
          })
        }
      },
    },
  }
</script>

<style lang="scss" scoped>

</style>
