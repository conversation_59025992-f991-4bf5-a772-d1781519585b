<template>
  <el-dialog
    v-drag
    append-to-body
    :title="title"
    :visible.sync="dialogFormVisible"
    width="750px"
    @close="close"
    :close-on-click-modal="false"
  >
    <el-form
        ref="dataForm"
        v-loading="formloading"
        :inline="true"
        :rules="rules"
        :model="formData"
        label-position="right"
        label-width="120px"
      >
        <el-form-item label="用户账号" prop="userAccount">
          <el-input v-model="formData.userAccount" placeholder="默认为手机号" style="width:200px;" disabled/>
        </el-form-item>
        <el-form-item v-if="formData.isAdd" label="密码" prop="userPwd">
          <el-input v-model="formData.userPwd" style="width:200px;" />
        </el-form-item>
        <el-form-item label="用户姓名" prop="userName">
          <el-input v-model="formData.userName" style="width:200px;" />
        </el-form-item>
        <el-form-item label="年龄" prop="age">
          <el-input v-model="formData.age" style="width:200px;" />
        </el-form-item>
        <el-form-item label="手机号" prop="telephone">
          <el-input v-model="formData.telephone" style="width:200px;" @blur="changeUserAccount"/>
        </el-form-item>
        <el-form-item label="性别" prop="gender">
          <el-radio-group @change="changeGender" v-model="formData.gender" size="small" style="width:200px;">
            <el-radio-button label="1">男</el-radio-button>
            <el-radio-button label="0">女</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="允许重复登录" prop="repeatLogin">
          <el-radio-group @change="changeRepeatLogin" v-model="formData.repeatLogin" size="small" style="width:200px;">
            <el-radio-button label="1">是</el-radio-button>
            <el-radio-button label="0">否</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="formData.email" style="width:200px;" />
        </el-form-item>
        <el-form-item label="角色" prop="roleNames">
          <el-input v-model="formData.roleNames" readonly @click.native="showRoleDialog" type="textarea" :autosize="{ minRows: 2, maxRows: 4}" style="width:500px;" />
        </el-form-item>
        <el-form-item label="所在部门" prop="departNames">
          <el-input v-model="formData.departNames" readonly @click.native="showDepartDialog" type="textarea" :autosize="{ minRows: 2, maxRows: 4}" style="width:500px;" />
        </el-form-item>
        <el-form-item label="是否启用" prop="state">
          <el-radio-group @change="changeState" v-model="formData.state" size="small" style="width:200px;">
            <el-radio-button label="1">是</el-radio-button>
            <el-radio-button label="0">否</el-radio-button>
          </el-radio-group>
        </el-form-item>
       <el-form-item label="有效时间" prop="effectiveTime">
         <el-date-picker
           v-model="formData.effectiveTime"
           format="yyyy-MM-dd"
           placeholder="选择时间"
           value-format="yyyy-MM-dd"
         />
       </el-form-item>
       <el-form-item label="排序" prop="sort">
         <el-input-number
           v-model.number="formData.sort"
           controls-position="right"
           :min="0"
           style="width: 150px"
         />
       </el-form-item>
      </el-form>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>
    <CommonDepartPopup ref="CommonDepartPopup" @callBackDepart="callBackDepart" />
    <CommonRolePopup ref="CommonRolePopup" @callBackRole="callBackRole"></CommonRolePopup>
  </el-dialog>
</template>

<script>
  import { checkUser, saveUser } from '@/api/system/user-api'
  import CommonDepartPopup from '../common/CommonDepartPopup'
  import CommonRolePopup from '../common/CommonRolePopup'
  import { genUUID } from '@/utils/th_utils.js'
  import { parseTime } from '@/utils/index'
  import { mapActions, mapGetters } from 'vuex'
  import { saveWorkbenchData } from '@/api/userWorkbench'
  export default {
    name: 'TableEdit',
    components: { CommonDepartPopup, CommonRolePopup },
    props:{},
    data() {
      const validateUserAccount = (rule, value, callback) => {
        if (!(this.formData.isAdd)) {
          callback()
          return
        }
        // 校验用户是否存在!
        checkUser({userAccount:value}).then(response => {
          callback()
        }).catch((err)=>{
          callback(new Error('用户账号已存在!'))
        })
      }
      const validateUserPhone = (rule, value, callback) => {
        const reg = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/
        if (value && !reg.test(value)) {
          callback(new Error('手机号格式输入有误！'))
          return
        }
        callback()
      }
      return {
        title: '',
        dialogFormVisible: false,
        formloading: false,
        formData: {},
        rules: {
          userAccount: [
            { required: true, message: '用户账号为必填', trigger: 'blur' },
            { required: true, validator: validateUserAccount, trigger: 'blur' }
          ],
          userName: [
            { required: true, message: '用户姓名为必填', trigger: 'blur' },
          ],
          departNames: [
            { required: true, message: '部门为必选', trigger: 'change' },
          ],
          roleNames: [
            { required: true, message: '角色为必选', trigger: 'change' },
          ],
          telephone: [
            { required: true, message: '请输入绑定手机号', trigger: 'blur' },
            { required: true, validator: validateUserPhone, trigger: 'blur' },
          ]
        },
      }
    },
    computed: {
      ...mapGetters({
        userId: 'user/userId',
        userName: 'user/userName',
      }),
    },
    created() {

    },
    methods: {
      changeUserAccount(){
        this.formData.userAccount = this.formData.telephone
      },
      showEdit(row) {
        if (!row) {
          this.title = '新增用户'
          this.initForm()
        } else {
          this.title = '编辑用户'
          this.formData = Object.assign({ isAdd: false,departNames:'',roleNames:'' }, row)
          this.formData.departNames = this.formData.departList.map(item=>item.departName).join(',')
          this.formData.roleNames = this.formData.roleList.map(item=>item.roleName).join(',')
        }
        this.dialogFormVisible = true
        this.$nextTick(()=>{
          this.$refs['dataForm'].clearValidate()
        })
      },
      // 初始化表单
      initForm() {
        this.formData = {
          id: genUUID(),
          sort: 1,
          userAccount: '',
          userPwd: '',
          userName: '',
          age:'',
          gender:'0',
          genderName:'女',
          telephone: '',
          email:'',
          roleList: [],
          roleNames:'',
          departList: [],
          departNames:'',
          repeatLogin: "1",
          repeatLoginName:'是',
          state:'1',
          stateName: "启用",
          effectiveTime:'',
          isAdd: true
        }
      },
      save() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.formData.userRoleRelList = []
            this.formData.userDepartRelList = []
            this.formData.roleList.forEach((item,index)=>{
              let obj={
                id: genUUID(),
                userId: this.formData.id,
                roleId: item.id,
                createBy: this.userId,
                createByName: this.userName,
                createTime: parseTime(new Date()),
                sort: index
              }
              this.formData.userRoleRelList.push(obj)
            })
            this.formData.departList.forEach((item,index)=>{
              let obj={
                id: genUUID(),
                userId: this.formData.id,
                departId: item.id,
                createBy: this.userId,
                createByName: this.userName,
                createTime: parseTime(new Date()),
                sort: index
              }
              this.formData.userDepartRelList.push(obj)
            })
            saveUser(this.formData).then(async response => {
              this.$baseMessage(
                this.formData.isAdd ? '新增用户成功！' : '修改用户成功!',
                'success', 'vab-hey-message-success'
              )
              if(this.formData.isAdd) {
                // await this.setDefaultApplicationMenu() // 设置默认的应用菜单
                this.$parent.setDefaultApplicationMenu(this.formData)
              }
              this.close()
              this.$emit('fetch-data')
            }).catch(err => {
              this.$baseMessage(
                this.formData.isAdd ? '新增用户失败！' : '修改用户失败!' ,
                'error', 'vab-hey-message-error'
              )
            })
          }
        })
      },
      close() {
        this.$refs['dataForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
      },
      showDepartDialog(){
        this.$refs.CommonDepartPopup.showDepartDialog(this.formData.departList.map(item=>item.id),true,false)//初始化勾选数据，是否复选，是否要根目录（-1）
      },
      callBackDepart(data){
        this.formData.departList = data
        this.formData.departNames = data.map(item=>item.label).join(',')
      },
      showRoleDialog(){
        this.$refs.CommonRolePopup.showRoleDialog(this.formData.roleList.map(item=>item.id),false)//初始化勾选数据，是否复选
      },
      callBackRole(data){
        this.formData.roleList = data
        this.formData.roleNames = data.map(item=>item.roleName).join(',')
      },
      changeGender(val){
        this.formData.genderName = this.formData.gender==='1'? '男' : '女'
        // console.log(this.formData.genderName)
      },
      changeRepeatLogin(val){
        this.formData.repeatLoginName = this.formData.repeatLogin==='1'? '是' : '否'
      },
      changeState(val){
        this.formData.stateName = this.formData.state==='1'? '启用' : '禁用'
      },
      async setDefaultApplicationMenu() {
        const defaultAppMenuIds = [
          '7b4f8686-68d4-4e43-9709-d60c373a4c6f', //项目报销
          'd85a023c-8190-4d2d-8e14-a57338d4b846', //机关报销
          '84ad37b4-2040-405d-8688-888215e16c6e', //项目出差
          '6b41774a-e30f-464d-8b37-985271cb3b59', //机关出差
          'acb385fe-ce31-44fe-8e61-a6a6677698e7', //项目采购
          '62f6cbe6-7e68-45b8-8381-ba27db3f5118', //机关采购
          '408e81af-a1d6-44f9-9a58-ea48caa2de64', //项目招待
          '20230280-30c7-4086-8601-fa2c763cedf2', //机关招待
          '26108cfd-d6af-4f86-8f96-cdcfd446273e', //项目开票
          'bf73b8a1-143c-4bbe-82e1-************', //机关开票
          '5d2c537b-b76a-48a8-8c2f-f200dbff0a2f', //用印申请
          '95407e88-e873-4fe1-838e-2b7a847d7158', //调休/请假
          '747914db-ad4a-4aaf-8daf-dfa0cb0068ea', //油卡充值
          '0f135da2-910b-4a0e-8eee-562050f3cd1b', //共享资料
        ]
        let paramsData = {
          id: genUUID(),
          groupName: '常用应用',
          status: '1',
          userWorkbenchMenus: [],
          userId: this.formData.id,
          sort: '0',
          remark: '',
          isAdd: true
        }
        paramsData.userWorkbenchMenus = defaultAppMenuIds.map((item, index) => {
          const menuItem = {
            id: genUUID(),
            bizId: paramsData.id,
            workbenchMenuId: item,
            sort: index,
            createBy: null,
            createTime: null,
            updateBy: null,
            updateTime: null
          }
          return menuItem
        })
        await saveWorkbenchData(paramsData)
      }
    },
  }
</script>

<style lang="scss" scoped>
  .dropTree{
    width: 100%;
    max-height:300px;
    overflow:auto;
  }
</style>
