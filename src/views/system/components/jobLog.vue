<template>
  <el-dialog
    v-drag
    append-to-body
    center
    :close-on-click-modal="false"
    title="任务执行日志"
    top="5vh"
    :visible.sync="dialogDetailVisible"
    width="1400px"
  >
    <el-form
      ref="form"
      :inline="true"
      label-width="140px"
      :model="queryForm"
      @submit.native.prevent
    >
      <el-form-item label="任务开始-结束时间" prop="queryDate">
        <el-date-picker
          v-model="queryForm.queryDate"
          align="right"
          end-placeholder="结束日期"
          range-separator="至"
          start-placeholder="开始日期"
          type="datetimerange"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <!--            <el-form-item label="任务结束时间" prop="endTime" >
             <el-date-picker
               v-model="queryForm.endTime"
               type="date"
               style="width: 200px;"
                value-format="yyyy-MM-dd"
               placeholder="请选择结束时间">
             </el-date-picker>
            </el-form-item> -->

      <el-form-item>
        <el-button
          icon="el-icon-search"
          native-type="submit"
          type="primary"
          @click="handleQuery"
        >
          查询
        </el-button>
        <el-button
          icon="el-icon-refresh-right"
          @click.native="resetForm('form')"
        >
          重置
        </el-button>
      </el-form-item>
    </el-form>
    <el-table
      ref="tableSort"
      v-loading="listLoading"
      border
      :data="list"
      max-height="300px"
      :size="lineHeight"
      stripe
    >
      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        :align="item.center ? item.center : 'center'"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'"
      >
        <template #default="{ row }">
          <span v-if="item.label === '是否成功'">
            <el-tag v-if="row.isSuccess === '1'" type="primary">是</el-tag>
            <el-tag v-else type="warning">否</el-tag>
          </span>
          <span v-else>{{ row[item.prop] }}</span>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-size="pageInfo.pageSize"
      :page-sizes="[5, 10, 15]"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <!-- 附件上传 -->
  </el-dialog>
</template>

<script>
  import { getDataListByPageLog } from '@/api/system/quartzJob-api.js'
  import tableMix from '@/views/mixins/table'

  export default {
    name: 'JobLog',
    components: {},
    mixins: [tableMix],
    props: {},
    data() {
      return {
        dialogDetailVisible: false,
        formData: {},
        checkList: [
          '任务名称',
          '执行Bean',
          '方法',
          '周期表达式',
          '参数',
          '耗时(ms)',
          '异常信息',
          '是否成功',
          '执行时间',
          '执行主机',
        ],
        columns: [
          {
            label: '任务名称',
            prop: 'jobName',
          },
          {
            label: '执行Bean',
            prop: 'jobBean',
          },
          {
            label: '方法',
            prop: 'methodName',
          },
          {
            label: '周期表达式',
            prop: 'jobCron',
          },
          {
            label: '参数',
            prop: 'jobParam',
          },
          {
            label: '耗时(ms)',
            prop: 'execTime',
          },
          {
            label: '异常信息',
            prop: 'exceptionDetail',
          },
          {
            label: '是否成功',
            prop: 'isSuccess',
          },
          {
            label: '执行时间',
            prop: 'createTime',
          },
          {
            label: '执行主机',
            prop: 'execIp',
          },
        ],

        queryForm: {
          queryDate: [],
          curPage: '',
          pageSize: '',
          jobId: '',
          startTime: '',
          endTime: '',
        },
      }
    },
    computed: {
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {},

    methods: {
      async showDialog(row) {
        this.formData = { ...row }
        await this.fetchData()
        this.dialogDetailVisible = true
      },
      async fetchData() {
        const paramsData = {
          curPage: this.pageInfo.curPage,
          pageSize: this.pageInfo.pageSize,
          jobId: this.formData.id,
          startTime: this.formData.startTime,
          endTime: this.formData.endTime,
        }
        if (this.queryForm.queryDate.length > 0) {
          paramsData.startTime = this.queryForm.queryDate[0]
          paramsData.endTime = this.queryForm.queryDate[1]
        }
        this.listLoading = true
        const {
          result: { records, total },
        } = await getDataListByPageLog(paramsData)
        this.list = records
        this.pageInfo.total = Number(total)
        this.listLoading = false
      },
    },
  }
</script>

<style></style>
