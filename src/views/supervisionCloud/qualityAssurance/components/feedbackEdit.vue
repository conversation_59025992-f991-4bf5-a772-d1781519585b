<template>
  <el-dialog
    v-drag
    append-to-body
    :before-close="closeBtn"
    center
    :close-on-click-modal="false"
    :title="pageTitle"
    top="5vh"
    :visible.sync="dialogFormVisible"
    width="1400px"
  >
    <el-form
      ref="dataForm"
      v-loading="formLoading"
      :inline="true"
      label-position="right"
      label-width="130px"
      :model="formData"
      :rules="rules"
    >
      <table class="form-table">
        <tr>
          <td>
            <el-form-item label="编号" prop="serialNumber">
              <el-input
                v-model="formData.serialNumber"
                disabled
                placeholder="自动生成"
                style="width: 250px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="所属项目" prop="projectCode">
              <el-input
                v-model="formData.projectName"
                readonly
                style="width: 250px"
              />
            </el-form-item>
          </td>
          <td>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="状态" prop="status">
              <el-input v-model="formData.statusName" :disabled="true" />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="巡查类别" prop="inspectionType">
              <el-input  v-model="formData.inspectionType" :disabled="true"  />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="巡查时间" prop="inspectionTime">
              <el-input v-model="formData.inspectionTime" :disabled="true" />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="隐患等级" prop="hiddenDangerLevel">
              <el-input v-model="formData.hiddenDangerLevelName" :disabled="true" />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="隐患后果" prop="hiddenDangerConsequence">
              <el-input v-model="formData.hiddenDangerConsequenceName" :disabled="true" />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="作业地点" prop="operationLocation">
              <el-input v-model="formData.operationLocation" :disabled="true" />
            </el-form-item>
          </td>
          <td></td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label="问题概述" prop="constructionContent">
              <el-input
                v-model="formData.problemOverview"
                maxlength="1000"
                placeholder="请输入内容(不超过1000字)"
                rows="3"
                style="width: 1150px"
                type="textarea"
                :disabled="true"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label="整改反馈意见" prop="feedback">
              <el-input
                v-model="formData.feedback"
                maxlength="1000"
                placeholder="请输入内容(不超过1000字)"
                rows="3"
                style="width: 1150px"
                type="textarea"
              />
            </el-form-item>
          </td>
        </tr>
      </table>
    </el-form>
    <div slot="footer" class="dialog-footer" style="text-align: center">
      <el-button type="danger" @click="closeBtn">关闭</el-button>
      <el-button type="primary" @click="saveBtn">保存</el-button>
    </div>
    <UploadLargeFileFdfsPopup ref="UploadLargeFileFdfsPopup" />
  </el-dialog>
</template>

<script>
  import { mapGetters } from 'vuex'
  import { parseTime, genUUID } from '@/utils/th_utils'
  import { saveData } from '@/api/supervisionCloud/qualityAssurance/safetyInspectionFeedback-api'
  import UploadLargeFileFdfsPopup from '@/views/common/UploadLargeFileFdfsPopup.vue'
  import {getDictList} from "@/api/system/dict-api";
  export default {
    computed: {
      ...mapGetters({
        departList: 'user/departList',
        userName: 'user/userName',
        userId: 'user/userId',
        // 所有的部门列表数据
        allDepartList: 'acl/allDepartList',
      }),
      pageTitle() {
        return this.formData.isAdd ? '新增' : '编辑'
      },
    },
    components: {
      UploadLargeFileFdfsPopup,
    },
    props: {
      departTreeData: {
        type: Array,
        default() {
          return []
        },
      },
    },
    data() {
      return {
        dialogFormVisible: false,
        formLoading: false,
        defaultProps: {
          children: 'children',
          label: 'label',
        },
        selectedStatus: [
          { label: '未生效', value: '0' },
          { label: '已生效', value: '1' },
          { label: '已反馈', value: '2', disabled: true },
          { label: '已结束', value: '3', disabled: true }
        ],
        selectHiddenDangerLevelList: [],
        selectHiddenDangerConsequenceList: [],
        formData: {
          id: '',
          bidSectionId: '',
          serialNumber: '',
          projectId: '',
          status: '',
          inspectionType: '安全巡查',
          inspectionTime: '',
          engineeringCategory: '',
          engineeringName: '',
          feedback: '',
          feedbackTime: '',
          feedbackBy: '',
          examine: '',
          examineTime: '',
          examineBy: '',
          hiddenDangerLevel: '',
          hiddenDangerConsequence: '',
          operationLocation: '',
          problemOverview:'',
          sort: '',
        },
        rules: {
          projectCode: [
            { required: true, message: '请选择项目', trigger: 'change' },
          ],
        },
      }
    },
    mounted() {
    },
    methods: {

      async showDialog(row) {
        if (row.isAdd) {
          const bizId = row.id
          delete row.id
          this.formData = {bizId:bizId,feedbackBy:this.userId,feedbackTime:new Date(),statusName:this.formatStatus(row.status),...row,id: genUUID(),isAdd:true}
        } else {
          this.formData = {statusName:this.formatStatus(row.status), ...row }
        }
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs.UploadLargeFileFdfsPopup.getParamsData({
            bizId: this.formData.id,
            bizCode: this.$route.name,
          })
        })
      },
      closeBtn() {
        if (this.formData.isAdd) {
          this.$refs.UploadLargeFileFdfsPopup.closeDelImg()
        }
        this.formLoading = false
        this.dialogFormVisible = false
        this.$refs.dataForm.resetFields()
      },
      saveBtn() {
        this.$refs.dataForm.validate((valid) => {
          if (valid) {
            this.formLoading = true
            const formData = {
              ...this.formData,
            }
            saveData(formData).then((res) => {
              if (res.code === 200) {
                this.$baseMessage(
                  '保存成功!',
                  'success',
                  'vab-hey-message-success'
                )
                this.$emit('refreshPage')
                this.formLoading = false
                this.dialogFormVisible = false
              }
            })
          }
        })
      },
      formatStatus(status) {
        return status === '0' ? '未生效' : status === '1' ? '已生效' : status === '2' ? '已反馈' : status === '3' ? '已结束' : '已结束'
      },
    },
  }
</script>

<style lang="scss" scoped>
  .image-upload {
    position: absolute;
    right: 21px;
    bottom: 93px;
    width: 536px;
    height: 160px;
    background: #fff;
    border-left: 1px solid #bbb;
  }
</style>
