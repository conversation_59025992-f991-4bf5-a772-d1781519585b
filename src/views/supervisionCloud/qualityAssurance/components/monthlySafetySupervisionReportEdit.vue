<template>
  <el-dialog
    v-drag
    append-to-body
    :before-close="closeBtn"
    center
    :close-on-click-modal="false"
    :title="pageTitle"
    top="5vh"
    :visible.sync="dialogFormVisible"
    width="1400px"
  >
    <el-form
      ref="dataForm"
      v-loading="formLoading"
      :inline="true"
      label-position="right"
      label-width="130px"
      :model="formData"
      :rules="rules"
    >
      <table class="form-table">
        <tr>
          <td>
            <el-form-item label="编号" prop="serialNumber">
              <el-input
                v-model="formData.serialNumber"
                disabled
                placeholder="自动生成"
                style="width: 250px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="所属项目" prop="projectCode">
              <el-input
                v-model="formData.projectCode"
                :disabled="!formData.isAdd"
                placeholder="选择所属项目"
                readonly
                style="width: 250px"
                @click.native="selectProject"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="项目名称" prop="projectName">
              <el-input
                v-model="formData.projectName"
                disabled
                placeholder="自动带出"
                style="width: 250px"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="施工标段" prop="constructionCode">
              <el-input
                v-model="formData.constructionCode"
                disabled
                placeholder="自动带出"
                style="width: 250px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="施工单位" prop="constructionUnitIds">
              <el-select
                v-model="formData.constructionUnitIds"
                :disabled="!formData.isAdd"
                placeholder="请选择施工单位"
                style="width: 250px"
              >
                <el-option
                  v-for="item in constructionUnitList"
                  :key="item.id"
                  :label="item.constructionUnitNames"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </td>
          <td>
            <el-form-item label="日期" prop="time">
              <el-date-picker
                v-model="formData.time"
                format="yyyy-MM-dd"
                placeholder="选择日期"
                style="width: 250px"
                type="date"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="汇总人" prop="recorderName">
              <el-input
                v-model="formData.recorderName"
                placeholder="选择汇总人"
                readonly
                style="width: 250px"
                @click.native="showRecorderListInfo"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="状态" prop="status">
              <el-select
                v-model="formData.status"
                placeholder="请选择状态"
                style="width: 250px"
              >
                <el-option label="未生效" value="0" />
                <el-option label="已生效" value="1" />
              </el-select>
            </el-form-item>
          </td>
          <td>
            <el-form-item label="创建人" prop="createByName">
              <el-input v-model="formData.createByName" disabled style="width: 250px" />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label="施工安保体系运行及安全责任制落实情况、安全生产费用使用情况" prop="firstContent">
              <el-input
                v-model="formData.firstContent"
                :autosize="{ minRows: 3, maxRows: 6 }"
                placeholder="请输入内容"
                style="width: 800px"
                type="textarea"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label="三类人员到岗情况及特种作业人员持证情况" prop="secondContent">
              <el-input
                v-model="formData.secondContent"
                :autosize="{ minRows: 3, maxRows: 6 }"
                placeholder="请输入内容"
                style="width: 800px"
                type="textarea"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label="施工设备进场验收、登记及定期检查维修保养情况" prop="thirdContent">
              <el-input
                v-model="formData.thirdContent"
                :autosize="{ minRows: 3, maxRows: 6 }"
                placeholder="请输入内容"
                style="width: 800px"
                type="textarea"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label="安全检查情况" prop="fourthContent">
              <el-input
                v-model="formData.fourthContent"
                :autosize="{ minRows: 3, maxRows: 6 }"
                placeholder="请输入内容"
                style="width: 800px"
                type="textarea"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label="安全教育培训情况" prop="fifthContent">
              <el-input
                v-model="formData.fifthContent"
                :autosize="{ minRows: 3, maxRows: 6 }"
                placeholder="请输入内容"
                style="width: 800px"
                type="textarea"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label="安全事故情况" prop="sixthContent">
              <el-input
                v-model="formData.sixthContent"
                :autosize="{ minRows: 3, maxRows: 6 }"
                placeholder="请输入内容"
                style="width: 800px"
                type="textarea"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label="安全监理工作情况" prop="seventhContent">
              <el-input
                v-model="formData.seventhContent"
                :autosize="{ minRows: 3, maxRows: 6 }"
                placeholder="请输入内容"
                style="width: 800px"
                type="textarea"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label="安全监理工作评价" prop="eighthContent">
              <el-input
                v-model="formData.eighthContent"
                :autosize="{ minRows: 3, maxRows: 6 }"
                placeholder="请输入内容"
                style="width: 800px"
                type="textarea"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label="下月安全监理工作计划" prop="ninthContent">
              <el-input
                v-model="formData.ninthContent"
                :autosize="{ minRows: 3, maxRows: 6 }"
                placeholder="请输入内容"
                style="width: 800px"
                type="textarea"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label="其它有关事项" prop="otherContent">
              <el-input
                v-model="formData.otherContent"
                :autosize="{ minRows: 3, maxRows: 6 }"
                placeholder="请输入内容"
                style="width: 800px"
                type="textarea"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="formData.remark"
                :autosize="{ minRows: 3, maxRows: 6 }"
                placeholder="请输入备注"
                style="width: 800px"
                type="textarea"
              />
            </el-form-item>
          </td>
        </tr>
      </table>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeBtn">取消</el-button>
      <el-button type="primary" @click="saveBtn">确定</el-button>
    </div>
    <ProjectListPopup
      ref="projectListPopupRef"
      @getProjectInfo="getProjectInfo"
    />
    <UploadLargeFileFdfsPopup ref="UploadLargeFileFdfsPopup" />
    <ProjectUserListPopup
      ref="ProjectUserListPopup"
      @getProjectUserInfo="getProjectUserInfo"
    />
  </el-dialog>
</template>

<script>
  import { mapGetters } from 'vuex'
  import { parseTime, genUUID } from '@/utils/th_utils'
  import { saveData } from '@/api/supervisionCloud/qualityAssurance/monthlySafetySupervisionReport-api'
  import UploadLargeFileFdfsPopup from '@/views/common/UploadLargeFileFdfsPopup.vue'
  import ProjectUserListPopup from '@/views/common/ProjectUserListPopup.vue'
  import ProjectListPopup from '@/views/common/ProjectListPopup.vue'
  export default {
    computed: {
      ...mapGetters({
        departList: 'user/departList',
        userName: 'user/userName',
        userId: 'user/userId',
        // 所有的部门列表数据
        allDepartList: 'acl/allDepartList',
      }),
      pageTitle() {
        return this.formData.isAdd ? '新增安全监理月报' : '编辑安全监理月报'
      },
    },
    components: {
      ProjectListPopup,
      ProjectUserListPopup,
      UploadLargeFileFdfsPopup,
    },
    props: {
      departTreeData: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        dialogFormVisible: false,
        formLoading: false,
        defaultProps: {
          children: 'children',
          label: 'label',
        },
        constructionUnitList: [],
        formData: {
          id: '',
          serialNumber: '',
          createDepart: '',
          createDepartName: '',
          time: '',
          depart: '',
          departName: '',
          departCode: '',
          createBy: '',
          createByName: '',
          viewUser: '',
          operationCode: '',
          serialNumberTable: '',
          projectId: '',
          projectCode: '',
          projectName: '',
          constructionUnitIds: '',
          constructionUnitNames: '',
          constructionCode: '',
          recorder: '',
          recorderName: '',
          status: '',
          firstContent: '',
          secondContent: '',
          thirdContent: '',
          fourthContent: '',
          fifthContent: '',
          sixthContent: '',
          seventhContent: '',
          eighthContent: '',
          ninthContent: '',
          otherContent: '',
          remark: '',
          sort: '',
        },
        rules: {
          projectCode: [
            { required: true, message: '请选择项目', trigger: 'change' },
          ],
          recorderName: [
            { required: true, message: '汇总人为必填', trigger: 'change' },
          ],
          time: [{ required: true, message: '日期为必填', trigger: 'blur' }],
          status: [{ required: true, message: '状态为必填', trigger: 'change' }],
        },
      }
    },
    methods: {
      async showDialog(row) {
        if (row.isAdd) {
          this.init()
        } else {
          this.formData = { ...row }
          // 获取施工单位列表
          this.getConstructionUnitList()
        }
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs.UploadLargeFileFdfsPopup.getParamsData({
            bizId: this.formData.id,
            bizCode: this.$route.name,
          })
        })
      },
      init() {
        this.formData = {
          id: genUUID(),
          serialNumber: '',
          createDepart: this.departList[0].id,
          createDepartName: this.departList[0].departName,
          time: parseTime(new Date()),
          depart: '',
          departName: '',
          departCode: '',
          createBy: this.userId,
          createByName: this.userName,
          viewUser: this.userId,
          operationCode: 'AQJLYB',
          serialNumberTable: 'monthly_safety_supervision_report',
          projectId: '',
          projectCode: '',
          projectName: '',
          constructionUnitIds: '',
          constructionUnitNames: '',
          constructionCode: '',
          recorder: '',
          recorderName: '',
          status: '0',
          firstContent: '',
          secondContent: '',
          thirdContent: '',
          fourthContent: '',
          fifthContent: '',
          sixthContent: '',
          seventhContent: '',
          eighthContent: '',
          ninthContent: '',
          otherContent: '',
          remark: '',
          sort: '',
          isAdd: true,
        }
      },
      closeBtn() {
        if (this.formData.isAdd) {
          this.$refs.UploadLargeFileFdfsPopup.closeDelImg()
        }
        this.formLoading = false
        this.dialogFormVisible = false
        this.$refs.dataForm.resetFields()
        this.init()
      },
      saveBtn() {
        this.$refs.dataForm.validate((valid) => {
          if (valid) {
            this.formLoading = true
            const formData = {
              ...this.formData,
            }
            saveData(formData).then((res) => {
              if (res.code === 200) {
                this.$baseMessage(
                  '保存成功!',
                  'success',
                  'vab-hey-message-success'
                )
                this.$emit('refreshPage')
                this.formLoading = false
                this.dialogFormVisible = false
              }
            })
          }
        })
      },
      selectProject() {
        this.$refs.projectListPopupRef.showDialog({
          selectIds: this.formData.projectId,
          projectName: this.formData.projectName,
        })
      },
      getProjectInfo(data) {
        if (data.length > 0 && data[0].id !== this.formData.projectId) {
          const dataObj = data[0]
          const { id, projectName, serialNumber } = dataObj
          this.formData = {
            ...this.formData,
            projectId: id,
            projectCode: serialNumber,
            projectName,
          }
          this.$refs.dataForm.clearValidate('projectCode')
        }
      },
      showRecorderListInfo() {
        let selectRows = []
        if (this.formData.recorder) {
          selectRows.push({
            id: this.formData.recorder,
            userName: this.formData.recorderName,
          })
        }
        let queryMap = {
          queryMap: {
            projectId: this.formData.projectId,
          },
          selectIds: this.formData.recorder,
          openAllSelectable: true,
          selectRows,
        }
        this.$refs.ProjectUserListPopup.showDialog(queryMap)
      },
      getProjectUserInfo(data) {
        if (data && data.length > 0) {
          this.formData = {
            ...this.formData,
            recorder: data[0].userId,
            recorderName: data[0].userName,
          }
        } else {
          this.formData = {
            ...this.formData,
            recorder: '',
            recorderName: '',
          }
        }
      },

      getConstructionUnitList() {
        // 这里应该调用获取施工单位列表的接口
        // 由于没有具体接口，这里模拟一些数据
        this.constructionUnitList = [
          {
            id: '1',
            constructionUnitNames: '施工单位1',
          },
          {
            id: '2',
            constructionUnitNames: '施工单位2',
          },
          {
            id: '3',
            constructionUnitNames: '施工单位3',
          },
        ]
      },
    },
  }
</script>

<style lang="scss" scoped>
  .form-table {
    width: 100%;
    border-collapse: collapse;

    td {
      padding: 5px;
      vertical-align: top;
    }
  }
</style>
