<template>
  <el-dialog
    v-drag
    append-to-body
    center
    :close-on-click-modal="false"
    title="安全监理月报详情"
    top="5vh"
    :visible.sync="dialogDetailVisible"
    width="1680px"
  >
    <div style="display: flex; max-height: 75vh; overflow-y: scroll">
      <div style="flex: 1">
        <el-descriptions
          border
          class="margin-top"
          :column="3"
          :label-style="labelStyle"
          size="medium"
        >
          <el-descriptions-item>
            <template slot="label">编号</template>
            {{ formData.serialNumber }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">所属项目</template>
            {{ formData.projectCode }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">项目名称</template>
            {{ formData.projectName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">监理标段号</template>
            {{ formData.bidNo }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">施工标段</template>
            {{ formData.constructionCode }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">施工单位</template>
            {{ formData.constructionUnitNames }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">日期</template>
            {{ formData.time }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">汇总人</template>
            {{ formData.recorderName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">创建人</template>
            {{ formData.createByName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">创建部门</template>
            {{ formData.createDepartName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">创建日期</template>
            {{ formData.createTime | dateformat('YYYY-MM-DD HH:mm:ss') }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">状态</template>
            <span>
              <el-tag v-if="formData.status === '0'" type="danger">
                未生效
              </el-tag>
              <el-tag v-else-if="formData.status === '1'" type="success">
                已生效
              </el-tag>
            </span>
          </el-descriptions-item>
          <el-descriptions-item :span="3">
            <template slot="label">施工安保体系运行及安全责任制落实情况、安全生产费用使用情况</template>
            {{ formData.firstContent }}
          </el-descriptions-item>
          <el-descriptions-item :span="3">
            <template slot="label">三类人员到岗情况及特种作业人员持证情况</template>
            {{ formData.secondContent }}
          </el-descriptions-item>
          <el-descriptions-item :span="3">
            <template slot="label">施工设备进场验收、登记及定期检查维修保养情况</template>
            {{ formData.thirdContent }}
          </el-descriptions-item>
          <el-descriptions-item :span="3">
            <template slot="label">安全检查情况</template>
            {{ formData.fourthContent }}
          </el-descriptions-item>
          <el-descriptions-item :span="3">
            <template slot="label">安全教育培训情况</template>
            {{ formData.fifthContent }}
          </el-descriptions-item>
          <el-descriptions-item :span="3">
            <template slot="label">安全事故情况</template>
            {{ formData.sixthContent }}
          </el-descriptions-item>
          <el-descriptions-item :span="3">
            <template slot="label">安全监理工作情况</template>
            {{ formData.seventhContent }}
          </el-descriptions-item>
          <el-descriptions-item :span="3">
            <template slot="label">安全监理工作评价</template>
            {{ formData.eighthContent }}
          </el-descriptions-item>
          <el-descriptions-item :span="3">
            <template slot="label">下月安全监理工作计划</template>
            {{ formData.ninthContent }}
          </el-descriptions-item>
          <el-descriptions-item :span="3">
            <template slot="label">其它有关事项</template>
            {{ formData.otherContent }}
          </el-descriptions-item>
          <el-descriptions-item :span="3">
            <template slot="label">备注</template>
            {{ formData.remark }}
          </el-descriptions-item>
        </el-descriptions>
        <!-- 附件上传 -->
        <UploadLargeFileFdfsPopupDetail
          ref="UploadLargeFileFdfsPopupDetail"
          style="margin-top: 20px"
        />
      </div>
    </div>
    <div slot="footer" v-if="!isApproval" class="dialog-footer">
      <el-button type="danger" @click="dialogDetailVisible = false">
        关闭
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import UploadLargeFileFdfsPopupDetail from '@/views/common/UploadLargeFileFdfsPopupDetail.vue'
  export default {
    components: {
      UploadLargeFileFdfsPopupDetail,
    },
    data() {
      return {
        dialogDetailVisible: false,
        formData: {},
        isApproval: false,
        labelStyle: {
          width: '130px',
        },
      }
    },
    methods: {
      callBackRefresh() {
        this.$parent.fetchData()
        this.dialogDetailVisible = false
      },
      showDialog(row) {
        this.isApproval = false
        this.formData = { ...row }
        this.$nextTick(() => {
          this.$refs.UploadLargeFileFdfsPopupDetail.getParamsData({
            bizId: this.formData.id,
            bizCode: this.$route.name,
            isShow: true,
          })
        })
        this.dialogDetailVisible = true
      },
    },
  }
</script>
