<template>
  <el-dialog
    v-drag
    append-to-body
    :before-close="closeBtn"
    center
    :close-on-click-modal="false"
    :title="pageTitle"
    top="5vh"
    :visible.sync="dialogFormVisible"
    width="1050px"
  >
    <!-- 表单标题 -->
    <div class="form-header">
      <h2 class="project-title">{{ formData.projectName }}</h2>
      <h3 class="form-title">监理日志</h3>
    </div>

    <el-form
      ref="dataForm"
      v-loading="formLoading"
      :inline="true"
      label-position="left"
      label-width="120px"
      :model="formData"
      :rules="rules"
    >
      <table class="form-table">
        <!-- 第一行：编号 -->
        <tr>
          <td colspan="3">
            <el-form-item label="编号" prop="serialNumber">
              <el-input
                v-model="formData.serialNumber"
                disabled
                placeholder="自动生成"
              />
            </el-form-item>
          </td>
        </tr>
        <!-- 第二行：监理机构 -->
        <tr>
          <td colspan="3">
          <el-form-item label="监理机构" prop="directorName">
            <el-input
              v-model="formData.directorName"
              :disabled="true"
            />
          </el-form-item>
          </td>
        </tr>

        <!-- 第三行：记录人和日期 -->
        <tr>
          <td>
            <el-form-item label="记录人" prop="recorder">
              <t-form-user
                v-model="formData.recorder"
                placeholder="请选择记录人"
              />

            </el-form-item>
          </td>
          <td>
            <el-form-item label="日期" prop="time">
              <el-date-picker
                v-model="formData.time"
                type="date"
                placeholder="请选择日期"
              ></el-date-picker>
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="审核人" prop="reviewer">
              <t-form-user
                v-model="formData.reviewer"
                placeholder="请选择审核人"
              />
            </el-form-item>

          </td>
          <td>
            <el-form-item label="天气情况" prop="weather">
              <el-input
                v-model="formData.weather"
                placeholder="请输入天气情况"
              />
            </el-form-item>
          </td>
        </tr>

        <tr>
          <td colspan="2">
            <el-form-item label="主要施工情况" prop="constructionContent">
              <el-input
                v-model="formData.constructionContent"
                maxlength="1000"
                placeholder="请输入主要施工情况(不超过1000字)"
                rows="3"
                style="width: 785px"
                type="textarea"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="2">
            <el-form-item label="监理主要工作" prop="superviseContent">
              <el-input
                v-model="formData.superviseContent"
                maxlength="1000"
                placeholder="请输入监理主要工作(不超过1000字)"
                rows="3"
                style="width: 785px"
                type="textarea"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="2">
            <el-form-item label="问题及处理情况" prop="otherContent">
              <el-input
                v-model="formData.otherContent"
                maxlength="1000"
                placeholder="请输入问题及处理情况(不超过1000字)"
                rows="3"
                style="width: 785px"
                type="textarea"
              />
            </el-form-item>
          </td>
        </tr>
      </table>
    </el-form>
    <div slot="footer" class="dialog-footer" style="text-align: center">
      <el-button type="danger" @click="closeBtn">关闭</el-button>
      <el-button type="primary" @click="saveBtn">保存</el-button>
    </div>
    <UploadInspectionFilePopup ref="UploadInspectionFilePopup" />
  </el-dialog>
</template>

<script>
  import { mapGetters } from 'vuex'
  import { parseTime, genUUID } from '@/utils/th_utils'
  import { saveData } from '@/api/supervisionCloud/jobLog/superviseLog-api'
  import UploadInspectionFilePopup from  '@/views/supervisionCloud/jobLog/inspectionRecord/components/UploadInspectionFilePopup.vue'
  export default {
    computed: {
      ...mapGetters({
        departList: 'user/departList',
        userName: 'user/userName',
        userId: 'user/userId',
      }),
      pageTitle() {
        return this.formData.isAdd ? '新增' : '编辑'
      },
    },
    components: {
      UploadInspectionFilePopup,
    },
    props: {
    },
    data() {
      return {
        dialogFormVisible: false,
        formLoading: false,
        defaultProps: {
          children: 'children',
          label: 'label',
        },
        formData: {
          id: '',
          serialNumber: '',
          createDepart: '',
          createDepartName: '',
          time: '',
          depart: '',
          departName: '',
          departCode: '',
          createBy: '',
          createByName: '',
          viewUser: '',
          operationCode: '',
          serialNumberTable: '',
          projectId: '',
          constructionUnitIds: '',
          constructionUnitNames: '',
          constructionCode: '',
          constructionContent: '',
          superviseContent: '',
          otherContent: '',
          recorder: this.userId,
          directorName: '',
          reviewer:'',
          weather: '',
          temperature: '',
          status: '',
          sort: '',
        },
        rules: {
          recorder: [
            { required: true, message: '请选择记录人', trigger: 'change' },
          ],
          time: [{ required: true, message: '日期为必填', trigger: 'blur' }],
        },
      }
    },
    methods: {
      async showDialog(row) {
        console.log(row, '=============')
        if (row.isAdd) {
          this.init(row)
        } else {
          this.formData = { ...row }
        }
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs.UploadInspectionFilePopup.getParamsData({
            bizId: this.formData.id,
            bizCode: this.$route.name,
          })
        })
      },
      init(row) {
        this.formData = {
          id: genUUID(),
          serialNumber: '',
          createDepart: this.departList[0].id,
          createDepartName: this.departList[0].departName,
          time: parseTime(new Date()),
          depart: '',
          departName: '',
          departCode: '',
          createBy: this.userId,
          createByName: this.userName,
          viewUser: this.userId,
          operationCode: 'JLRZ-'+row.bidNo,
          serialNumberTable: 'supervise_log',
          projectId: row.projectId,
          projectName: row.projectName,
          construction_unit_id: '',
          construction_content: '',
          supervise_content: '',
          other_content: '',
          recorder: this.userId,
          directorName:row.directorName,
          reviewer:'',
          weather: '',
          status: '0',
          sort: '',
          isAdd: true,
        }
      },
      closeBtn() {
        if (this.formData.isAdd) {
          this.$refs.UploadInspectionFilePopup.closeDelImg()
        }
        this.formLoading = false
        this.dialogFormVisible = false
        this.$refs.dataForm.resetFields()
      },
      saveBtn() {
        this.$refs.dataForm.validate((valid) => {
          if (valid) {
            this.formLoading = true
            const formData = {
              ...this.formData,
            }
            saveData(formData).then((res) => {
              if (res.code === 200) {
                this.$baseMessage(
                  '保存成功!',
                  'success',
                  'vab-hey-message-success'
                )
                this.$emit('refreshPage')
                this.formLoading = false
                this.dialogFormVisible = false
              }
            })
          }
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
// 表单头部样式
.form-header {
  text-align: center;
  margin-bottom: 10px;
  border-bottom: 1px solid #e4e7ed;

  .project-title {
    font-size: 22px;
    font-weight: 600;
    color: #303133;
    margin: 0 0 8px 0;
  }

  .form-title {
    font-size: 16px;
    font-weight: 500;
    color: #606266;
    margin: 0;
  }
}


.image-upload {
  position: absolute;
  right: 21px;
  bottom: 93px;
  width: 536px;
  height: 160px;
  background: #fff;
  border-left: 1px solid #bbb;
}
</style>
