<template>
  <el-dialog
    v-drag
    append-to-body
    center
    :close-on-click-modal="false"
    title="交工验收报告详情"
    top="5vh"
    :visible.sync="dialogDetailVisible"
    width="1680px"
  >
    <div style="display: flex; max-height: 75vh; overflow-y: scroll">
      <div style="flex: 1">
        <el-descriptions
          border
          class="margin-top"
          :column="3"
          :label-style="labelStyle"
          size="medium"
        >
          <el-descriptions-item>
            <template slot="label">编号</template>
            {{ formData.serialNumber }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">所属项目</template>
            {{ formData.projectCode }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">项目名称</template>
            {{ formData.projectName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">监理标段号</template>
            {{ formData.bidNo }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">施工单位</template>
            {{ formData.constructionUnitNames }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">施工标段号</template>
            {{ formData.constructionCode }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">施工单位负责人</template>
            {{ formData.constructionUnitLeader }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">建设单位</template>
            {{ formData.buildName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">监理单位负责人</template>
            {{ formData.supervisionUnitLeader }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">交工验收日期</template>
            {{ formData.subDate | dateformat('YYYY-MM-DD') }}
          </el-descriptions-item>

          <el-descriptions-item>
            <template slot="label">设计单位</template>
            {{ formData.designUnit }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">设计单位负责人</template>
            {{ formData.designUnitLeader }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">创建人</template>
            {{ formData.createByName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">创建日期</template>
            {{ formData.createTime | dateformat('YYYY-MM-DD HH:mm:ss') }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">状态</template>
            <span>
              <el-tag v-if="formData.status === '0'" type="danger">
                未生效
              </el-tag>
              <el-tag v-else-if="formData.status === '1'" type="success">
                已生效
              </el-tag>
            </span>
          </el-descriptions-item>
          <el-descriptions-item :span="3">
            <template slot="label">交工验收结果</template>
            {{ formData.completionAcceptanceResults }}
          </el-descriptions-item>
        </el-descriptions>
        <!-- 附件上传 -->
        <UploadLargeFileFdfsPopupDetail
          ref="UploadLargeFileFdfsPopupDetail"
          style="margin-top: 20px"
        />
      </div>
    </div>
    <div slot="footer" v-if="!isApproval" class="dialog-footer">
      <el-button type="danger" @click="dialogDetailVisible = false">
        关闭
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import UploadLargeFileFdfsPopupDetail from '@/views/common/UploadLargeFileFdfsPopupDetail.vue'
  export default {
    components: {
      UploadLargeFileFdfsPopupDetail,
    },
    data() {
      return {
        dialogDetailVisible: false,
        formData: {},
        isApproval: false,
        labelStyle: {
          width: '130px',
        },
      }
    },
    methods: {
      callBackRefresh() {
        this.$parent.fetchData()
        this.dialogDetailVisible = false
      },
      showDialog(row) {
        this.isApproval = false
        this.formData = { ...row }
        this.$nextTick(() => {
          this.$refs.UploadLargeFileFdfsPopupDetail.getParamsData({
            bizId: this.formData.id,
            bizCode: this.$route.name,
            isShow: true,
          })
        })
        this.dialogDetailVisible = true
      },
    },
  }
</script>

<style lang="scss" scoped></style>
