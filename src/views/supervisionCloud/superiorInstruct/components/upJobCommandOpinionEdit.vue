<template>
  <el-dialog
    v-drag
    append-to-body
    :before-close="closeBtn"
    center
    :close-on-click-modal="false"
    :title="pageTitle"
    top="5vh"
    :visible.sync="dialogFormVisible"
    width="1400px"
  >
    <el-form
      ref="dataForm"
      v-loading="formLoading"
      :inline="true"
      label-position="right"
      label-width="130px"
      :model="formData"
      :rules="rules"
    >
      <table class="form-table">
        <tr>
          <td>
            <el-form-item label="编号" prop="serialNumber">
              <el-input
                v-model="formData.serialNumber"
                disabled
                placeholder="自动生成"
                style="width: 250px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="所属项目" prop="projectCode">
              <el-input
                v-model="formData.projectCode"
                :disabled="!formData.isAdd"
                placeholder="选择所属项目"
                readonly
                style="width: 250px"
                @click.native="selectProject"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="项目名称" prop="projectName">
              <el-input
                v-model="formData.projectName"
                disabled
                placeholder="自动关联"
                readonly
                style="width: 250px"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="建设单位" prop="constructionUnit">
              <el-input
                v-model="formData.constructionUnit"
                :disabled="true"
                placeholder="建设单位"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="监理单位" prop="supervisoryUnit">
              <el-input
                v-model="formData.supervisoryUnit"
                :disabled="true"
                placeholder="监理单位"
              />
            </el-form-item>
          </td>
            <td>
            <el-form-item label="施工单位" prop="workUnit">
              <el-input
                v-model="formData.workUnit"
                placeholder="请选择"
                readonly
                @click.native="showConstructionUnitListInfo"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="检查人" prop="checkUserName">
              <el-input v-model="formData.checkUserName" />
            </el-form-item>
          </td>
           <td>
            <el-form-item label="设计单位" prop="designUnit">
              <el-input v-model="formData.designUnit" />
            </el-form-item>
          </td>
          <td></td>
        </tr>
        <tr>
          <td>
            <el-form-item label="主抄" prop="mainCopy">
              <el-input v-model="formData.mainCopy" />
            </el-form-item>
          </td>
         <td >
            <el-form-item label="抄送" prop="copyTo">
              <el-input v-model="formData.copyTo" />
            </el-form-item>
          </td>
          
          <td></td>
        </tr>
        <!-- <tr v-if="!formData.isAdd">
          <td>
            <el-form-item label="创建人" prop="createByName">
              <el-input
                v-model="formData.createByName"
                disabled
                style="width: 250px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="创建日期" prop="createTime">
              <el-date-picker
                v-model="formData.createTime"
                disabled
                placeholder="请选择创建日期"
                style="width: 250px"
                type="date"
              />
            </el-form-item>
          </td>
          <td></td>
        </tr> -->
        <tr>
          <td colspan="3">
            <el-form-item label="经检查发现以下问题" prop="troubleContent">
              <el-input
                v-model="formData.troubleContent"
                maxlength="1000"
                placeholder="请输入内容(不超过1000字)"
                rows="3"
                style="width: 1150px"
                type="textarea"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label="整改要求建议" prop="adviseContent">
              <el-input
                v-model="formData.adviseContent"
                maxlength="1000"
                placeholder="请输入内容(不超过1000字)"
                rows="3"
                style="width: 1150px"
                type="textarea"
              />
            </el-form-item>
          </td>
        </tr>
       
      </table>
    </el-form>
    <div slot="footer" class="dialog-footer" style="text-align: center">
      <el-button type="danger" @click="closeBtn">关闭</el-button>
      <el-button type="primary" @click="saveBtn">保存</el-button>
    </div>
    <ProjectListPopup
      ref="projectListPopupRef"
      @getProjectInfo="getProjectInfo"
    />
    <UploadLargeFileFdfsPopup ref="UploadLargeFileFdfsPopup" />
    <ProjectUserListPopup
      ref="ProjectUserListPopup"
      @getProjectUserInfo="getProjectUserInfo"
    />
  </el-dialog>
</template>

<script>
  import { mapGetters } from 'vuex'
  import { parseTime, genUUID } from '@/utils/th_utils'
  import { saveData } from '@/api/supervisionCloud/superiorInstruct/upJobCommandOpinion-api'
  import UploadLargeFileFdfsPopup from '@/views/common/UploadLargeFileFdfsPopup.vue'
  import ProjectUserListPopup from '@/views/common/ProjectUserListPopup.vue'
  import ProjectListPopup from '@/views/common/ProjectListPopup.vue'
  export default {
    computed: {
      ...mapGetters({
        departList: 'user/departList',
        userName: 'user/userName',
        userId: 'user/userId',
        // 所有的部门列表数据
        allDepartList: 'acl/allDepartList',
      }),
      pageTitle() {
        return this.formData.isAdd ? '新增' : '编辑'
      },
    },
    components: {
      ProjectListPopup,
      ProjectUserListPopup,
      UploadLargeFileFdfsPopup,
    },
    props: {
      departTreeData: {
        type: Array,
        default() {
          return []
        },
      },
    },
    data() {
      return {
        dialogFormVisible: false,
        formLoading: false,
        defaultProps: {
          children: 'children',
          label: 'label',
        },
        formData: {
          id: '',
          serialNumber: '',
          createDepart: '',
          createDepartName: '',
          time: '',
          depart: '',
          departName: '',
          departCode: '',
          createBy: '',
          createByName: '',
          viewUser: '',
          operationCode: '',
          serialNumberTable: '',
          projectId: '',
          constructionUnitIds: '',
          constructionUnitNames: '',
          constructionCode: '',
          constructionContent: '',
          superviseContent: '',
          otherContent: '',
          recorder: '',
          recorderName: '',
          weather: '',
          temperature: '',
          status: '',
          sort: '',
        },
        rules: {
          projectCode: [
            { required: true, message: '请选择项目', trigger: 'change' },
          ],
          checkUserName: [{ required: true, message: '检查人为必填', trigger: 'blur' }],
        },
      }
    },
    methods: {
      async showDialog(row) {
        if (row.isAdd) {
          this.init()
        } else {
          this.formData = { ...row }
        }
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs.UploadLargeFileFdfsPopup.getParamsData({
            bizId: this.formData.id,
            bizCode: this.$route.name,
          })
        })
      },
      init() {
        this.formData = {
          id: genUUID(),
          serialNumber: '',
          createDepart: this.departList[0].id,
          createDepartName: this.departList[0].departName,
          time: parseTime(new Date()),
          depart: '',
          departName: '',
          departCode: '',
          createBy: this.userId,
          createByName: this.userName,
          viewUser: this.userId,
          operationCode: 'SJGZZL',
          serialNumberTable: 'upjob_opinion',
          project_id: '',
          designUnit: '',
          workUnit: '',
          checkUserName: '',
          mainCopy: '',
          copyTo: '',
          troubleContent: '',
          adviseContent: '',
          status: '0',
          sort: '',
          isAdd: true,
        }
      },
      closeBtn() {
        if (this.formData.isAdd) {
          this.$refs.UploadLargeFileFdfsPopup.closeDelImg()
        }
        this.formLoading = false
        this.dialogFormVisible = false
        this.$refs.dataForm.resetFields()
        this.init()
      },
      saveBtn() {
        this.$refs.dataForm.validate((valid) => {
          if (valid) {
            this.formLoading = true
            const formData = {
              ...this.formData,
            }
            saveData(formData).then((res) => {
              if (res.code === 200) {
                this.$baseMessage(
                  '保存成功!',
                  'success',
                  'vab-hey-message-success'
                )
                this.$emit('refreshPage')
                this.formLoading = false
                this.dialogFormVisible = false
              }
            })
          }
        })
      },
      selectProject() {
        this.$refs.projectListPopupRef.showDialog({
          selectIds: this.formData.projectId,
          projectName: this.formData.projectName,
        })
      },
      getProjectInfo(data) {
        if (data.length > 0 && data[0].id !== this.formData.projectId) {
          const dataObj = data[0]
          const { id, projectName, serialNumber } = dataObj
          this.formData = {
            ...this.formData,
            projectId: id,
            projectCode: serialNumber,
            projectName,
          }
          this.$refs.dataForm.clearValidate('projectCode')
        }
      },
      showConstructionUnitListInfo() {},
      showRecorderListInfo() {
        let selectRows = []
        if (this.formData.recorder) {
          selectRows.push({
            id: this.formData.recorder,
            userName: this.formData.recorderName,
          })
        }
        let queryMap = {
          queryMap: {
            projectId: this.formData.projectId,
          },
          selectIds: this.formData.recorder,
          openAllSelectable: true,
          selectRows,
        }
        this.$refs.ProjectUserListPopup.showDialog(queryMap)
      },
      getProjectUserInfo(data) {
        if (data && data.length > 0) {
          this.formData = {
            ...this.formData,
            recorder: data[0].userId,
            recorderName: data[0].userName,
          }
        } else {
          this.formData = {
            ...this.formData,
            recorder: '',
            recorderName: '',
          }
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .image-upload {
    position: absolute;
    right: 21px;
    bottom: 93px;
    width: 536px;
    height: 160px;
    background: #fff;
    border-left: 1px solid #bbb;
  }
</style>
