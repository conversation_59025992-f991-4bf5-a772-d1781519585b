<template>
  <div
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="80px"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item label="编号" prop="serialNumber">
            <el-input
              v-model="queryForm.serialNumber"
              clearable
              placeholder="请输入编号"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="下达人" prop="issuer">
            <el-input
              v-model="queryForm.issuer"
              clearable
              placeholder="请输入下达人"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="下达时间" prop="releaseDate">
              <el-date-picker v-model="queryForm.releaseDate" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="date" placeholder="请选择" />
            </el-form-item>
          <el-form-item>
            <el-button
              icon="el-icon-search"
              native-type="submit"
              type="primary"
              @click="handleQuery"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-right"
              @click.native="resetFormPage"
            >
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
      <vab-query-form-left-panel :span="12">
        <el-button
          v-permissions="{ permission: ['workStopIntruction:add'] }"
          icon="el-icon-plus"
          type="primary"
          @click="handleAdd"
        >
          添加
        </el-button>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="12">
        <el-button
          style="margin: 0 10px 10px 0 !important"
          type="primary"
          @click="clickFullScreen"
        >
          <vab-icon
            :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'"
          />
          表格全屏
        </el-button>
        <el-popover
          ref="popover"
          popper-class="custom-table-checkbox"
          trigger="hover"
        >
          <el-radio-group v-model="lineHeight">
            <el-radio label="medium">大</el-radio>
            <el-radio label="small">中</el-radio>
            <el-radio label="mini">小</el-radio>
          </el-radio-group>
          <template #reference>
            <el-button style="margin: 0 10px 10px 0 !important" type="primary">
              <vab-icon icon="line-height" />
              表格尺寸
            </el-button>
          </template>
        </el-popover>
        <el-popover popper-class="custom-table-checkbox" trigger="hover">
          <el-checkbox-group v-model="checkList">
            <vab-draggable v-bind="dragOptions" :list="columns">
              <div v-for="(item, index) in columns" :key="item + index">
                <vab-icon icon="drag-drop-line" />
                <el-checkbox
                  :disabled="item.disableCheck === true"
                  :label="item.label"
                >
                  {{ item.label }}
                </el-checkbox>
              </div>
            </vab-draggable>
          </el-checkbox-group>
          <template #reference>
            <el-button
              icon="el-icon-setting"
              style="margin: 0 0 10px 0 !important"
              type="primary"
            >
              可拖拽列设置
            </el-button>
          </template>
        </el-popover>
      </vab-query-form-right-panel>
    </vab-query-form>
    <el-table
      ref="tableSort"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      :size="lineHeight"
      stripe
      @selection-change="setSelectRows"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column align="center" label="序号" width="60">
        <template #default="{ $index }">
          {{ getIndex($index) }}
        </template>
      </el-table-column>
      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        :align="item.center ? item.center : 'center'"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'"
      >
        <template #default="{ row }">
          <span v-if="item.label === '下达日期'">
            {{ row[item.prop] | dateformat('YYYY-MM-DD') }}
          </span>
          <span v-else-if="item.label === '停工日期'">
            {{ row[item.prop] | dateformat('YYYY-MM-DD') }}
          </span>
            <span v-else-if="item.label === '复工日期'">
            {{ row[item.prop] | dateformat('YYYY-MM-DD') }}
          </span>
          <span v-else-if="item.label === '状态'">
            <el-switch
              v-if="isDisabledEditFun(row)"
              v-model="row[item.prop]"
              active-value="1"
              disabled
              inactive-value="0"
            />
            <el-switch
              v-else
              v-model="row[item.prop]"
              active-value="1"
              inactive-value="0"
              @change="changeStatus(row)"
            />
          </span>
          <span v-else>
            {{ row[item.prop] }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="320">
        <template #default="{ row }">
          <el-button
            v-permissions="{ permission: ['workStopIntruction:update'] }"
            :disabled="isDisabledEditFun(row) || row.status === '1'"
            icon="el-icon-edit"
            style="margin: 0 10px 10px 0 !important"
            type="primary"
            @click="handleEdit(row)"
          >
            编辑
          </el-button>
          <el-button
            icon="el-icon-view"
            style="margin: 0 10px 10px 0 !important"
            type="success"
            @click="handleDetail(row)"
          >
            详情
          </el-button>
          <el-button
             v-if="row.status=='1'"
            icon="el-icon-view"
            style="margin: 0 10px 10px 0 !important"
            type="success"
            @click="replyInfo(row)"
          >
            回复单
          </el-button>
          <el-dropdown>
            <el-button size="small" type="success">
              <i class="el-icon-more" />
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-if="isDeleteFlag(row)"
                v-permissions="{ permission: ['workStopIntruction:del'] }"
                style="color: #fd5353"
                @click.native.prevent="handleDelete(row)"
              >
                <i class="el-icon-delete" />
                删除
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <!-- 详情 -->
    <tableDetail ref="tableDetail" />
    <!-- 新增修改 -->
    <tableEdit
      ref="tableEdit"
      :depart-tree-data="allDepartList"
      @refreshPage="resetFormPage"
    />
  </div>
</template>
<script>
  import tableMix from '@/views/mixins/table'
  import tableDetail from './components/workStopIntructionDetail.vue'
  import tableEdit from './components/workStopIntructionEdit.vue'
  import {
    deleteData,
    getDataListByPage,
    changeStatus,
  } from '@/api/supervisionCloud/superiorInstruct/workStopIntruction-api'

  export default {
    name: 'workStopIntruction',
    components: {
      tableDetail,
      tableEdit,
    },
    mixins: [tableMix],
    data() {
      return {
        formMaxHeight: 2,
        columns: [
          {
            label: '编号',
            prop: 'serialNumber',
            width: '250',
          },
          {
            label: '项目名称',
            prop: 'projectName',
          },
          {
            label: '监理标段',
            prop: 'supervisionSection',
            width: '120',
          },
          {
            label: '施工标段',
            prop: 'constructionCode',
            width: '120',
          },
           {
            label: '下达人',
            prop: 'issuer',
            width: '120',
          },
           {
            label: '下达日期',
            prop: 'releaseDate',
            width: '120',
          },
             {
            label: '停工日期',
            prop: 'stopDate',
            width: '120',
          },
             {
            label: '复工日期',
            prop: 'replyDate',
            width: '120',
          },
          {
            label: '状态',
            prop: 'status',
            width: '120',
          },
        ],
        defaultProps: {
          children: 'children',
          label: 'label',
        },
        queryForm: {
          releaseDate:'',
          serialNumber: '',
          issuer: '',
        },
      }
    },
    created() {
      // this.getDictListByCodes()
      this.fetchData()
    },
    methods: {
      fetchData() {
        this.listLoading = true

        const queryForm = {
          ...this.queryForm,
          curPage: this.pageInfo.curPage,
          pageSize: this.pageInfo.pageSize,
        }
        getDataListByPage(queryForm).then((res) => {
          this.listLoading = false
          const {
            result: { total, records },
          } = res
          this.list = records
          this.pageInfo.total = Number(total)
        })
      },
      // 添加
      handleAdd() {
        this.$refs['tableEdit'].showDialog({ isAdd: true,isBack: false })
      },
      //回复单
      replyInfo(row){
        this.$refs['tableEdit'].showDialog({
          isBack: true,
          isAdd: false,
          ...row,
        })
      },
      // 编辑
      handleEdit(row) {
        this.$refs['tableEdit'].showDialog({
          isBack: false,
          isAdd: false,
          ...row,
        })
      },
      changeStatus(row) {
        changeStatus({ id: row.id, status: row.status })
          .then(() => {
            this.pageInfo.curPage = 1
            this.fetchData()
            this.$baseMessage('操作成功!', 'success', 'vab-hey-message-success')
          })
          .catch(() => {
            this.$baseMessage('操作失败!', 'error', 'vab-hey-message-error')
          })
      },
      //详情
      handleDetail(row) {
        this.$refs['tableDetail'].showDialog(row)
      },
      // 删除
      handleDelete(row) {
        if (row.id) {
          this.$baseConfirm('你确定要删除当前数据吗', null, async () => {
            deleteData({ id: row.id })
              .then(() => {
                this.pageInfo.curPage = 1
                this.batchDeleteImg(row.id, this.$route.name)
                this.fetchData()
                this.$baseMessage(
                  '删除成功!',
                  'success',
                  'vab-hey-message-success'
                )
              })
              .catch(() => {
                this.$baseMessage('删除失败!', 'error', 'vab-hey-message-error')
              })
          })
        }
      },
      // 是否可编辑
      isDisabledEditFun(row) {
        if (this.isCreater(row.createBy)) {
          // 是创建人 可编辑
          return false
        }
        // 其他情况不可编辑
        return true
      },
      // 是否可删除
      isDeleteFlag(row) {
        if (this.isCreater(row.createBy)) {
          // 是创建人 可删除
          return true
        }
        // 其他情况不可删除
        return false
      },
      // 获取数据字典
      getDictListByCodes() {},
    },
  }
</script>

<style lang="scss" scoped>
  $base: '.depart-management';
  #{$base}-container {
    padding: 0 !important;
    background: $base-color-background !important;

    &.vab-fullscreen {
      padding: 20px !important;
    }
  }
</style>
