<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="80px"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item label="图片名称" label-width="80px" prop="fileName">
            <el-input v-model="queryForm.fileName" />
          </el-form-item>
          <el-form-item>
            <el-button
              icon="el-icon-search"
              native-type="submit"
              type="primary"
              @click="handleQuery"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-right"
              @click.native="resetForm('form')"
            >
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
      <vab-query-form-left-panel :span="12">
        <el-button icon="el-icon-plus" type="primary" @click="handleAdd" v-permissions="{ permission: ['projectBanner:add'] }">
          上传
        </el-button>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="12">
        <el-button
          style="margin: 0 10px 10px 0 !important"
          type="primary"
          @click="clickFullScreen"
        >
          <vab-icon
            :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'"
          />
          表格全屏
        </el-button>
        <el-popover
          ref="popover"
          popper-class="custom-table-checkbox"
          trigger="hover"
        >
          <el-radio-group v-model="lineHeight">
            <el-radio label="medium">大</el-radio>
            <el-radio label="small">中</el-radio>
            <el-radio label="mini">小</el-radio>
          </el-radio-group>
          <template #reference>
            <el-button style="margin: 0 10px 10px 0 !important" type="primary">
              <vab-icon icon="line-height" />
              表格尺寸
            </el-button>
          </template>
        </el-popover>
        <el-popover popper-class="custom-table-checkbox" trigger="hover">
          <el-checkbox-group v-model="checkList">
            <vab-draggable v-bind="dragOptions" :list="columns">
              <div v-for="(item, index) in columns" :key="item + index">
                <vab-icon icon="drag-drop-line" />
                <el-checkbox
                  :disabled="item.disableCheck === true"
                  :label="item.label"
                >
                  {{ item.label }}
                </el-checkbox>
              </div>
            </vab-draggable>
          </el-checkbox-group>
          <template #reference>
            <el-button
              icon="el-icon-setting"
              style="margin: 0 0 10px 0 !important"
              type="primary"
            >
              可拖拽列设置
            </el-button>
          </template>
        </el-popover>
      </vab-query-form-right-panel>
    </vab-query-form>
    <el-table
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      :size="lineHeight"
      stripe
    >
      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        :align="item.center ? item.center : 'center'"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'"
      >
        <template #default="{ row }">
          <span v-if="item.label === '图片名称'">
            <div v-if="/^(jpeg|png|jpg|bmp)$/.test(row.fileExt)">
              <el-image
                title="点击预览"
                style="width: 80px; height: 80px"
                :src="row.urlPath"
                fit="cover"
                :preview-src-list="[row.urlPath]"
              />
              <div>{{ row.fileName }}</div>
            </div>
            <el-link v-else type="primary" :href="'http://kkview.jxth.com.cn:8012/onlinePreview?url='+encodeURIComponent(toBase(row.urlPath))" target="_blank" :underline="false" title="点击预览">
              {{ row.fileName }}
            </el-link>
          </span>
          <span v-else-if="item.label === '图片大小(MB)'">
            {{ fileSizeFormat(row.fileSize) }}
          </span>
          <span v-else-if="item.label === '图片地址'">
            {{ row.urlPath }}
          </span>
          <span v-else>{{ row[item.prop] }}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="操作"
        width="200"
      >
        <template #default="{ row }">
          <el-button
            type="success"
            icon="el-icon-download"
            @click="downloadFile(row)"
            style="margin: 0 10px 10px 0 !important"
          >下载</el-button>
          <el-button
            type="danger"
            icon="el-icon-delete"
            @click="handleDelete(row)"
            v-permissions="{ permission: ['projectBanner:del'] }"
            style="margin: 0 10px 10px 0 !important"
          >删除
          </el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <CommonUploadLargeFileFdfsPopup ref="CommonUploadLargeFileFdfsPopup" class="uploadSlot" @refreshUploadFileList="fetchData"/>
  </div>
</template>

<script>
  import {
    getUploadFilesByPage, deleteFile, downloadFile
  } from '@/api/system/uploadFile-api'
  import tableMix from '@/views/mixins/table'
  import CommonUploadLargeFileFdfsPopup from '@/views/system/common/CommonUploadLargeFileFdfsPopup'
  import { fileAddr } from '@/utils/constants'
  export default {
    name: 'projectBanner',
    mixins: [tableMix],
    components: {
      CommonUploadLargeFileFdfsPopup,
    },
    data() {
      return {
        queryForm: {
          fileName: '',
          bizId: '765640a7-45b1-11eb-9e74-0894ef72d9c4',
          bizCode: 'app-banner'
        },
        checkList: [
          '图片名称', '图片大小(MB)',  '上传人', '上传时间',  '图片类型', '图片地址'
        ],
        columns: [
          {
            label: '图片名称',
            prop: 'fileName',
            disableCheck: true
          },
          {
            label: '图片大小(MB)',
            prop: 'fileSize',
            disableCheck: true,
          },
          {
            label: '上传人',
            prop: 'createByName'
          },
          {
            label: '上传时间',
            prop: 'createTime'
          },
          {
            label: '图片类型',
            prop: 'fileExt'
          },
          {
            label: '图片地址',
            prop: 'urlPath'
          }
        ],
      }
    },
    created() {
      this.fetchData()
    },
    mounted() {},
    beforeDestroy() {},
    methods: {
      async fetchData() {
        const curPage = this.pageInfo.curPage
        const pageSize = this.pageInfo.pageSize
        this.listLoading = true
        const { result: { records, total }} = await getUploadFilesByPage({...this.queryForm, curPage, pageSize})
        this.list = records
        this.pageInfo.total = Number(total)
        this.listLoading = false
      },
      toBase(url){
        return this.$Base64.encode(url)
      },
      fileSizeFormat(fileSize) {
        const fileSizeMb = parseFloat(fileSize / 1048576).toFixed(4)
        return fileSizeMb
      },
      handleAdd() {
        this.$refs.CommonUploadLargeFileFdfsPopup.showUploadLargeDialog(this.queryForm)
      },
      handleDelete(row) {
        if (row.id) {
          this.$baseConfirm('你确定要删除当前文件吗', null, async () => {
            deleteFile(row.id).then(response => {
              this.pageInfo.curPage = 1
              this.fetchData()
              this.$baseMessage('删除成功!', 'success', 'vab-hey-message-success')
            }).catch(err=>{
              this.$baseMessage('删除失败!', 'error', 'vab-hey-message-error')
            })
          })
        }
      },
      downloadFile(row) {
        // 构造a标签 通过a标签来下载
        const url = row.urlPath + '?fileName=' + row.fileName + '.' + row.fileExt
        const a = document.createElement('a')
        a.style.display = 'none'
        a.href = url
        a.target = 'view_window'
        // 此处的download是a标签的内容，固定写法，不是后台api接口
        a.setAttribute('download', row.fileName + '.' + row.fileExt)
        document.body.appendChild(a)
        // 点击下载
        a.click()
        // 下载完成移除元素
        document.body.removeChild(a)
        // 释放掉blob对象
        window.URL.revokeObjectURL(url)
      },
    },
  }
</script>

<style lang="scss" scoped></style>
