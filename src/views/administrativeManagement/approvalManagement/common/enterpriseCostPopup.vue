<template>
  <el-dialog
    v-drag
    append-to-body
    center
    :close-on-click-modal="false"
    :title="title"
    top="5vh"
    :visible.sync="isShowDialog"
    width="500px"
  >
    <el-input
      v-model="filterText"
      clearable
      placeholder="输入关键字进行过滤"
      style="margin-bottom: 10px"
    />
    <el-tree
      ref="departTree"
      check-strictly
      :data="departData"
      :default-checked-keys="defaultKeys"
      default-expand-all
      :filter-node-method="filterNode"
      node-key="id"
      :props="defaultProps"
      v-loading="treeLoading"
      show-checkbox
      style="max-height: 600px; overflow: auto"
      @check-change="handleNodeClick"
    />
    <div slot="footer" class="dialog-footer">
      <el-button type="danger" @click="isShowDialog = false">关闭</el-button>
      <el-button type="primary" @click="confirmSelect">确定</el-button>
    </div>
  </el-dialog>
</template>
<script>
  import {
    materialGetDataList,
    firmCostGetDataList,
  } from '@/api/financialManagement/budgetManagement.js'
  import { getDictList } from '@/api/system/dict-api'
  export default {
    name: 'CommmonDepartPopup',
    components: {},
    data() {
      return {
        title: '采购清单',
        isShowDialog: false,
        departData: [],
        isMultiSelect: false,
        selectedManageCosts: [],
        isRoot: true,
        defaultProps: {
          children: 'children',
          label: 'label',
        },
        json01: [],
        checkedId: '',
        treeLoading: false,
        defaultKeys: [],
        filterText: '',
        queryForm: {
          budgetId: '',
          approvalResult: '2'
        },
      }
    },
    watch: {
      // 根据关键词过滤
      filterText(val) {
        this.$refs.departTree.filter(val)
      },
    },
    async created() {
      this.getDictDetails()
    },
    methods: {
      // 获取成本费用项目
      getDictDetails() {
        getDictList({ dictCode: 'budgetListing' }).then((response) => {
          this.selectedManageCosts = response.result[0].children
        })
      },
      filterNode(value, data) {
        if (!value) return true
        return data.label.indexOf(value) !== -1
      },
      // 只能单选部门方法
      handleNodeClick(data, checked, node) {
        if (this.isMultiSelect) return
        if (checked === true) {
          this.checkedId = data.id
          this.$refs.departTree.setCheckedKeys([data.id])
        } else {
          if (this.checkedId === data.id) {
            this.$refs.departTree.setCheckedKeys([data.id])
          }
        }
      },
      async loadBussinessCoststData() {
        this.treeLoading = true
        await materialGetDataList(this.queryForm).then((response) => {
          const json = JSON.parse(
            JSON.stringify(response.result).replace(
              /supplierUnitName/g,
              'label'
            )
          )
          this.departData = json
          if (this.isRoot) {
            this.departData = this.attachTopNode(
              this.departData,
              '材料成本',
              null,
              'materialCost'
            )
          }
        })
        await firmCostGetDataList(this.queryForm).then((res) => {
          this.json01 = JSON.parse(
            JSON.stringify(res.result).replace(/countName/g, 'label')
          )
          this.dataListSorting(this.json01)
          let data01 = this.json01
          if (this.isRoot) {
            data01 = this.attachTopNode(
              data01,
              '项目成本',
              null,
              'enterpriseCost'
            )
          }
          this.departData = this.departData.concat(data01)
        })
        this.treeLoading = false
      },
      // 对项目成本列表中数据根据费用明细管理的列表数据进行排序
      dataListSorting(json01) {
        // 创建一个 Map，存储 费用明细管理的列表数据 中元素的顺序
        const orderMap = new Map()
        this.selectedManageCosts[0].children.forEach((item, index) => {
          orderMap.set(item.dictName, index)
        })

        // 根据 orderMap 中的顺序对 项目成本列表中数据 进行排序
        json01.sort((a, b) => {
          const indexA = orderMap.get(a.label)
          const indexB = orderMap.get(b.label)
          return indexA - indexB
        })
      },
      attachTopNode(treeData, label = '全部', icon = 'el-icon-document', id) {
        const parentNode = []
        const topNode = {}
        topNode.id = id
        topNode.label = label
        topNode.icon = icon
        topNode.children = treeData
        topNode.disabled = true
        parentNode.push(topNode)
        return parentNode
      },
      async showBussinessCoststDialog(ids, budgetId, isMultiSelect, isRoot) {
        this.isShowDialog = true
        this.isRoot = isRoot
        this.queryForm.budgetId = budgetId
        await this.loadBussinessCoststData()
        this.defaultKeys = ids
        this.isMultiSelect = isMultiSelect
        this.dialogFormVisible = true
      },
      confirmSelect() {
        var selectDatas = null
        selectDatas = this.$refs.departTree.getCheckedNodes()
        this.$emit('callBackEnterpriseCost', selectDatas)
        this.isShowDialog = false
      },
    },
  }
</script>
