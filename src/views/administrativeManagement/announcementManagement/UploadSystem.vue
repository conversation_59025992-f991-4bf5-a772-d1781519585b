<template>
  <el-dialog  v-drag append-to-body :title="title" :visible.sync="isShowDialog" width="1000px" top="5vh"
    :close-on-click-modal="false" :show-close="true" center @close="close">
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form ref="searchForm" :inline="true" :model="queryData" class="demo-form-inline">
          <el-form-item label="文件名称" prop="fileName">
            <el-input v-model="queryData.fileName" placeholder="请输入名称" style="width: 200px" />
          </el-form-item>
          <el-button icon="el-icon-search" type="primary" @click.native="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh-right" @click.native="resetForm('searchForm')">重置</el-button>
        </el-form>

        <el-table ref="dataTable" v-loading="listLoading" :data="tableData" element-loading-text="加载中" max-height="490"
          border stripe class="tableList" size="medium" row-key="id" @select="handleSelect" @row-click="rowClick"
          @select-all="selectAll">
          <el-table-column type="selection" min-width="5%" align="center" />
          <el-table-column prop="fileName" label="文件名" min-width="15%" align="center">
            <template #default="{ row }">
              <div v-if="/^(jpeg|png|jpg|bmp)$/.test(row.fileExt)">
                <el-image
                  title="点击预览"
                  style="width: 80px; height: 80px"
                  :src="row.urlPath"
                  fit="cover"
                  :preview-src-list="[row.urlPath]"
                />
                <div>{{ row.fileName }}</div>
              </div>
              <span v-else style="color: #1890ff">{{ row.fileName }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="fileSize" label="文件大小(MB)" min-width="10%" align="center"
            :formatter="fileSizeFormat" /> -->
          <el-table-column prop="fileExt" label="文件类型" min-width="8%" align="center" />
          <!-- <el-table-column prop="bizCode" label="业务编码" min-width="10%" align="center" /> -->
          <el-table-column prop="createByName" label="上传人" min-width="10%" align="center" />
          <el-table-column prop="createTime" label="上传时间" min-width="10%" align="center" />
          <!-- <el-table-column label="操作" min-width="20%" align="center">
            <template slot-scope="{row}">
              <el-button size="mini" type="danger" icon="el-icon-delete" @click="deleteFile(row)">删除</el-button>
            </template>
          </el-table-column> -->
        </el-table>
        <div style="margin-top:15px;text-align: left">
          <el-pagination :current-page="pageInfo.curPage" :page-sizes="[10, 20, 50, 100]" :page-size="pageInfo.pageSize"
            background layout="total, sizes, prev, pager, next, jumper" :total="pageInfo.total"
            @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </div>
      </el-col>
    </el-row>
    <div slot="footer" class="dialog-footer">
      <el-button type="danger" @click="isShowDialog = false">关闭</el-button>
      <el-button type="primary" @click="confirmSelect">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import request from '@/utils/gkRequest'
  import {
    getUploadFilesByPage,
    deleteFile
  } from '@/api/system/uploadFile-api'
  import { fileAddr } from '@/utils/constants'
  export default {
    name: 'UploadSystem',
    props: {
      isMulti: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        listLoading: false,
        queryData: {
          fileName: '',
          bizId: '772b721a-45a3-11eb-9e74-0894ef72d9c4',
          bizCode: 'UploadSystem'
        },
        pageInfo: {
          curPage: 1,
          pageSize: 10,
          total: 100
        },
        tableData: [],
        title: '资源库',
        isShowDialog: false,
        mulSelect: [],
      }
    },
    methods: {
      show() {
        this.isShowDialog = true
        this.initSearch()
      },
      fileSizeFormat(row, column) {
        const fileSize = row[column.property];
        const fileSizeMb = parseFloat(fileSize / 1048576).toFixed(4);
        return fileSizeMb
      },
      async initSearch() {
        this.pageInfo.curPage = 1
        this.pageInfo.pageSize = 10
        await this.fetchData()
      },
      resetForm(formName) {
        this.$refs[formName].resetFields()
        this.pageInfo.curPage = 1
        this.refreshUploadFiles()
      },
      deleteFile(row) {
        if(!row.id) return
        this.$baseConfirm('你确定要删除当前文件吗', null, async () => {
          deleteFile(row.id).then(response => {
            this.pageInfo.curPage = 1
            this.fetchData()
            this.$baseMessage('删除成功!', 'success', 'vab-hey-message-success')
          }).catch(err=>{
            this.$baseMessage('删除失败!', 'error', 'vab-hey-message-error')
          })
        })
      },
      async fetchData() {
        const curPage = this.pageInfo.curPage
        const pageSize = this.pageInfo.pageSize
        this.listLoading = true
        const params = {
          ...this.queryData,
          curPage: this.pageInfo.curPage,
          pageSize: this.pageInfo.pageSize
        }
        const { result: { records, total }} = await getUploadFilesByPage(params)
        this.tableData = records
        this.pageInfo.total = Number(total)
        this.listLoading = false
        if(this.mulSelect.length) {
          this.tableToggleSelect()
        }
      },
      tableToggleSelect() {
        const rowData = []
        const selectIds = this.mulSelect.map(item => item.id)
        this.tableData.forEach(item => {
          if(selectIds.includes(item.id)) {
            rowData.push(item)
          }
        })
        this.$nextTick(() => {
          this.$refs.dataTable.clearSelection()
          if(rowData.length > 0) {
            rowData.forEach(item => {
              this.$refs.dataTable.toggleRowSelection(item)
            })
          }
        })
      },
      handleQuery() {
        this.pageInfo.curPage = 1
        this.refreshUploadFiles()
      },
      handleSizeChange(val) {
        this.pageInfo.pageSize = val
        this.pageInfo.curPage = 1
        this.refreshUploadFiles()
      },
      handleCurrentChange(val) {
        this.pageInfo.curPage = val
        this.refreshUploadFiles()
      },
      selectAll(selection) {
        this.mulSelect = []
        this.$refs.dataTable.clearSelection()
      },
      rowClick(row, column, event) {
        const isSelectedIdx = this.mulSelect.findIndex(item => item.id === row.id)
        if(!this.isMulti) {
          // 单选处理
          this.mulSelect = []
          isSelectedIdx < 0 && this.$refs.dataTable.clearSelection()
        }
        if(isSelectedIdx < 0) {
          this.$refs.dataTable.toggleRowSelection(row, true)
          this.mulSelect.push(row)
        } else {
          this.$refs.dataTable.toggleRowSelection(row, false)
          this.mulSelect.splice(isSelectedIdx, 1)
        }
      },
      handleSelect(selection, row) {
        const isSelectedIdx = this.mulSelect.findIndex(item => item.id === row.id)
        if(!this.isMulti) {
          // 单选处理
          this.mulSelect = []
          if(isSelectedIdx < 0) {
            this.$refs.dataTable.clearSelection()
            this.$refs.dataTable.toggleRowSelection(row, true)
          }
        }
        if(isSelectedIdx < 0) {
          this.mulSelect.push(row)
        } else {
          this.$refs.dataTable.toggleRowSelection(row, false)
          this.mulSelect.splice(isSelectedIdx, 1)
        }
      },
      async refreshUploadFiles() {
        await this.fetchData()
      },
      confirmSelect() {
        if(!this.mulSelect.length) {
          this.$baseMessage('未选择任何文件!', 'warning', 'vab-hey-message-warning')
          return
        }
        this.$emit('callBackData', this.mulSelect)
        this.isShowDialog = false
      },
      close() {
        this.mulSelect = []
        this.$refs.dataTable.clearSelection()
      }
    }
  }
</script>

<style scoped lang="scss">
::v-deep .el-table__header-wrapper .el-checkbox__inner {
  display: none;
}
</style>
