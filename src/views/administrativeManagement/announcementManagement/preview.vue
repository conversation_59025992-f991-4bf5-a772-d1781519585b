<template>
  <el-dialog
    append-to-body
    :visible.sync="dialogVisible"
    @close="close"
    :fullscreen="true"
    :close-on-click-modal="false"
    custom-class="preview-container"
  >
    <div class="preview-content">
      <div class="content-top">
        <div class="content-title">{{ announcementData.title }}</div>
        <div class="author-time">
          <span>发布人：{{ announcementData.createByName }}</span>
          <span>发布时间：{{ announcementData.timingTime }}</span>
        </div>
      </div>
      <div class="announcement-content-area ql-editor">
        <div v-html="announcementData.content"></div>
        <div class="attch-box" v-if="announcementData.accessory">
          <el-divider content-position="left">附件</el-divider>
          <div class="attch-item" v-for="(item, index) in announcementData.accessory" :key="index">
            <div class="attch-con">
              {{ item.fileName + '.' + item.fileExt }}
              <span><i class="el-icon-download"></i></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
  import { parseTime } from '@/utils'
  export default {
    data() {
      return {
        dialogVisible: false,
        announcementData: {}
      }
    },
    methods: {
      showDialog(data) {
        this.dialogVisible = true
        const accessoryData = !!data.accessory && JSON.parse(data.accessory)
        this.announcementData = {
          createByName: this.$store.getters['user/userName'],
          ...data,
          accessory: accessoryData,
          timingTime: data.timingTime || parseTime(new Date())
        }
      },
      close() {
        this.announcementData = {}
      }
    }
  }
</script>

<style lang="scss" scoped>
  ::v-deep .preview-container {
    background: linear-gradient(45deg, #e2f2ff, #eef2fe, #e8efff, #e9f0ff) !important;
    .el-dialog__body {
      height: calc(100% - 30px);
    }
    .preview-content {
      width: calc(100% - 350px);
      height: 100%;
      margin: 0 auto;
      box-sizing: border-box;
      padding: 20px 30px;
      background-color: #fff;
      border-radius: 10px;

      .content-top {
        margin-bottom: 20px;
        text-align: center;

        .content-title {
          color: rgb(15, 25, 38);
          font-size: 18px;
          font-weight: 600;
          width: 50%;
          margin: 0 auto;
          margin-bottom: 15px;
          padding-top: 15px;
        }

        .author-time {
          text-align: center;
          color: rgb(152, 157, 162);
          font-size: 12px;

          span {
            padding-left: 10px;
          }
        }
      }
      .attch-box {
        margin-top: 50px;
        padding-bottom: 15px;

        .attch-item {
          margin-bottom: 10px;
          color: #31e2ff;

          .attch-con {
            display: inline-block;
            cursor: pointer;

            &:hover {
              text-decoration: underline;
              color: #1890ff;
            }

            span {
              font-size: 16px;
              padding-left: 5px;
              vertical-align: middle;
            }
          }
        }

        .el-divider__text {
          color: rgb(152, 157, 162);
        }
        .el-page-header__content {
          font-size: 14px !important;
        }
      }
    }
    .announcement-content-area {
      height: calc(100% - 90px);
      overflow-y: scroll;
    }
  }

</style>
<style>
  .announcement-content-area img {
    max-width: 100%;
  }
</style>
