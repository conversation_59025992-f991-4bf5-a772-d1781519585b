<template>
  <el-dialog
    v-drag
    append-to-body
    center
    :close-on-click-modal="false"
    :title="title"
    top="5vh"
    :visible.sync="dialogFormVisible"
    width="1400px"
    @close="close"
  >
    <el-form
      ref="dataForm"
      v-loading="formloading"
      :inline="true"
      label-position="right"
      label-width="130px"
      :model="formData"
      :rules="rules"
    >
      <table v-loading="formloading" class="form-table">
        <tr class="title">
          <td colspan="3">基本信息</td>
        </tr>
        <tr>
          <td>
            <el-form-item label="单号" prop="serialNumber">
              <el-input
                v-model="formData.serialNumber"
                disabled
                placeholder="自动生成"
                readonly
                style="width: 280px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="申请人" prop="createBy">
              <el-input
                v-model="formData.createByName"
                disabled
                readonly
                style="width: 280px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="申请日期" prop="recordDate">
              <el-date-picker
                v-model="formData.recordDate"
                type="date"
                style="width: 250px"
                placeholder="请选择申请日期"
              ></el-date-picker>
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="所在部门" prop="createDepartName">
              <el-input v-model="formData.createDepartName" disabled />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="是否节假日" prop="isFestival">
              <el-radio-group
                v-model="formData.isFestival"
                size="small"
                style="width: 200px"
              >
                <el-radio-button label="1">是</el-radio-button>
                <el-radio-button label="2">否</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </td>
          <td>
            <el-form-item label="同行加班人" prop="peerPerson">
              <t-form-user
                v-model="formData.peerPerson"
                multiple
                placeholder="请选择"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="开始日期" prop="startTime">
              <el-date-picker
                v-model="formData.startTime"
                format="yyyy-MM-dd HH:mm"
                placeholder="选择时间"
                style="width: 280px"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm"
                @change="timewstamp"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="结束日期" prop="endTime">
              <el-date-picker
                v-model="formData.endTime"
                format="yyyy-MM-dd HH:mm"
                type="datetime"
                placeholder="选择时间"
                style="width: 280px"
                value-format="yyyy-MM-dd HH:mm"
                @change="timewstamp"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="时长" prop="duration">
              <el-input
                v-model="formData.duration"
                disabled
                style="width: 280px"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label="加班事由" prop="reasons">
              <el-input
                v-model="formData.reasons"
                maxlength="1000"
                placeholder="请输入内容(不超过1000字)"
                style="width: 1180px"
                type="textarea"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label="备注" prop="remarks">
              <el-input
                v-model="formData.remarks"
                maxlength="1000"
                placeholder="请输入内容(不超过1000字)"
                style="width: 1180px"
                type="textarea"
              />
            </el-form-item>
          </td>
        </tr>
      </table>
      <UploadLargeFileFdfsPopup ref="UploadLargeFileFdfsPopup"/>
    </el-form>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
  import { saveData } from '@/api/administrativeManagement/overtimeApplication-api.js'
  import { areaList } from '@/utils/area.js'
  import { getDepartList } from '@/api/system/depart-api'
  import { genUUID, parseTime } from '@/utils/th_utils.js'
  import { mapGetters } from 'vuex'
  import UploadItem from '../../../administrativeManagement/announcementManagement/UploadItem.vue'
  import UploadLargeFileFdfsPopup from "@/views/common/UploadLargeFileFdfsPopup.vue";

  export default {
    name: 'TableEdit',
    components: {
      UploadLargeFileFdfsPopup,
      UploadItem,
    },
    props: {},
    data() {
      const validateDate = (rule, value, callback) => {
        let startDate = Date.parse(this.formData.startTime)
        let endDate = Date.parse(this.formData.endTime)
        if (endDate && endDate && startDate > endDate) {
          this.formData.duration = '自动计算'
          callback(new Error('结束时间不能小于开始时间，请修改!'))
        }
        callback()
      }
      return {
        title: '',
        dialogFormVisible: false,
        formloading: false,
        formData: {},
        recordDate: '',
        areaOptions: areaList,
        departTreeData: [],
        projectList: [],
        isShow: false,
        defaultProps: {
          children: 'children',
          label: 'label',
        },
        rules: {
          startTime: [
            { required: true, message: '请选择开始时间', trigger: 'change' },
            { validator: validateDate, trigger: 'change' },
          ],
          endTime: [
            { required: true, message: '请选择结束时间', trigger: 'change' },
            { validator: validateDate, trigger: 'change' },
          ],
          reasons: {
            required: true,
            message: '加班事由为必填',
            trigger: 'blur',
          },
        },
        queryForm: {},
        duration: '',
      }
    },
    computed: {
      ...mapGetters({
        userId: 'user/userId',
        userName: 'user/userName',
        departList: 'acl/departList',
      }),
    },
    created() {
      this.getDepartList()
    },
    methods: {
      getDepartList() {
        getDepartList({}).then((response) => {
          this.departTreeData = response.result
        })
      },
      timewstamp() {
        if (this.formData.startTime && this.formData.endTime) {
          var stime = Date.parse(new Date(this.formData.startTime))
          var etime = Date.parse(new Date(this.formData.endTime))
          // 两个时间戳相差的毫秒数
          var usedTime = etime - stime
          // 计算相差的天数
          // let days = Math.floor(usedTime / (24 * 3600 * 1000))
          // 计算天数后剩余的毫秒数
          //let millisecond = usedTime % (24 * 3600 * 1000);
          // 计算出小时数
           let hours = Math.floor(usedTime / (3600 * 1000));
          // 计算出剩余的毫秒数，然后转换成分钟
          let minutes = Math.floor((usedTime % (3600 * 1000)) / (60 * 1000));
          var time = hours  + '小时'
          if (minutes){
            time = time + minutes + '分';
          }
          this.formData.duration = time
        }
      },
      showEdit(type, row) {
        if (!row) {
          this.title = '新增加班申请'
          this.dialogStatus = 'create'
          this.initForm()
        } else {
          this.title = '编辑加班申请'
          this.dialogStatus = 'update'
          this.formData = Object.assign({ isAdd: false }, row)
          console.log(this.formData)
          this.timewstamp()
        }
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].clearValidate()
            this.$refs.UploadLargeFileFdfsPopup.getParamsData({bizId: this.formData.id, bizCode: 'overtimeApplication'})

        })
      },
      // 初始化表单
      initForm() {
        this.formData = {
          id: genUUID(),
          reasons: '', //加班事由
          peerName: '', //同行人名字
          updateBy: '',
          peerPerson: '', //同行人id
          createDepart: this.departList[0].id, //部门id
          departList: this.departList,
          createDepartName : this.departList[0].departName,
          startTime: '', //开始时间
          departName: '', //部门名字
          serialNumber: '', //序号
          isFestival:'2',
          departCode: this.departList[0].departCode, //部门code
          createByName: this.userName,
          viewUser: this.userId, //创建人名字
          updateTime: '',
          operationCode: 'JBSQ',
          serialNumberTable:'overtime_application',
          sort: '',
          createBy: this.userId, //创建人ID
          createTime: '',
          endTime: '', //结束时间
          recordDate: parseTime(new Date()), //申请日期
          remarks: '',
          duration: '',
          isAdd: true,
        }
      },
      save() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            // return
            saveData(this.formData)
              .then((response) => {
                this.$baseMessage(
                  this.formData.isAdd ? '新增成功！' : '修改成功!',
                  'success',
                  'vab-hey-message-success'
                )
                this.close()
                this.$emit('fetch-data')
              })
              .catch((err) => {
                this.$baseMessage(
                  this.formData.isAdd ? '新增失败！' : '修改失败!',
                  'error',
                  'vab-hey-message-error'
                )
              })
          }
        })
      },
      close() {
        this.$refs['dataForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
      },
    },
  }
</script>

<style lang="scss" scoped>
  .form-item-container {
    height: 50px;

    .select-list {
      width: 100%;
      height: 100%;
    }
  }
</style>
