<template>
  <el-dialog
    v-drag
    append-to-body
    center
    :close-on-click-modal="false"
    :title="actionMap[dialogStatus]"
    top="5vh"
    :visible.sync="dialogFormVisible"
    width="1400px"
    @close="close"
  >
    <el-form
      ref="dataForm"
      v-loading="formloading"
      :inline="true"
      label-position="right"
      label-width="120px"
      :model="formData"
      :rules="rules"
    >
      <table v-loading="formloading" class="form-table">
        <tr class="title">
          <td colspan="3">基本信息</td>
        </tr>
        <tr>
          <td>
            <el-form-item label="单号" prop="serialNumber">
              <el-input
                v-model="formData.serialNumber"
                disabled
                placeholder="自动生成"
                style="width: 280px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="申请人" prop="createByName">
              <el-input
                v-model="formData.createByName"
                disabled
                style="width: 280px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="申请日期" prop="recordDate">
              <el-date-picker
                v-model="formData.recordDate"
                disabled
                format="yyyy-MM-dd"
                placeholder="选择时间"
                style="width: 280px"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="车牌号" prop="plateNumber">
              <el-input
                v-model="formData.plateNumber"
                @click.native="showVehicleListInfo"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="费用所属项目" prop="projectName">
              <el-input
                v-model="formData.projectName"
                :disabled="!formData.isAdd"
                filterable
                placeholder="请选择"
                style="width: 280px"
                @click.native="selectContractProject"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="费用所属部门" prop="depart">
              <t-form-tree
                v-model="formData.depart"
                :default-props="{ children: 'children', label: 'departName' }"
                :disabled="!formData.isAdd"
                :show-top="false"
                :tree-data="departTreeData"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="车辆类型" prop="typeName">
              <el-input v-model="formData.typeName" disabled />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="车辆品牌" prop="brand">
              <el-input v-model="formData.brand" disabled />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="加油型号" prop="oilTypeName">
              <el-input v-model="formData.oilTypeName" disabled />
            </el-form-item>
          </td>
        </tr>

        <tr>
          <td>
            <el-form-item label="车辆排量" prop="outputVolume">
              <el-input v-model="formData.outputVolumeName" disabled />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="是否配油卡" prop="isOilCardName">
              <el-input v-model="formData.isOilCardName" disabled />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="兼职司机" prop="partTimeDriver">
              <t-form-user
                v-model="formData.partTimeDriver"
                placeholder="请选择"
              />
            </el-form-item>
          </td>
        </tr>
        <tr></tr>
        <tr>
          <td>
            <el-form-item label="预计开始时间" prop="startTime">
              <el-date-picker
                v-model="formData.startTime"
                format="yyyy-MM-dd"
                placeholder="选择时间"
                style="width: 280px"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="预计结束时间" prop="endTime">
              <el-date-picker
                v-model="formData.endTime"
                format="yyyy-MM-dd"
                placeholder="选择时间"
                style="width: 280px"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label="备注" prop="remarks">
              <el-input
                v-model="formData.remarks"
                maxlength="1000"
                placeholder="请输入内容(不超过1000字)"
                style="width: 1180px"
                type="textarea"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label=" 上传照片">
              <div class="form-item-container">
                <div class="item-top">
                  <span>上传图片大小不超过10M，类型为jpg、png</span>
                </div>

                <div class="item-radio-btn" style="margin-top: 10px">
                  <el-button icon="el-icon-upload2" @click="showUploadItem()">
                    直接上传
                  </el-button>
                </div>
                <div v-if="formData.picture" class="select-list">
                  <div class="select-item">
                    <el-image
                      :preview-src-list="[formData.picture]"
                      :src="formData.picture"
                      style="width: 40px; height: 40px"
                    />
                    <i
                      class="el-icon-delete"
                      @click="formData.picture = ''"
                    ></i>
                  </div>
                </div>
              </div>
            </el-form-item>
          </td>
        </tr>
      </table>
    </el-form>

    <!-- 附件上传 -->
    <UploadLargeFileFdfsPopup ref="UploadLargeFileFdfsPopup" />
    <UploadItem ref="uploadItemRef" @callbackSuccess="callbackSuccess" />
    <ProjectListPopup
      ref="projectListPopupRef"
      @getProjectInfo="getProjectInfo"
    />
    <VehicleListPopup ref="VehicleListPopup" @getVehicleInfo="getVehicleInfo" />
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="saveData">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
  import { genUUID } from '@/utils/th_utils.js'
  import { saveData } from '@/api/administrativeManagement/vehicleAllocation-api'
  import { mapGetters } from 'vuex'
  import UploadLargeFileFdfsPopup from '@/views/common/UploadLargeFileFdfsPopup.vue'
  import UploadItem from '../../../administrativeManagement/announcementManagement/UploadItem.vue'
  import VehicleListPopup from '@/views/common/VehicleListPopup.vue'
  import { getDepartList } from '@/api/system/depart-api'
  import ProjectListPopup from '@/views/common/ProjectListPopup.vue'

  export default {
    name: 'TableEdit',
    components: {
      UploadLargeFileFdfsPopup,
      UploadItem,
      VehicleListPopup,
      ProjectListPopup,
    },
    props: {},
    data() {
      const validateDate = (rule, value, callback) => {
        let startDate = Date.parse(this.formData.startTime)
        let endDate = Date.parse(this.formData.endTime)
        if (endDate && endDate && startDate > endDate) {
          this.formData.duration = '自动计算'
          callback(new Error('结束时间不能小于开始时间，请修改!'))
        }
        callback()
      }
      return {
        outputVolumeList: [
          { label: '1.8及以上', value: '1', subsidyStandard: '1.6元/公里' },
          { label: '1.8以下', value: '2', subsidyStandard: '1.1元/公里' },
        ],
        title: '',
        formloading: false,
        visible: false,
        departTreeData: [],
        dialogStatus: '',
        dialogFormVisible: false,
        actionMap: {
          update: '编辑车辆调配信息',
          create: '新增车辆调配信息',
        },
        formData: {},
        rules: {
          // substitute: { required: true, message: '代班人为必填', trigger: 'blur' },
          startTime: [
            { required: true, message: '请选择开始时间', trigger: 'change' },
            { validator: validateDate, trigger: 'change' },
          ],
          endTime: [
            { required: true, message: '请选择结束时间', trigger: 'change' },
            { validator: validateDate, trigger: 'change' },
          ],
        },
      }
    },
    computed: {
      ...mapGetters({
        userId: 'user/userId',
        userName: 'user/userName',
        departList: 'acl/departList',
      }),
    },
    created() {
      this.getDepartList()
    },
    methods: {
      getOutputVolumeName(row) {
        if (!row.outputVolume) return ''
        this.formData.outputVolumeName = this.outputVolumeList.find(
          (item) => item.value === row.outputVolume
        ).label
      },
      async getProjectInfo(data) {
        console.log(data, 'data')
        if (data.length > 0) {
          const dataObj = data[0]
          const { id, projectName, serialNumber } = dataObj
          if (this.formData.budgetId && id !== this.formData.budgetId) {
            this.formData.budgetId = ''
            this.detailList = []
          }
          this.formData = {
            ...this.formData,
            projectId: id,
            projectName,
            projectSerialNumber: serialNumber,
          }
          this.formData.depart = dataObj.depart
          this.formData.departName = dataObj.departName
          this.formData.departCode = dataObj.departCode
          console.log(projectName, 'projectName')
          this.$refs.dataForm.clearValidate('projectName')
        }
      },
      selectContractProject() {
        this.$refs.projectListPopupRef.showDialog({
          selectIds: this.formData.projectId,
          projectName: this.formData.projectName
        })
      },
      getVehicleInfo(data) {
        console.log(data)
        if (data.length > 0) {
          let datas = data[0]
          this.formData.isOilCardName = datas.isOilCardName
          this.formData.plateNumber = datas.plateNumber
          this.formData.oilTypeName = datas.oilTypeName
          this.formData.vehicleId = datas.id
          this.formData.outputVolume = datas.outputVolume
          this.formData.typeName = datas.typeName
          this.formData.brand = datas.brand
          this.getOutputVolumeName(datas)
        }
      },
      showVehicleListInfo() {
        let queryMap = {
          stateList: [0],
        }
        console.log(queryMap)
        this.$refs.VehicleListPopup.showDialog({ queryMap })
      },
      getDepartList() {
        getDepartList({}).then((response) => {
          this.departTreeData = response.result
        })
      },
      showUploadItem() {
        const data = {
          bizId: this.formData.id,
          bizCode: 'vehicleAllocation2',
          isOneFile: true,
        }
        this.$refs.uploadItemRef.showUploadFileDialog(data)
      },
      callbackSuccess(data) {
        console.log('===========')
        console.log(data.urlPath)
        this.formData.picture = data.urlPath
      },
      setAllCheck() {
        if (this.formData.startTime && this.formData.endTime) {
          var sTime = Date.parse(this.formData.startTime)
          var eTime = Date.parse(this.formData.endTime)
          // 两个时间戳相差的毫秒数
          var usedTime = eTime - sTime
          // 计算相差的天数
          var days = Math.floor(usedTime / (24 * 3600 * 1000))
          // 计算天数后剩余的毫秒数
          var leave1 = usedTime % (24 * 3600 * 1000)
          // 计算出小时数
          var hours = Math.floor(leave1 / (3600 * 1000))
          this.formData.duration = days + '天' + hours + '时'
        }
      },
      callBackUser(val, data) {
        this.formData.substitute = val.map((item) => item).join(',')
        this.formData.substituteName = data.map((item) => item).join(',')
      },

      // 初始化表单
      initForm() {
        this.formData = {
          id: genUUID(),
          isAdd: true,
          operationCode: 'CLDP',
          serialNumberTable: 'vehicle_allocation',
          startTime: '', //开始时间
          endTime: '', //结束时间
          recordDate: new Date(), //申请日期
          createDepart: this.departList[0].id, //项目所属部门id
          createDepartName: this.departList[0].departName, //项目所属部门名称
          depart: this.departList[0].id, //项目所属部门id
          departName: this.departList[0].departName, //项目所属部门名称
          departCode: this.departList[0].departCode, //部门code
          departList: this.departList,
          createBy: this.userId, //创建人id
          createByName: this.userName,
          viewUser: this.userId, //创建人姓名
          createTime: new Date(), //创建时间
          updateBy: '', //修改人 修改的时候填写
          updateByName: '',
          updateTime: '', //修改时间
          remarks: '', //备注
          outputVolumeName: '',
          sort: '', //排序
          partTimeDriver: '', //兼职司机
          picture: '', //图片
          projectId: '', //项目id
          projectName: '', //项目名称
          isOilCardName: '',
          plateNumber: '',
          oilTypeName: '',
          vehicleId: '',
          outputVolume: '',
          typeName: '',
          brand: '',
        }
      },

      showEdit(row) {
        if (!row) {
          this.dialogStatus = 'create'
          this.initForm()
        } else {
          this.dialogStatus = 'update'
          this.dialogFormVisible = true
          this.formData = Object.assign({ isAdd: false }, row)
          this.formData.updateBy = this.userName
          // this.formData.updateByName = this.userName
          this.formData.updateTime = new Date()
        }
        this.$nextTick(() => {
          this.$refs.UploadLargeFileFdfsPopup.getParamsData({
            bizId: this.formData.id,
            bizCode: 'vehicleAllocation',
          })
        })
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].clearValidate()
        })
      },

      saveData() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            saveData(this.formData)
              .then((response) => {
                this.$baseMessage(
                  this.formData.isAdd ? '新增成功！' : '修改成功!',
                  'success',
                  'vab-hey-message-success'
                )
                this.close()
                this.$emit('fetch-data')
              })
              .catch((err) => {
                this.$baseMessage(
                  this.formData.isAdd ? '新增失败！' : '修改失败!',
                  'error',
                  'vab-hey-message-error'
                )
              })
          }
        })
      },
      close() {
        this.$refs['dataForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
      },
    },
  }
</script>

<style lang="scss" scoped>
  .dropTree {
    width: 100%;
    max-height: 300px;
    overflow: auto;
  }
</style>
