<template>
  <el-dialog
    v-drag
    append-to-body
    center
    :close-on-click-modal="false"
    title="合同详情"
    top="5vh"
    :visible.sync="dialogDetailVisible"
    width="1040px"
  >
    <el-form
      ref="dataForm"
      v-loading="formLoading"
      :inline="true"
      label-position="right"
      label-width="130px"
      :model="formData"
    >
      <!-- <div class="form-table-title">基本信息</div> -->
      <el-descriptions
        border
        class="margin-top"
        :column="3"
        :label-style="labelStyle"
        size="medium"
      >
        <el-descriptions-item>
          <template slot="label">姓名</template>
          {{ formData.userName }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">部门</template>
          {{ $parent.getTypeNameByDepartList(formData.departList) }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">合同公司</template>
          {{ formData.contractCompany }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">合同类型</template>
          {{ formData.contractTypeName }}
        </el-descriptions-item>

        <el-descriptions-item>
          <template slot="label">首次合同起始日</template>
          {{ formData.firstStartTime | dateformat('YYYY-MM-DD') }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">首次合同到期日</template>
          {{ formData.firstEndTime | dateformat('YYYY-MM-DD') }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">现合同起始日</template>
          {{ formData.nowStartTime | dateformat('YYYY-MM-DD') }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">现合同到期日</template>
          {{ formData.nowEndTime | dateformat('YYYY-MM-DD') }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">签约次数</template>
          {{ formData.numberContracts }}
        </el-descriptions-item>
      </el-descriptions>
    </el-form>
    <!-- 附件上传 -->
    <UploadLargeFileFdfsPopupDetail
      ref="UploadLargeFileFdfsPopupDetail"
      style="margin-top: 20px"
    />
    <div slot="footer" class="dialog-footer">
      <el-button type="danger" @click="dialogDetailVisible = false">
        关闭
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import UploadLargeFileFdfsPopupDetail from '@/views/common/UploadLargeFileFdfsPopupDetail.vue'
  export default {
    components: {
      UploadLargeFileFdfsPopupDetail,
    },
    props: {},
    data() {
      return {
        dialogDetailVisible: false,
        formLoading: false,
        formData: {},
        labelStyle: {
          width: '130px',
        },
      }
    },
    computed: {},
    created() {},
    methods: {
      async showDialog(row) {
        this.formLoading = true
        this.formData = { ...row }
        this.formLoading = false
        this.$nextTick(() => {
          this.$refs.UploadLargeFileFdfsPopupDetail.getParamsData({
            bizId: this.formData.id,
            bizCode: 'contractAttachment',
            isShow: true,
          })
          this.$refs['dataForm'].clearValidate()
        })
        this.dialogDetailVisible = true
      },
      // callBackRefresh(){
      //   this.$parent.fetchData()
      //   this.dialogDetailVisible = false
      // },
    },
  }
</script>

<style lang="scss" scoped></style>
