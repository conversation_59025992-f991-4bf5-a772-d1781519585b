<template>
  <el-dialog
    v-drag
    append-to-body
    center
    :close-on-click-modal="false"
    title="绩效考核详情"
    top="5vh"
    :visible.sync="dialogDetailVisible"
    width="1600px"
    @close="closeDialog"
  >
    <div
      style="
        display: flex;
        max-height: 75vh;
        overflow-y: scroll;
        overflow-x: hidden;
      "
    >
      <div style="flex: 1">
        <ele-table-editor
          ref="tableCol"
          v-model="localTableData"
          :columns="columns"
          :disabled="disabled"
          :is-show-add="false"
          :is-show-delete="false"
          :rules="rules"
          :table-attrs="tableAttrs"
          :table-on="tableOn"
        >
          <template #assessmentStandards="{ data }">
            <div v-html="formatRemark(data)"></div>
          </template>
        </ele-table-editor>
        <div
          class="subtotal"
          style="
            padding-top: 20px;
            overflow: hidden;
            font-size: 18px;
            color: #f00;
          "
        >
          <div style="float: left">得分小计</div>
          <div style="float: right">
            <span
              style="width: 160px; display: inline-block; text-align: center"
            >
              {{ total }}分
            </span>
            <span
              style="width: 160px; display: inline-block; text-align: center"
            >
              {{ totalFinal }}分
            </span>
          </div>
        </div>
        <h3 style="border-top: 1px solid #f2f2f2; line-height: 50px">
          评语及建议:
          <span style="font-size: 14px" v-html="formData.remark"></span>
        </h3>
        <!-- 审批 -->
        <VabApproveFlow
          v-if="isApproval"
          ref="ApprovalFlow"
          @callBackRefresh="callBackRefresh"
        />
      </div>
      <div v-if="formData.approvalResult !== '0'">
        <!-- 审批记录 -->
        <VabWorkFlowApproveRecDlg ref="workFlwApprovalRecDlg" />
      </div>
    </div>
    <div slot="footer" v-if="!isApproval" class="dialog-footer">
      <el-button type="danger" @click="dialogDetailVisible = false">
        关闭
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import {
    getPerformanceAppraisalItemById,
    updateByMap,
  } from '@/api/administrativeManagement/performanceAppraisal'
  import EleTableEditor from 'ele-table-editor'

  export default {
    components: {
      EleTableEditor,
    },
    props: {
      tableData: {
        type: Array,
        default() {
          return []
        },
      },
    },
    data() {
      return {
        dialogDetailVisible: false,
        showSupplier: false,
        formLoading: false,
        isApproval: false,
        formData: {},
        total: 0,
        totalFinal: 0,
        disabled: false,
        boo: false,
        localTableData: [], // 新增局部数据属性
        columns: [
          {
            prop: 'firstLevelIndicator',
            label: '一级指标',
            width: 150,
            align: 'center',
          },
          {
            prop: 'secondaryIndicators',
            label: '二级指标',
            width: 200,
            align: 'center',
          },
          {
            prop: 'assessmentStandards',
            label: '考核标准',
          },
          {
            prop: 'completionStatus',
            label: '完成情况(自评)',
            align: 'center',
            width: 160,
            content: {
              type: 'el-input-number',
              attrs: {
                placeholder: '分数',
                disabled: true,
                controls: false,
              },
            },
          },
          {
            prop: 'finalScore',
            label: '最终得分',
            width: 160,
            align: 'center',
            content: {
              type: 'el-input-number',
              attrs: {
                placeholder: '分数',
                disabled: false,
                controls: false,
              },
              change: (val, row) => {
                this.saveFinal(row)
              },
            },
          },
        ],
        rules: {},
        tableAttrs: {
          border: true,
          height: 450,
          spanMethod: this.objectSpanMethod,
        },
        // 已合并行的标记数组
        mergedRows: [],
        tableOn: {},
      }
    },
    created() {
      this.localTableData = this.tableData.map((item) => ({ ...item }))
    },
    methods: {
      objectSpanMethod({ row, rowIndex, columnIndex }) {
        if (columnIndex === 0) {
          // 如果当前行已经被合并过，则跳过
          if (this.mergedRows[rowIndex]) {
            return {
              rowspan: 0,
              colspan: 0,
            }
          }

          const firstLevelIndicator = row.firstLevelIndicator
          let rowspan = 1

          // 计算相同类别的连续行数，并标记这些行
          for (let i = rowIndex + 1; i < this.localTableData.length; i++) {
            if (
              this.localTableData[i].firstLevelIndicator === firstLevelIndicator
            ) {
              rowspan++
              this.mergedRows[i] = true // 标记该行已经被合并
            } else {
              break
            }
          }

          // 返回合并结果
          return rowspan > 1
            ? { rowspan, colspan: 1 }
            : { rowspan: 1, colspan: 1 }
        }

        // 默认返回不合并
        return {
          rowspan: 1,
          colspan: 1,
        }
      },
      async saveFinal(row) {
        if (row.finalScore <= row.max) {
          await updateByMap(row).then(() => {
            this.totalFinal = this.localTableData.reduce(
              (acc, item) => acc + Number(item.finalScore || 0),
              0
            )
          })
        } else {
          this.$message.error('数据错误！')
          row.finalScore = row.max
          console.log(row.finalScore, 237)
          await updateByMap(row).then(() => {
            this.totalFinal = this.localTableData.reduce(
              (acc, item) => acc + Number(item.finalScore || 0),
              0
            )
          })
        }
      },
      formatRemark(item) {
        let remark = item.assessmentStandards
        return remark ? remark.replace(/\n/g, '<br/>') : ''
      },
      async showApprovalFlowByParams(bizKey, row, paramsMap) {
        await this.showDialog(row)
        this.isApproval = true
        this.$nextTick(() => {
          this.$refs.ApprovalFlow.showApprovalFlowByParams(
            bizKey,
            row,
            paramsMap
          )
        })
      },
      callBackRefresh() {
        this.$parent.fetchData()
        this.dialogDetailVisible = false
      },
      async showDialog(row, edit) {
        this.isApproval = false
        this.formData = { ...row }
        this.localTableData = this.tableData.map((item) => ({ ...item }))
        await getPerformanceAppraisalItemById(this.formData.id).then(
          ({ result }) => {
            result.PerformanceAppraisalItems.forEach((item) => {
              this.localTableData.forEach((items) => {
                if (
                  item.type === items.firstLevelIndicator &&
                  item.typeSon === items.secondaryIndicators
                ) {
                  items.completionStatus = item.fraction
                  items.finalScore = item.fractionFinal
                  items.id = item.id
                }
              })
            })
            this.total = this.localTableData.reduce(
              (acc, item) => acc + Number(item.completionStatus || 0),
              0
            )
            this.totalFinal = this.localTableData.reduce(
              (acc, item) => acc + Number(item.finalScore || 0),
              0
            )
            // if (this.total > 100) {
            //   this.total = 100
            // }
            // if (this.totalFinal > 100) {
            //   this.totalFinal = 100
            // }
          }
        )
        this.dialogDetailVisible = true
        this.$nextTick(() => {
          if (this.formData.approvalResult !== '0') {
            this.$refs.workFlwApprovalRecDlg.getTaskProcessListByBizKey(
              this.formData.id
            )
          }
          if (edit === false) {
            this.columns[4].content[0].attrs.disabled = true
          } else {
            this.columns[4].content[0].attrs.disabled = false
          }
        })
      },
      closeDialog() {
        this.$emit('fetch-data')
        this.localTableData = []
      },
    },
  }
</script>

<style lang="scss" scoped></style>
