<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="80px"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item label="编号" prop="serialNumber">
            <el-input
              v-model="queryForm.serialNumber"
              clearable
              placeholder="请输入编号"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="被考核者" prop="createByName">
            <el-input
              v-model="queryForm.createByName"
              clearable
              placeholder="请输入被考核者"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="审批状态" prop="approvalResult">
            <el-select
              ref="selectFlow"
              v-model="queryForm.approvalResult"
              filterable
              placeholder="请选择审批状态"
              style="width: 200px"
            >
              <el-option option value="">所有状态</el-option>
              <el-option
                v-for="item in approvalResultNameList"
                :key="item.id"
                :label="item.approvalResultName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              icon="el-icon-search"
              native-type="submit"
              type="primary"
              @click="handleQuery"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-right"
              @click.native="resetFormPage"
            >
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
      <vab-query-form-left-panel>
        <el-button
          v-permissions="{ permission: ['staffApplication:add'] }"
          icon="el-icon-plus"
          type="primary"
          @click="handleAdd"
        >
          添加
        </el-button>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel>
        <el-button
          style="margin: 0 10px 10px 0 !important"
          type="primary"
          @click="clickFullScreen"
        >
          <vab-icon
            :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'"
          />
          表格全屏
        </el-button>
        <el-popover
          ref="popover"
          popper-class="custom-table-checkbox"
          trigger="hover"
        >
          <el-radio-group v-model="lineHeight">
            <el-radio label="medium">大</el-radio>
            <el-radio label="small">中</el-radio>
            <el-radio label="mini">小</el-radio>
          </el-radio-group>
          <template #reference>
            <el-button style="margin: 0 10px 10px 0 !important" type="primary">
              <vab-icon icon="line-height"/>
              表格尺寸
            </el-button>
          </template>
        </el-popover>
        <el-popover popper-class="custom-table-checkbox" trigger="hover">
          <el-checkbox-group v-model="checkList">
            <vab-draggable v-bind="dragOptions" :list="columns">
              <div v-for="(item, index) in columns" :key="item + index">
                <vab-icon icon="drag-drop-line"/>
                <el-checkbox
                  :disabled="item.disableCheck === true"
                  :label="item.label"
                >
                  {{ item.label }}
                </el-checkbox>
              </div>
            </vab-draggable>
          </el-checkbox-group>
          <template #reference>
            <el-button
              icon="el-icon-setting"
              style="margin: 0 0 10px 0 !important"
              type="primary"
            >
              可拖拽列设置
            </el-button>
          </template>
        </el-popover>
      </vab-query-form-right-panel>
    </vab-query-form>

    <el-table
      ref="tableSort"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      row-key="id"
      :size="lineHeight"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      @selection-change="setSelectRows"
    >
      <el-table-column align="center" type="selection" width="55"/>
      <el-table-column align="center" label="序号" width="60">
        <template #default="{ $index }">
          {{ getIndex($index) }}
        </template>
      </el-table-column>
      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        :align="item.center ? item.center : 'center'"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'"
      >
        <template #default="{ row }">
          <span v-if="item.label === '创建时间'">
            {{ row.createTime | dateformat('YYYY-MM-DD') }}
          </span>
          <span v-else-if="item.label === '审批状态'">
            <el-tag :type="fmtFlowType(row)">
              {{ row[item.prop] }}
            </el-tag>
          </span>
          <span v-else-if="item.label === '评价等级'">
            <el-tag :type="ratingType(row.totalFinal)">
              {{ ratingName(row.totalFinal) }}
            </el-tag>
          </span>
          <span v-else>
            {{ row[item.prop] }}
          </span>
        </template>
      </el-table-column>

      <el-table-column align="center" fixed="right" label="操作" width="320">
        <template #default="{ row }">
          <el-button
            v-permissions="{ permission: ['staffApplication:update'] }"
            :disabled="isDisabledEditFun(row)"
            icon="el-icon-edit"
            style="margin: 0 10px 0 0 !important"
            type="primary"
            @click="handleEdit(row)"
          >
            编辑
          </el-button>
          <el-button
            v-if="isAssignee(row)"
            icon="el-icon-edit-outline"
            style="margin: 0 10px 10px 0 !important"
            type="success"
            @click.native.prevent="showApprovalDlg(row)"
          >
            审批
          </el-button>
          <el-button
            icon="el-icon-view"
            style="margin: 0 10px 10px 0 !important"
            type="success"
            @click="handleDetail(row)"
          >
            详情
          </el-button>
          <el-dropdown>
            <el-button size="small" type="success">
              <i class="el-icon-more"/>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-if="isStartFlowFlag(row)"
                @click.native.prevent="startWorkFlow(row)"
              >
                <i class="el-icon-video-play"/>
                启动流程
              </el-dropdown-item>
              <el-dropdown-item
                v-if="isDeleteFlowFlag(row)"
                @click.native.prevent="deleteWorkFlow(row)"
              >
                <i class="el-icon-refresh-left"/>
                撤回流程
              </el-dropdown-item>
              <el-dropdown-item
                v-if="isDeleteFlag(row)"
                v-permissions="{ permission: ['staffApplication:del'] }"
                style="color: #fd5353"
                @click.native.prevent="handleDelete(row)"
              >
                <i class="el-icon-delete"/>
                删除
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <table-edit
      ref="edit"
      :table-data="tableData"
      @fetch-data="fetchData"
    />
    <table-detail
      ref="tableDetail"
      :table-data="tableData"
      @fetch-data="fetchData"
    />
    <!-- 流程启动动态审批条件处理 -->
    <VabStartFlowProcess ref="VabStartFlowProcess"/>
  </div>
</template>

<script>
import tableEdit from './components/performanceAppraisalEdit.vue'
import tableDetail from './components/performanceAppraisalDetail.vue'
import tableMix from '@/views/mixins/table'
import {
  deleteData,
  getDataListByPage,
} from '@/api/administrativeManagement/performanceAppraisal'
import {mapGetters} from 'vuex'
import {reportAddr} from '@/utils/constants'
import {getDictList} from '@/api/system/dict-api'
//ending
export default {
  name: 'StaffApplication',
  components: {
    tableDetail,
    tableEdit,
  },
  mixins: [tableMix],
  data() {
    return {
      columns: [
        {
          label: '编号',
          prop: 'serialNumber',
          width: '250',
        },
        {
          label: '被考核人',
          prop: 'createByName',
        },
        {
          label: '部门',
          prop: 'createDepartName',
          width: '200',
        },
        {
          label: '创建时间',
          prop: 'createTime',
          width: '160',
        },
        {
          label: '自己评分',
          prop: 'total',
          width: '200',
        },
        {
          label: '最终得分',
          prop: 'totalFinal',
          width: '200',
        },
        {
          label: '评价等级',
          prop: 'rating',
          width: '200',
        },
        {
          label: '审批状态',
          prop: 'approvalResultName',
          width: '100',
        },
      ],
      //搜索条件必备字段在这里
      queryForm: {
        serialNumber: '',
        createByName: '',
      },
      list: [],
      pageInfo: {},
      tableData: [],
      performanceAppraisal: []
    }
  },
  computed: {
    ...mapGetters({
      userId: 'user/userId',
      userName: 'user/userName',
      departList: 'acl/departList',
    }),
    finallyColumns() {
      return this.columns.filter((item) =>
        this.checkList.includes(item.label)
      )
    },
  },
  created() {
    this.initDefaultCheck()
    this.fetchData()
    this.getDictListByCodes()
  },
  methods: {
    ratingType(value) {
      if (value >= 0 && value <= 69) {
        return 'danger'
      } else if (value >= 70 && value <= 84) {
        return 'warning'
      } else if (value >= 70 && value <= 84) {
        return 'primary'
      } else {
        return 'success'
      }
    },
    ratingName(value) {
      if (value >= 0 && value <= 69) {
        return '不称职'
      } else if (value >= 70 && value <= 84) {
        return '合格'
      } else if (value >= 70 && value <= 84) {
        return '良好'
      } else {
        return '优秀'
      }
    },
    async getDictListByCodes() {
      await getDictList({dictCode: 'performanceAppraisal1'}).then((response) => {
        this.performanceAppraisal = response.result[0].children
      })
      for (let i = 0; i < this.performanceAppraisal.length; i++) {
        for (let j = 0; j < this.performanceAppraisal[i].children.length; j++) {
          const child = this.performanceAppraisal[i].children[j];
          const assessmentStandard = child.children.length > 0 ? child.children[0].remark : null;
          // console.log(391,child)
          if (child.dictName === '突出贡献满足条件（22分）') {
            this.tableData.push({
              firstLevelIndicator: child.parentDictName,
              secondaryIndicators: child.dictName,
              assessmentStandards: assessmentStandard,
              completionStatus: 0,
              finalScore: 0,
              max: (Number)(child.remark),
            });
          } else {
            this.tableData.push({
              firstLevelIndicator: child.parentDictName,
              secondaryIndicators: child.dictName,
              assessmentStandards: assessmentStandard,
              completionStatus: (Number)(child.remark),
              finalScore: (Number)(child.remark),
              max: (Number)(child.remark),
            });
          }

        }
      }
    },
    fetchData() {
      this.listLoading = true
      const queryForm = {
        ...this.queryForm,
        curPage: this.pageInfo.curPage,
        pageSize: this.pageInfo.pageSize,
      }
      getDataListByPage(queryForm).then((res) => {
        this.listLoading = false
        const {
          result: {total, records},
        } = res
        this.list = records
        this.pageInfo.total = Number(total)
      })


    },
    // //打开报表
    // showReport(row) {
    //   const params = '&id=' + row.id
    //   const url =
    //     reportAddr +
    //     '/ReportServer?reportlet=/oaNew/regularApplication/TXSQ.cpt' +
    //     params
    //   window.open(url)
    // },

    //添加
    handleAdd() {
      this.$refs['edit'].showDialog()
    },
    //编辑
    handleEdit(row) {
      this.$refs['edit'].showDialog(row)
    },
    //详情
    handleDetail(row) {

      this.$refs['tableDetail'].showDialog(row, false)
    },
    handleDelete(row) {
      if (row.id) {
        this.$baseConfirm(
          '你确定要删除当前绩效考核数据吗',
          null,
          async () => {
            deleteData({id: row.id}).then(() => {
              this.fetchData()
              this.$baseMessage(
                '删除成功!',
                'success',
                'vab-hey-message-success'
              )
            })
          }
        )
      }
    },
  },
}
</script>

<style lang="scss" scoped></style>
