<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="80px"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item label="所属部门" prop="createDepartName">
            <el-input
              v-model="queryForm.createDepartName"
              clearable
              placeholder="请输入部门名称"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="申请人" prop="createByName">
            <el-input
              v-model="queryForm.createByName"
              clearable
              placeholder="请输入申请人姓名"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item>
            <el-button
              icon="el-icon-search"
              native-type="submit"
              type="primary"
              @click="handleQuery"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-right"
              @click.native="resetForm('form')"
            >
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
      <vab-query-form-left-panel>
        <el-button
          v-permissions="{ permission: ['departTravel:add'] }"
          icon="el-icon-plus"
          type="primary"
          @click="handleAdd"
        >
          添加
        </el-button>
        <el-button
          :disabled="exportLoading"
          icon="el-icon-download"
          type="warning"
          @click.native="exportBtn('机关出差记录.xlsx')"
        >
          导出
        </el-button>
        <!--        <el-button v-permissions="{ permission: ['departTravel:del'] }" icon="el-icon-delete" type="danger"-->
        <!--          @click="delBatch">-->
        <!--          批删-->
        <!--        </el-button>-->
      </vab-query-form-left-panel>
      <vab-query-form-right-panel>
        <el-button
          style="margin: 0 10px 10px 0 !important"
          type="primary"
          @click="clickFullScreen"
        >
          <vab-icon
            :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'"
          />
          表格全屏
        </el-button>
        <el-popover
          ref="popover"
          popper-class="custom-table-checkbox"
          trigger="hover"
        >
          <el-radio-group v-model="lineHeight">
            <el-radio label="medium">大</el-radio>
            <el-radio label="small">中</el-radio>
            <el-radio label="mini">小</el-radio>
          </el-radio-group>
          <template #reference>
            <el-button style="margin: 0 10px 10px 0 !important" type="primary">
              <vab-icon icon="line-height" />
              表格尺寸
            </el-button>
          </template>
        </el-popover>
        <el-popover popper-class="custom-table-checkbox" trigger="hover">
          <el-checkbox-group v-model="checkList">
            <vab-draggable v-bind="dragOptions" :list="columns">
              <div v-for="(item, index) in columns" :key="item + index">
                <vab-icon icon="drag-drop-line" />
                <el-checkbox
                  :disabled="item.disableCheck === true"
                  :label="item.label"
                >
                  {{ item.label }}
                </el-checkbox>
              </div>
            </vab-draggable>
          </el-checkbox-group>
          <template #reference>
            <el-button
              icon="el-icon-setting"
              style="margin: 0 0 10px 0 !important"
              type="primary"
            >
              可拖拽列设置
            </el-button>
          </template>
        </el-popover>
      </vab-query-form-right-panel>
    </vab-query-form>

    <el-table
      ref="tableSort"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      row-key="id"
      :size="lineHeight"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      @selection-change="setSelectRows"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column align="center" label="序号" width="60">
        <template #default="{ $index }">
          {{ getIndex($index) }}
        </template>
      </el-table-column>
      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        :align="item.center ? item.center : 'center'"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'"
      >
        <template #default="{ row }">
          <span v-if="item.label === '开始时间'">
            {{ dateFormat(row.startTime) }}
          </span>
          <span v-else-if="item.label === '结束时间'">
            {{ dateFormat(row.endTime) }}
          </span>
          <span v-else-if="item.label === '审批状态'">
            <el-tag :type="fmtFlowType(row)">
              {{ row[item.prop] }}
            </el-tag>
          </span>
          <span v-else>{{ row[item.prop] }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" fixed="right" label="操作" width="320">
        <template #default="{ row }">
          <el-button
            v-permissions="{ permission: ['departTravel:update'] }"
            :disabled="isDisabledEditFun(row)"
            icon="el-icon-edit"
            style="margin: 0 10px 0 0 !important"
            type="primary"
            @click="handleEdit(row)"
          >
            编辑
          </el-button>
          <el-button
            v-if="isAssignee(row)"
            icon="el-icon-edit-outline"
            style="margin: 0 10px 10px 0 !important"
            type="success"
            @click.native.prevent="showApprovalDlg(row)"
          >
            审批
          </el-button>
          <el-button
            icon="el-icon-view"
            style="margin: 0 10px 10px 0 !important"
            type="success"
            @click="handleDetail(row)"
          >
            详情
          </el-button>
          <el-dropdown>
            <el-button size="small" type="success">
              <i class="el-icon-more" />
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-if="isStartFlowFlag(row)"
                @click.native.prevent="startWorkFlow(row)"
              >
                <i class="el-icon-video-play" />
                启动流程
              </el-dropdown-item>
              <el-dropdown-item
                v-if="isDeleteFlowFlag(row)"
                @click.native.prevent="deleteWorkFlow(row)"
              >
                <i class="el-icon-refresh-left" />
                撤回流程
              </el-dropdown-item>
              <el-dropdown-item @click.native.prevent="showReport(row)">
                <i class="el-icon-video-play" />
                查看报表
              </el-dropdown-item>
              <el-dropdown-item
                v-if="isDeleteFlag(row)"
                v-permissions="{ permission: ['departTravel:del'] }"
                style="color: #fd5353"
                @click.native.prevent="handleDelete(row)"
              >
                <i class="el-icon-delete" />
                删除
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <table-edit ref="edit" :tree-data="treeData" @fetch-data="fetchData" />
    <!-- <type ref="type" @callBack="showAdd" /> -->
    <travelDetail ref="tableDetail" />
    <!-- 部门选择
    <VabWorkFlowDepart ref="VabWorkFlowDepart" /> -->

    <!-- 流程启动动态审批条件处理 -->
    <VabStartFlowProcess ref="VabStartFlowProcess" />

    <VabTableExport ref="tableExport" @refreshPage="resetFormPage" />
  </div>
</template>

<script>
  import {
    deleteDataEvection,
    getDataListByPageEvection,
    exportData,
  } from '@/api/administrativeManagement/evection'
  import TableEdit from './components/travelDepartEdit.vue'
  import travelDetail from './components/travelDepartDetail.vue'
  import tableMix from '@/views/mixins/table'
  import { parseTime } from '@/utils'
  import { reportAddr } from '@/utils/constants'
  //ending
  export default {
    name: 'DepartTravel',
    components: {
      TableEdit,
      travelDetail,
    },
    mixins: [tableMix],
    data() {
      return {
        // formMinHeight:1,
        // hasPage:false,
        columns: [
          {
            label: '单号',
            prop: 'serialNumber',
            width: '240',
          },
          {
            label: '所属部门',
            prop: 'departName',
            width: '160',
          },
          // {
          //   label: '项目名称',
          //   prop: 'projectName',
          //   width: '140',
          // },
          {
            label: '申请人',
            prop: 'createByName',
          },
          {
            label: '同行人',
            prop: 'peerName',
            width: '160',
          },
          {
            label: '出差事由',
            prop: 'businessTravelReason',
            width: '320',
          },
          {
            label: '出发城市',
            prop: 'departureCity',
            width: '200',
          },
          {
            label: '目的地城市',
            prop: 'destinationCity',
            width: '200',
          },
          {
            label: '出行方式',
            prop: 'tavelModeName',
          },
          {
            label: '住宿情况',
            prop: 'accommodationName',
          },
          {
            label: '开始时间',
            prop: 'startTime',
            width: '150',
          },
          {
            label: '结束时间',
            prop: 'endTime',
            width: '150',
          },
          {
            label: '审批状态',
            prop: 'approvalResultName',
            width: '100',
          },
        ],
        treeData: [],
        //搜索条件必备字段
        queryForm: {
          createDepartName: '',
          createByName: '',
          // startTime: '',
          // endTime: '',
        },
        departData: [],
        defaultProps: {
          children: 'children',
          label: 'label',
        },
        // departTreeData: [],
        //ending
      }
    },
    computed: {},
    created() {
      this.fetchData()
      //      this.loadDepartData() //搜索条件所属项目拿数据的方法
    },
    methods: {
      exportBtn(excelName) {
        this.queryForm.excelName = excelName
        this.$refs['tableExport'].showDialog(this.columns)
      },
      exportData(columns) {
        this.exportLoading = true
        exportData({ ...this.queryForm, ...columns }).then((response) => {
          const link = document.createElement('a')
          link.download = this.queryForm.excelName
          link.href = window.URL.createObjectURL(new Blob([response]))
          document.body.appendChild(link)
          link.click()
          link.download = ''
          document.body.removeChild(link)
          URL.revokeObjectURL(response)
        })
        this.exportLoading = false
      },

      showReport(row) {
        const params = '&id=' + row.id
        let url = ''
        if (row.tavelModeName === '私车公用') {
          url =
            reportAddr +
            '/ReportServer?reportlet=/oaNew/travel/travelDepart.cpt' +
            params
        } else {
          url +=
            reportAddr +
            '/ReportServer?reportlet=/oaNew/travel/travelDepartExct.cpt' +
            params
        }
        window.open(url)
      },
      // getDepartList() {
      //   getDepartList({}).then((response) => {
      //     this.departTreeData = response.result
      //   })
      // },
      dateFormat(date) {
        if (!date) {
          return ''
        }
        return parseTime(new Date(date), '{y}-{m}-{d} {h}:{i}')
      },
      async fetchData() {
        //async 异步数据处理方法
        this.queryForm.curPage = this.pageInfo.curPage
        this.queryForm.pageSize = this.pageInfo.pageSize
        this.queryForm.type = 'depart'
        this.listLoading = true
        const { result } = await getDataListByPageEvection(this.queryForm)
        this.list = result.records //把请求到的数据赋值给表格
        this.pageInfo.total = Number(result.total)
        this.listLoading = false
      },
      handleDelete(row) {
        if (row.id) {
          this.$baseConfirm('你确定要删除当前数据吗', null, async () => {
            deleteDataEvection({ id: row.id }).then(() => {
              this.fetchData()
              this.$baseMessage(
                '删除成功!',
                'success',
                'vab-hey-message-success'
              )
            })
          })
        }
      },
      // delBatch() {
      //   if (this.selectRows.length == 0) {
      //     this.$baseMessage(
      //       '请最少选中一条记录!',
      //       'error',
      //       'vab-hey-message-error'
      //     )
      //   } else {
      //     const ids = this.selectRows.map((item) => item.id).join(',')
      //     this.$baseConfirm('你确定要批量删除数据吗', null, async () => {
      //       deleteDataEvection({ id: ids })
      //         .then((response) => {
      //           this.pageInfo.curPage = 1
      //           this.fetchData()
      //           this.$baseMessage(
      //             '批量删除成功！',
      //             'success',
      //             'vab-hey-message-success'
      //           )
      //         })
      //         .catch((err) => {
      //           this.$baseMessage(
      //             '批量删除失败！',
      //             'error',
      //             'vab-hey-message-error'
      //           )
      //         })
      //     })
      //   }
      // },
      handleAdd() {
        this.$refs['edit'].showEdit() //看到这个之后，要去找showEdit 方法
      },
      handleEdit(row) {
        this.$refs['edit'].showEdit('', row)
      },
      showAdd(type) {
        this.$refs['edit'].showEdit(type)
      },
      handleDetail(row) {
        this.$refs['tableDetail'].showDialog(row)
      },
      // 搜索条件必备下面的方法
      resetFormPage() {
        // this.createDepart = ''
        this.queryForm.departName = ''
        this.queryForm.createDepart = ''
        this.resetForm('form')
      },
      //      async loadDepartData() {
      //        await getDepartList({}).then((response) => {
      //          const json = JSON.parse(
      //            JSON.stringify(response.result).replace(/departName/g, 'name')
      //          )
      //          this.departData = formatEleDropDownTree(json, '-1')
      //        })
      //      },
      // departTreeHide() {
      //   const selectDatas = this.$refs.departTree.getCheckedNodes()
      //   if (selectDatas.length > 0) {
      //     this.queryForm.departName = selectDatas[0].label
      //     this.queryForm.createDepart = this.createDepart
      //   }
      // },
      // handleNodeClick(data, checked, node) {
      //   if (this.createDepart) {
      //     if (checked) {
      //       this.$refs.departTree.setCheckedKeys([])
      //       this.$refs.departTree.setCheckedKeys([data.id])
      //     } else {
      //       this.createDepart = ''
      //       this.$refs.departTree.setCheckedKeys([])
      //     }
      //   } else {
      //     this.createDepart = data.id
      //     this.$refs.departTree.setCheckedKeys([data.id])
      //   }
      // },
      // filterNode(value, data) {
      //   if (!value) return true
      //   return data.label.indexOf(value) !== -1
      // },
      //ending
      // async workFlowData() {
      //   const item = this.flowData
      //   console.log(item)
      //   return {
      //     menuCode: this.$route.name,
      //     createDepart: item.createDepart,
      //     variables: {
      //       //业主数据主键
      //       bizId: item.id,
      //       //申请人主键
      //       applicantId: item.registrant,
      //       //申请人名字
      //       applicant: item.proposerName,
      //       //所属部门
      //       createDepart: item.createDepart,
      //       //所属部门名称
      //       departName: item.departName,
      //       //审批通知名称
      //       fullName: item.proposerName + '提交的' + this.$route.meta.title,
      //       //小程序审批通知名称
      //       weChatFullName: item.proposerName + '-' + this.$route.meta.title
      //     },
      //   }
      // },
      getIndex(index) {
        return (this.pageInfo.curPage - 1) * this.pageInfo.pageSize + index + 1
      },
    },
  }
</script>

<style lang="scss" scoped></style>
