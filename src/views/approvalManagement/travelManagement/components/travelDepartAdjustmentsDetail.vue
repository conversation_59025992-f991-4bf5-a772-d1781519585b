<template>
  <el-dialog
    v-drag
    append-to-body
    center
    :close-on-click-modal="false"
    title="出差申请详情"
    top="5vh"
    :visible.sync="dialogDetailVisible"
    width="1400px"
  >
    <div
      style="
        display: flex;
        max-height: 75vh;
        overflow-y: scroll;
        overflow-x: hidden;
      "
    >
      <div style="flex: 1">
        <el-descriptions border class="margin-top" :column="3" size="medium">
          <el-descriptions-item>
            <template slot="label">单号</template>
            {{ formData.serialNumber }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">申请人</template>
            {{ formData.createByName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">申请日期</template>
            {{ formData.startTime | dateformat('YYYY-MM-DD') }}
          </el-descriptions-item>
          <!-- <el-descriptions-item>
            <template slot="label">申请日期</template>
            {{ dateFormat(formData.applicationDate) }}
          </el-descriptions-item> -->
          <el-descriptions-item :span="3">
            <template slot="label">出差事由</template>
            {{ formData.businessTravelReason }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">同行人</template>
            {{ formData.oldPeerName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">登记部门</template>
            {{ formData.createDepartName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">费用所属部门</template>
            {{ formData.departName }}
          </el-descriptions-item>

          <el-descriptions-item>
            <template slot="label">出发城市</template>
            {{ formData.oldDepartureCity }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">目的城市</template>
            {{ formData.oldDestinationCity }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">出行方式</template>
            {{ formData.oldTavelModeName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">开始日期</template>
            {{ formData.oldStartTime | dateformat('YYYY-MM-DD HH:mm') }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">结束日期</template>
            {{ formData.oldEndTime | dateformat('YYYY-MM-DD HH:mm') }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">时长</template>
            {{ formData.oldDuration }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">备注</template>
            {{ formData.oldRemarks }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">住宿情况</template>
            {{ formData.oldAccommodationName }}
          </el-descriptions-item>

          <el-descriptions-item>
            <template slot="label">变更后出发城市</template>
            {{ formData.departureCity }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">变更后目的城市</template>
            {{ formData.destinationCity }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">变更后出行方式</template>
            {{ formData.tavelModeName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">变更后开始日期</template>
            {{ formData.startTime | dateformat('YYYY-MM-DD HH:mm') }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">变更后结束日期</template>
            {{ formData.endTime | dateformat('YYYY-MM-DD HH:mm') }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">变更后时长</template>
            {{ formData.duration }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">变更后同行人</template>
            {{ formData.peerName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">变更后住宿情况</template>
            {{ formData.accommodationName }}
          </el-descriptions-item>

          <el-descriptions-item v-if="formData.tavelModeName === '私车公用'">
            <template slot="label">出发前公里数</template>
            {{ formData.startForKm }}
          </el-descriptions-item>
          <el-descriptions-item v-if="formData.tavelModeName === '私车公用'">
            <template slot="label">返程后公里数</template>
            {{ formData.afterForKm }}
          </el-descriptions-item>
          <el-descriptions-item v-if="formData.tavelModeName === '私车公用'">
            <template slot="label">小记公里数</template>
            {{ formData.totalKm }}
          </el-descriptions-item>

          <el-descriptions-item v-if="formData.tavelModeName === '私车公用'">
            <template slot="label">出发前照片：</template>
            <el-image
              :preview-src-list="[formData.startForImg]"
              :src="formData.startForImg"
              style="width: 100px; height: 100px"
            >
              <div
                class="image-slot"
                style="
                  width: 100px;
                  height: 100px;
                  background: #cccccc;
                  line-height: 100px;
                  text-align: center;
                "
                slot="error"
              >
                暂无图片
              </div>
            </el-image>
          </el-descriptions-item>
          <el-descriptions-item v-if="formData.tavelModeName === '私车公用'">
            <template slot="label">目的地照片：</template>
            <el-image
              :preview-src-list="[formData.destinationImg]"
              :src="formData.destinationImg"
              style="width: 100px; height: 100px"
            >
              <div
                class="image-slot"
                style="
                  width: 100px;
                  height: 100px;
                  background: #cccccc;
                  line-height: 100px;
                  text-align: center;
                "
                slot="error"
              >
                暂无图片
              </div>
            </el-image>
          </el-descriptions-item>
          <el-descriptions-item v-if="formData.tavelModeName === '私车公用'">
            <template slot="label">返程后照片：</template>
            <el-image
              :preview-src-list="[formData.afterForImg]"
              :src="formData.afterForImg"
              style="width: 100px; height: 100px"
            >
              <div
                class="image-slot"
                style="
                  width: 100px;
                  height: 100px;
                  background: #cccccc;
                  line-height: 100px;
                  text-align: center;
                "
                slot="error"
              >
                暂无图片
              </div>
            </el-image>
          </el-descriptions-item>

          <el-descriptions-item>
            <template slot="label">变更后备注</template>
            {{ formData.remarks }}
          </el-descriptions-item>
        </el-descriptions>
        <!-- 审批 -->
        <VabApproveFlow
          v-if="isApproval"
          ref="ApprovalFlow"
          @callBackRefresh="callBackRefresh"
        />
      </div>
      <div v-if="formData.approvalResult !== '0' && showFlow">
        <!-- 审批记录 -->
        <VabWorkFlowApproveRecDlg ref="workFlwApprovalRecDlg" />
      </div>
    </div>

    <div slot="footer" v-if="!isApproval" class="dialog-footer">
      <el-button type="danger" @click="dialogDetailVisible = false">
        关闭
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
  import { parseTime } from '@/utils'
  export default {
    props: {
      // 是否显示审批流程
      showFlow: {
        type: Boolean,
        default: true,
      },
    },
    data() {
      return {
        dialogDetailVisible: false,
        formData: {},
        isApproval: false,
      }
    },
    methods: {
      dateFormat(date) {
        if (!date) {
          return ''
        }
        return parseTime(new Date(date), '{y}-{m}-{d} {h}:{i}')
      },
      showApprovalFlowByParams(bizKey, row, paramsMap) {
        this.showDialog(row)
        this.isApproval = true
        this.$nextTick(() => {
          this.$refs.ApprovalFlow.showApprovalFlowByParams(
            bizKey,
            row,
            paramsMap
          )
        })
      },
      callBackRefresh() {
        this.$parent.fetchData()
        this.dialogDetailVisible = false
      },
      async showDialog(row) {
        this.isApproval = false
        this.formData = { ...row }
        this.dialogDetailVisible = true

        if (this.formData.approvalResult !== '0' && this.showFlow) {
          this.$nextTick(() => {
            this.$refs.workFlwApprovalRecDlg.getTaskProcessListByBizKey(
              this.formData.id
            )
          })
        }
      },
    },
  }
</script>

<style lang="scss" scoped></style>
