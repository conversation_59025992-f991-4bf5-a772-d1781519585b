<template>
  <el-dialog
    v-drag
    append-to-body
    :before-close="closeBtn"
    center
    :close-on-click-modal="false"
    :title="pageTitle"
    :visible.sync="dialogDetailVisible"
    width="750px"
  >
    <el-form
      ref="dataForm"
      v-loading="formLoading"
      :inline="true"
      label-position="right"
      label-width="120px"
      :model="formData"
      :rules="rules"
    >
      <el-form-item label="排序" prop="sort">
        <el-input-number
          v-model.number="formData.sort"
          controls-position="right"
          :max="99999"
          :min="0"
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="请假类型名称" prop="dictName">
        <el-input v-model="formData.dictName" style="width: 200px" />
      </el-form-item>
      <el-form-item label="请假类型说明" prop="remark">
        <el-input
          v-model="formData.remark"
          style="width: 530px"
          type="textarea"
        />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer" style="text-align: center">
      <el-button type="danger" @click="closeBtn">关闭</el-button>
      <el-button type="primary" @click="saveBtn">保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { genUUID } from '@/utils/th_utils'
  import { saveData } from '@/api/system/dict-api'
  export default {
    components: {},
    props: {
      parentId: {
        type: String,
        default() {
          return ''
        },
      },
      total: {
        type: Number,
        default() {
          return 0
        },
      },
    },
    data() {
      return {
        dialogDetailVisible: false,
        formLoading: false,
        defaultProps: {
          children: 'children',
          label: 'label',
        },
        formData: {},
        rules: {
          dictName: [
            { required: true, message: '请假类型名称为必填', trigger: 'blur' },
          ],
        },
      }
    },
    computed: {
      pageTitle() {
        return this.formData.isAdd ? '新增' : '编辑'
      },
    },
    methods: {
      async showDialog(row) {
        this.formData = { ...row }
        if (this.formData.isAdd) {
          this.init()
        }
        this.dialogDetailVisible = true
      },
      async saveBtn() {
        if (this.formData.isAdd) {
          this.formData.dictCode =
            'leaveType-' + String(Number(this.total) + 1).padStart(4, '0')
        }
        this.$refs.dataForm.validate((valid) => {
          if (valid) {
            this.formData.dictFullName = this.formData.dictName
            saveData(this.formData)
              .then((res) => {
                if (res.code === 200) {
                  this.$baseMessage(
                    this.formData.isAdd ? '新增成功！' : '修改成功!',
                    'success',
                    'vab-hey-message-success'
                  )
                  this.$emit('refreshPage')
                  this.formLoading = false
                  this.dialogDetailVisible = false
                }
              })
              .catch(() => {
                this.$baseMessage(
                  this.formData.isAdd ? '新增失败！' : '修改失败!',
                  'error',
                  'vab-hey-message-error'
                )
              })
          }
        })
      },
      closeBtn() {
        this.formLoading = false
        this.dialogDetailVisible = false
        this.$refs.dataForm.resetFields()
        this.init()
      },
      init() {
        this.formData = {
          id: genUUID(),
          dictName: '',
          dictFullName: '',
          parentId: this.parentId,
          dictCode: '',
          sort: this.total + 1,
          remark: '',
          isAdd: true,
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  .image-upload {
    position: absolute;
    right: 21px;
    bottom: 93px;
    width: 536px;
    height: 160px;
    background: #fff;
    border-left: 1px solid #bbb;
  }
</style>
