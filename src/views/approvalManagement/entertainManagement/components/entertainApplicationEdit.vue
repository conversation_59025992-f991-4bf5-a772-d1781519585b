<template>
  <el-dialog
    v-drag
    append-to-body
    center
    :close-on-click-modal="false"
    :title="title"
    top="5vh"
    :visible.sync="dialogFormVisible"
    width="1400px"
    @close="close"
  >
    <el-form
      ref="dataForm"
      v-loading="formloading"
      :inline="true"
      label-position="right"
      label-width="130px"
      :model="formData"
      :rules="rules"
    >
      <table v-loading="formloading" class="form-table">
        <tr class="title">
          <td colspan="3">
            {{ formData.type === 'project' ? '项目招待申请' : '机关招待申请' }}
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="单号" prop="serialNumber">
              <el-input
                v-model="formData.serialNumber"
                disabled
                placeholder="自动生成"
                readonly
                style="width: 280px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="申请人" prop="createByName">
              <el-input
                v-model="formData.createByName"
                disabled
                readonly
                style="width: 280px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="申请日期" prop="applicationDate">
              <el-date-picker
                v-model="formData.applicationDate"
                format="yyyy-MM-dd"
                placeholder="选择时间"
                style="width: 280px"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label="招待事由" prop="reasons">
              <el-input
                v-model="formData.reasons"
                maxlength="1000"
                placeholder="请输入内容(不超过1000字)"
                style="width: 1180px"
                type="textarea"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="招待形式" prop="receptionType">
              <el-select
                v-model="formData.receptionType"
                filterable
                placeholder="请选择招待形式"
                style="width: 250px"
              >
                <el-option
                  v-for="item in receptionTypeList"
                  :key="item.id"
                  :label="item.dictName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </td>
          <td>
            <el-form-item label="招待等级" prop="receptionLevel">
              <el-select
                v-model="formData.receptionLevel"
                :disabled="
                  formData.receptionType ==
                    '6f6be1f1-3823-454a-8fa2-84b09bd19994' ||
                  formData.receptionType == ''
                "
                filterable
                placeholder="请选择招待等级"
                style="width: 250px"
              >
                <el-option
                  v-for="item in receptionLevelList"
                  :key="item.id"
                  :label="item.dictName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </td>
          <td>
            <el-form-item label="招待金额(元)" prop="entertainMoney">
              <el-input
                v-model="formData.entertainMoney"
                placeholder="请输入金额(小数位不超过2位)"
                style="width: 280px"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="招待人数" prop="entertainNum">
              <el-input
                v-model="formData.entertainNum"
                multiple
                placeholder="请输入招待人数"
                style="width: 280px"
                @input="handleEntertainNum"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="接待人数" prop="receptionNum">
              <el-input
                v-model="formData.receptionNum"
                multiple
                placeholder="请输入接待人数"
                style="width: 280px"
                @input="handleReceptionNum"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item
              v-if="formData.type == 'depart'"
              label="费用所属部门"
              prop="depart"
              required
            >
              <t-form-tree
                v-model="formData.depart"
                :default-props="{ children: 'children', label: 'departName' }"
                :disabled="!formData.isAdd"
                :show-top="false"
                style="width: 280px"
                :tree-data="departTreeData"
              />
            </el-form-item>
            <el-form-item
              v-if="formData.type == 'project'"
              label="费用所属项目"
              prop="projectId"
              required
            >
              <el-input
                v-model="formData.projectName"
                :disabled="!formData.isAdd"
                filterable
                placeholder="请选择"
                style="width: 280px"
                @click.native="selectContractProject"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label="备注" prop="remarks">
              <el-input
                v-model="formData.remarks"
                maxlength="1000"
                placeholder="请输入内容(不超过1000字)"
                style="width: 1180px"
                type="textarea"
              />
            </el-form-item>
          </td>
        </tr>
      </table>
    </el-form>
    <!-- 附件上传 -->
    <UploadLargeFileFdfsPopup ref="UploadLargeFileFdfsPopup" />
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>
    <ProjectListPopup
      ref="projectListPopupRef"
      @getProjectInfo="getProjectInfo"
    />
  </el-dialog>
</template>

<script>
  import { saveDataEntertainApplication } from '@/api/administrativeManagement/entertainApplication-api'
  import UploadLargeFileFdfsPopup from '@/views/common/UploadLargeFileFdfsPopup.vue'
  import {
    getUploadFilesByPage,
    deleteFile,
    downloadFile,
  } from '@/api/system/uploadFile-api'
  import { fileAddr } from '@/utils/constants'
  import { listToTree } from '@/utils/util.js'
  import { getDictList } from '@/api/system/dict-api'
  import { getDepartList } from '@/api/system/depart-api'
  import { genUUID, parseTime } from '@/utils/th_utils.js'
  import { mapGetters } from 'vuex'
  import tableMix from '@/views/mixins/baseTable'
  import ProjectListPopup from '@/views/common/ProjectListPopup.vue'

  export default {
    name: 'TableEdit',
    components: {
      ProjectListPopup,
      UploadLargeFileFdfsPopup,
    },
    mixins: [tableMix],
    props: {},
    data() {
      const validateReg = (rule, value, callback) => {
        const reg = /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/ //任意正整数，正小数（小数位不超过2位）
        if (value && !reg.test(value)) {
          callback(new Error('金额格式不对'))
        } else {
          callback()
        }
      }
      const departValidateReg = (rule, value, callback) => {
        if (
          this.formData.type == 'depart' &&
          value == '' &&
          this.formData.receptionType !== 'a876de34-44f7-4fc5-bca7-f971c2a51cd9'
        ) {
          callback(new Error('机关招待如选择外请需说明理由'))
        } else {
          callback()
        }
      }
      return {
        title: '',
        dialogFormVisible: false,
        formloading: false,
        formData: {},
        applicationDate: '',
        selectedTavelModeName: [],
        selectedAccommodation: [],
        receptionTypeList: [],
        receptionLevelList: [],
        projectDataList: [],
        departTreeData: [],
        defaultProps: {
          children: 'children',
          label: 'label',
        },
        rules: {
          entertainMoney: [
            { required: true, message: '招待金额为必填', trigger: 'blur' },
            { validator: validateReg, trigger: 'change' },
          ],
          reasons: [
            { required: true, message: '招待事由为必填', trigger: 'blur' },
          ],
          receptionNum: [
            { required: true, message: '接待人数为必填', trigger: 'blur' },
          ],
          entertainNum: [
            { required: true, message: '招待人数为必填', trigger: 'blur' },
          ],
          projectId: [
            { required: true, message: '费用所属项目为必填', trigger: 'blur' },
          ],
          depart: [
            { required: true, message: '费用所属部门为必填', trigger: 'blur' },
          ],
          receptionType: [
            { required: true, message: '招待形式为必填', trigger: 'change' },
          ],
          remarks: [{ validator: departValidateReg, trigger: 'change' }],
        },
        duration: '',
      }
    },
    computed: {
      ...mapGetters({
        userId: 'user/userId',
        userName: 'user/userName',
        departList: 'acl/departList',
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.getDictDetailsByCode()
      this.getDepartList()
    },
    methods: {
      handleEntertainNum(value) {
        this.formData.entertainNum = value.replace(/\D/g, '')
      },
      handleReceptionNum(value) {
        this.formData.receptionNum = value.replace(/\D/g, '')
      },
      selectContractProject() {
        this.$refs.projectListPopupRef.showDialog({
          selectIds: this.formData.projectId,
          projectName: this.formData.projectName
        })
      },
      getDepartList() {
        getDepartList({}).then((response) => {
          this.departTreeData = response.result
        })
      },
      async getProjectInfo(data) {
        if (data.length > 0) {
          const dataObj = data[0]
          const {
            id,
            projectName,
            serialNumber,
            depart,
            departName,
            departCode,
          } = dataObj
          this.formData = {
            ...this.formData,
            projectId: id,
            projectName,
            departCode,
            projectSerialNumber: serialNumber,
            depart,
            departName: departName,
          }
          this.$refs.dataForm.clearValidate('projectName')
        }
      },
      getDictDetailsByCode() {
        this.receptionTypeList = []
        this.receptionLevelList = []
        getDictList({ dictCode: 'receptionType' }).then((response) => {
          this.receptionTypeList = response.result[0].children
        })
        getDictList({ dictCode: 'receptionLevel' }).then((response) => {
          this.receptionLevelList = response.result[0].children
        })
      },
      showEdit(type, row) {
        if (!row) {
          this.title = '新增招待申请'
          this.dialogStatus = 'create'
          this.initForm()
          this.formData.type = type
        } else {
          this.title = '编辑招待申请'
          this.dialogStatus = 'update'
          this.formData = Object.assign({ isAdd: false }, row)
        }
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs.UploadLargeFileFdfsPopup.getParamsData({
            bizId: this.formData.id,
            bizCode: 'entertainApplication',
          })
        })
        // this.$parent.$refs.UploadLargeFileFdfsPopup.headRun()
        this.$nextTick(() => {
          this.$refs['dataForm'].clearValidate()
        })
      },
      projectTreeShow() {
        if (!this.formData.isAdd && this.formData.projectId) {
          this.$nextTick(() => {
            this.$refs.tree.setCheckedKeys([this.formData.projectId])
          })
        }
      },
      projectTreeHide() {
        const selectDatas = this.$refs.tree.getCheckedNodes()
        if (selectDatas.length > 0) {
          this.formData.projectName = selectDatas[0].label
          this.formData.projectId = selectDatas[0].id
        } else {
          this.formData.projectName = ''
          this.formData.projectId = ''
        }
      },
      projectChange(val) {
        let project = this.projectDataList.find((item) => {
          return item.id == val
        })
        this.formData.projectCode = project.projectCode
        this.formData.departCode = project.departCode
      },
      projectNodeClick(data, checked, node) {
        if (this.formData.projectId) {
          if (checked) {
            this.$refs.tree.setCheckedKeys([])
            this.$refs.tree.setCheckedKeys([data.id])
          } else {
            this.formData.projectId = ''
            this.$refs.tree.setCheckedKeys([])
          }
        } else {
          this.formData.projectId = data.id
          this.$refs.tree.setCheckedKeys([data.id])
        }
      },
      // 初始化表单
      initForm() {
        this.formData = {
          id: genUUID(),
          serialNumber: '', //单号
          reasons: '', //招待原因
          updateTime: '',
          sort: '',
          entertainMoney: '', //请客金额
          createBy: '', //创建人ID
          createByName: this.userName,
          viewUser: this.userId, //申请人名字
          createTime: '',
          updateBy: '',
          entertainNum: '', //招待人数
          receptionNum: '', //接待人数
          receptionLevel: '', //接待等级
          receptionType: '', //接待形式
          createDepart: this.departList[0].id,
          createDepartName: this.departList[0].departName,
          serialNumberTable:'entertain_application',
          depart: '', //部门id
          departCode: '',
          projectName: '', //项目名字
          projectId: '', //费用所属项目
          departName: '', //部门名字
          applicationDate: parseTime(new Date()), //申请日期
          remarks: '',
          operationCode: '',
          isAdd: true,
        }
      },
      save() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            if (this.formData.type == 'depart') {
              this.formData.departCode = this.departTreeData.find((item) => {
                return item.id === this.formData.depart
              }).departCode
              this.formData.operationCode = 'JGZD'
            } else if (this.formData.type == 'project') {
              this.formData.operationCode = 'XMZD'
            }
            // return
            if (!this.formData.isAdd){
              delete this.formData.serialNumber
            }
            saveDataEntertainApplication(this.formData)
              .then((response) => {
                this.$baseMessage(
                  this.formData.isAdd ? '新增成功！' : '修改成功!',
                  'success',
                  'vab-hey-message-success'
                )
                this.close()
                this.$emit('fetch-data')
              })
              .catch((err) => {
                this.$baseMessage(
                  this.formData.isAdd ? '新增失败！' : '修改失败!',
                  'error',
                  'vab-hey-message-error'
                )
              })
          }
        })
      },
      close() {
        this.$refs['dataForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
      },
      // 附件
    },
  }
</script>

<style lang="scss" scoped></style>
