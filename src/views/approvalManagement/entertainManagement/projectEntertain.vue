<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="80px"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item label="所属项目" prop="projectName">
            <el-input
              v-model="queryForm.projectName"
              clearable
              placeholder="请输入申请项目"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="申请人" prop="applicantName">
            <el-input
              v-model="queryForm.createByName"
              clearable
              placeholder="请输入申请人姓名"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="审批状态" prop="approvalResult">
            <el-select
              ref="selectFlow"
              v-model="queryForm.approvalResult"
              placeholder="请选审批状态"
            >
              <el-option option value="">所有状态</el-option>
              <el-option
                v-for="item in approvalResultNameList"
                :key="item.id"
                :label="item.approvalResultName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              icon="el-icon-search"
              native-type="submit"
              type="primary"
              @click="handleQuery"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-right"
              @click.native="resetFormPage"
            >
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
      <vab-query-form-left-panel>
        <el-button
          v-permissions="{ permission: ['projectEntertain:add'] }"
          icon="el-icon-plus"
          type="primary"
          @click="showAdd"
        >
          添加
        </el-button>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel>
        <el-button
          style="margin: 0 10px 10px 0 !important"
          type="primary"
          @click="clickFullScreen"
        >
          <vab-icon
            :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'"
          />
          表格全屏
        </el-button>
        <el-popover
          ref="popover"
          popper-class="custom-table-checkbox"
          trigger="hover"
        >
          <el-radio-group v-model="lineHeight">
            <el-radio label="medium">大</el-radio>
            <el-radio label="small">中</el-radio>
            <el-radio label="mini">小</el-radio>
          </el-radio-group>
          <template #reference>
            <el-button style="margin: 0 10px 10px 0 !important" type="primary">
              <vab-icon icon="line-height" />
              表格尺寸
            </el-button>
          </template>
        </el-popover>
        <el-popover popper-class="custom-table-checkbox" trigger="hover">
          <el-checkbox-group v-model="checkList">
            <vab-draggable v-bind="dragOptions" :list="columns">
              <div v-for="(item, index) in columns" :key="item + index">
                <vab-icon icon="drag-drop-line" />
                <el-checkbox
                  :disabled="item.disableCheck === true"
                  :label="item.label"
                >
                  {{ item.label }}
                </el-checkbox>
              </div>
            </vab-draggable>
          </el-checkbox-group>
          <template #reference>
            <el-button
              icon="el-icon-setting"
              style="margin: 0 0 10px 0 !important"
              type="primary"
            >
              可拖拽列设置
            </el-button>
          </template>
        </el-popover>
      </vab-query-form-right-panel>
    </vab-query-form>

    <el-table
      ref="tableSort"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      row-key="id"
      :size="lineHeight"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      @selection-change="setSelectRows"
    >
      <el-table-column
        align="center"
        :selectable="selectableFun"
        type="selection"
        width="55"
      />
      <el-table-column align="center" label="序号" width="60">
        <template #default="{ $index }">
          {{ getIndex($index) }}
        </template>
      </el-table-column>
      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        :align="item.center ? item.center : 'center'"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'"
      >
        <template #default="{ row }">
          <span v-if="item.label === '申请日期'">
            {{ row.applicationDate | dateformat('YYYY-MM-DD') }}
          </span>
          <span v-else-if="item.label === '费用所属部门/项目'">
            {{ row.type === 'depart' ? row.departName : row.projectName }}
          </span>
          <span v-else-if="item.prop === 'entertainMoney'">
            {{ row[item.prop] | currencyFormat }}
          </span>
          <span v-else-if="item.prop === 'entertainNum'">
            {{ row[item.prop] ? row[item.prop] + '人' : '' }}
          </span>
          <span v-else-if="item.prop === 'receptionNum'">
            {{ row[item.prop] ? row[item.prop] + '人' : '' }}
          </span>
          <span v-else-if="item.label === '审批状态'">
            <el-tag :type="fmtFlowType(row)">
              {{ row[item.prop] }}
            </el-tag>
          </span>
          <span v-else>{{ row[item.prop] }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" fixed="right" label="操作" width="320">
        <template #default="{ row }">
          <el-button
            v-permissions="{ permission: ['projectEntertain:update'] }"
            :disabled="isDisabledEditFun(row)"
            icon="el-icon-edit"
            style="margin: 0 10px 0 0 !important"
            type="primary"
            @click="handleEdit(row)"
          >
            编辑
          </el-button>
          <el-button
            v-if="isAssignee(row)"
            icon="el-icon-edit-outline"
            style="margin: 0 10px 10px 0 !important"
            type="success"
            @click.native.prevent="showApprovalDlg(row)"
          >
            审批
          </el-button>
          <el-button
            icon="el-icon-view"
            style="margin: 0 10px 10px 0 !important"
            type="success"
            @click="handleDetail(row)"
          >
            详情
          </el-button>
          <el-dropdown>
            <el-button size="small" type="success">
              <i class="el-icon-more" />
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-if="isStartFlowFlag(row)"
                @click.native.prevent="startWorkFlow(row)"
              >
                <i class="el-icon-video-play" />
                启动流程
              </el-dropdown-item>
              <el-dropdown-item @click.native.prevent="showReport(row)">
                <i class="el-icon-video-play" />
                查看报表
              </el-dropdown-item>
              <el-dropdown-item
                v-if="isDeleteFlowFlag(row)"
                @click.native.prevent="deleteWorkFlow(row)"
              >
                <i class="el-icon-refresh-left" />
                撤回流程
              </el-dropdown-item>
              <el-dropdown-item
                v-if="isDeleteFlag(row)"
                v-permissions="{
                  permission: ['projectEntertain:del'],
                }"
                style="color: #fd5353"
                @click.native.prevent="handleDelete(row)"
              >
                <i class="el-icon-delete" />
                删除
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <table-edit ref="edit" :tree-data="treeData" @fetch-data="fetchData" />
    <entertainApplicationDetail ref="tableDetail" />
    <!-- 流程启动动态审批条件处理 -->
    <VabStartFlowProcess ref="VabStartFlowProcess" />
  </div>
</template>

<script>
  import {
    deleteDataEntertainApplication,
    getDataListByPageEntertainApplication,
  } from '@/api/administrativeManagement/entertainApplication-api'
  import TableEdit from './components/entertainApplicationEdit.vue'
  import entertainApplicationDetail from './components/entertainApplicationDetail.vue'
  import tableMix from '@/views/mixins/table'
  // import ProjectSelect from '@/views/common/ProjectSelect.vue'
  import { mapGetters } from 'vuex'
  import { reportAddr } from '@/utils/constants'
  //ending
  export default {
    name: 'ProjectEntertain',
    components: {
      TableEdit,
      entertainApplicationDetail,
      // ProjectSelect,
      ...mapGetters({
        userId: 'user/userId',
        userName: 'user/userName',
        departList: 'acl/departList',
      }),
    },
    mixins: [tableMix],
    data() {
      return {
        // formMinHeight:1,
        // hasPage:false,
        columns: [
          {
            label: '单号',
            prop: 'serialNumber',
            width: '220',
          },
          {
            label: '费用所属项目',
            prop: 'projectName',
            width: '320',
          },
          {
            label: '申请人',
            prop: 'createByName',
          },
          {
            label: '登记部门',
            prop: 'createDepartName',
            width: '120',
          },
          {
            label: '招待形式',
            prop: 'receptionTypeName',
          },
          {
            label: '招待人数',
            prop: 'entertainNum',
            width: '100',
          },
          {
            label: '接待人数',
            prop: 'receptionNum',
            width: '100',
          },
          {
            label: '招待原因',
            prop: 'reasons',
            width: '360',
          },
          {
            label: '招待金额(元)',
            prop: 'entertainMoney',
            width: '160',
          },

          {
            label: '申请日期',
            prop: 'applicationDate',
            width: '120',
          },
          {
            label: '审批状态',
            prop: 'approvalResultName',
            width: '120',
          },
        ],
        treeData: [],
        queryForm: {
          createByName: '',
          type: 'project',
          projectName: '',
          approvalResult: '',
        },
        type: 'project',
        departData: [],
        defaultProps: {
          children: 'children',
          label: 'label',
        },
      }
    },
    computed: {},
    created() {
      this.initDefaultCheck()
      this.fetchData()
    },
    methods: {
      flowDataVariables() {
        return {
          amount: this.flowData.entertainMoney,
          receptionType: this.flowData.receptionType,
        }
      },
      // 限制勾选
      selectableFun(row) {
        return this.isDeleteFlag(row)
      },
      showReport(row) {
        const params = '&id=' + row.id
        const url =
          reportAddr +
          '/ReportServer?reportlet=/oaNew/routineApproval/projectEntertain.cpt' +
          params
        window.open(url)
      },
      async fetchData() {
        //async 异步数据处理方法
        this.queryForm.curPage = this.pageInfo.curPage
        this.queryForm.pageSize = this.pageInfo.pageSize
        this.listLoading = true
        const { result } = await getDataListByPageEntertainApplication(
          this.queryForm
        )
        this.list = result.records //把请求到的数据赋值给表格
        this.pageInfo.total = Number(result.total)
        this.listLoading = false
      },
      handleDelete(row) {
        if (row.id) {
          this.$baseConfirm('你确定要删除当前数据吗', null, async () => {
            deleteDataEntertainApplication({ id: row.id }).then(() => {
              this.fetchData()
              this.$baseMessage(
                '删除成功!',
                'success',
                'vab-hey-message-success'
              )
            })
          })
        }
      },

      showAdd() {
        this.$refs['edit'].showEdit(this.type)
      },
      handleEdit(row) {
        this.$refs['edit'].showEdit(row.type, row)
      },
      handleDetail(row) {
        this.$refs['tableDetail'].showDialog(row)
      },
      // 搜索条件必备下面的方法
      resetFormPage() {
        // this.$refs.departTree.setCheckedKeys([])
        this.queryForm.projectName = ''
        this.queryForm.createByName = ''
        this.queryForm.approvalResult = ''
        this.resetForm('form')
      },
    },
  }
</script>

<style lang="scss" scoped></style>
