<template>
  <el-dialog
    v-drag
    append-to-body
    center
    :close-on-click-modal="false"
    title="短时外出详情"
    top="5vh"
    :visible.sync="dialogDetailVisible"
    width="1650px"
  >
    <div style="display: flex; max-height: 75vh; overflow-y: scroll">
      <div style="flex: 1">
        <el-form
          ref="dataForm"
          :inline="true"
          label-position="right"
          label-width="130px"
          :model="formData"
        >
          <el-descriptions
            border
            class="margin-top"
            :column="3"
            :label-style="labelStyle"
            size="medium"
          >
            <el-descriptions-item>
              <template slot="label">单号</template>
              {{ formData.serialNumber }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">申请人</template>
              {{ formData.createByName }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">申请部门</template>
              {{ formData.createDepartName }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">申请日期</template>
              {{ formData.recordDate | dateformat('YYYY-MM-DD') }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">费用所属部门</template>
              {{ formData.departName }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">出行方式</template>
              {{ formData.travelModeName }}
            </el-descriptions-item>
            <el-descriptions-item :span="3">
              <template slot="label">外出事由</template>
              {{ formData.reason }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">开始时间</template>
              {{ formData.startDate | dateformat('YYYY-MM-DD  HH:mm') }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">结束时间</template>
              {{ formData.endDate | dateformat('YYYY-MM-DD HH:mm') }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">时长</template>
              {{ calculationDuration() }}
            </el-descriptions-item>
            <el-descriptions-item v-if="formData.travelModeName === '私车公用'">
              <template slot="label">车牌号</template>
              {{ formData.plateNumber }}
            </el-descriptions-item>
            <el-descriptions-item v-if="formData.travelModeName === '私车公用'">
              <template slot="label">车辆归属</template>
              {{ formData.carOwnerName }}
            </el-descriptions-item>
            <el-descriptions-item v-if="formData.travelModeName === '私车公用'">
              <template slot="label">车补标准</template>
              {{ formData.subsidyStandard }}
            </el-descriptions-item>
            <el-descriptions-item v-if="formData.travelModeName === '私车公用'">
              <template slot="label">出发前公里数</template>
              {{
                formData.startKilometers ? formData.startKilometers + 'km' : ''
              }}
            </el-descriptions-item>
            <el-descriptions-item v-if="formData.travelModeName === '私车公用'">
              <template slot="label">返程后公里数</template>
              {{
                formData.returnKilometers
                  ? formData.returnKilometers + 'km'
                  : ''
              }}
            </el-descriptions-item>
            <el-descriptions-item v-if="formData.travelModeName === '私车公用'">
              <template slot="label">本次行驶里程</template>
              {{ calculationMileageTraveled() }}
            </el-descriptions-item>
            <el-descriptions-item v-if="formData.travelModeName === '私车公用'">
              <template slot="label">出发前照片：</template>
              <el-image
                :preview-src-list="[formData.startForImg]"
                :src="formData.startForImg"
                style="width: 100px; height: 100px"
              >
                <div
                  class="image-slot"
                  style="
                    width: 100px;
                    height: 100px;
                    background: #cccccc;
                    line-height: 100px;
                    text-align: center;
                  "
                  slot="error"
                >
                  暂无图片
                </div>
              </el-image>
            </el-descriptions-item>
            <el-descriptions-item v-if="formData.travelModeName === '私车公用'">
              <template slot="label">目的地照片：</template>
              <el-image
                :preview-src-list="[formData.destinationImg]"
                :src="formData.destinationImg"
                style="width: 100px; height: 100px"
              >
                <div
                  class="image-slot"
                  style="
                    width: 100px;
                    height: 100px;
                    background: #cccccc;
                    line-height: 100px;
                    text-align: center;
                  "
                  slot="error"
                >
                  暂无图片
                </div>
              </el-image>
            </el-descriptions-item>
            <el-descriptions-item v-if="formData.travelModeName === '私车公用'">
              <template slot="label">返程后照片：</template>
              <el-image
                :preview-src-list="[formData.afterForImg]"
                :src="formData.afterForImg"
                style="width: 100px; height: 100px"
              >
                <div
                  class="image-slot"
                  style="
                    width: 100px;
                    height: 100px;
                    background: #cccccc;
                    line-height: 100px;
                    text-align: center;
                  "
                  slot="error"
                >
                  暂无图片
                </div>
              </el-image>
            </el-descriptions-item>
            <el-descriptions-item :span="3">
              <template slot="label">备注</template>
              {{ formData.remark }}
            </el-descriptions-item>
          </el-descriptions>
        </el-form>
        <!-- 附件上传 -->
        <UploadLargeFileFdfsPopupDetail
          ref="UploadLargeFileFdfsPopupDetail"
          style="margin-top: 20px"
        />
        <!-- 审批 -->
        <VabApproveFlow
          v-if="isApproval"
          ref="ApprovalFlow"
          @callBackRefresh="callBackRefresh"
        />
      </div>
      <div v-if="formData.approvalResult !== '0'">
        <!-- 审批记录 -->
        <VabWorkFlowApproveRecDlg ref="workFlwApprovalRecDlg" />
      </div>
    </div>
    <div slot="footer" v-if="!isApproval" class="dialog-footer">
      <el-button type="danger" @click="dialogDetailVisible = false">
        关闭
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import UploadLargeFileFdfsPopupDetail from '@/views/common/UploadLargeFileFdfsPopupDetail.vue'
  export default {
    components: {
      UploadLargeFileFdfsPopupDetail,
    },
    data() {
      return {
        dialogDetailVisible: false,
        formData: {},
        isApproval: false,
        labelStyle: {
          width: '130px',
        },
      }
    },
    methods: {
      showApprovalFlowByParams(bizKey, row, params) {
        this.showDialog(row)
        this.isApproval = true
        this.$nextTick(() => {
          this.$refs.ApprovalFlow.showApprovalFlowByParams(bizKey, row, params)
        })
      },
      callBackRefresh() {
        this.$parent.fetchData()
        this.dialogDetailVisible = false
      },
      showDialog(row) {
        this.isApproval = false
        this.formData = { ...row }
        this.$nextTick(() => {
          if (this.formData.approvalResult !== '0') {
            this.$refs.workFlwApprovalRecDlg.getTaskProcessListByBizKey(
              this.formData.id
            )
          }
          this.$refs.UploadLargeFileFdfsPopupDetail.getParamsData({
            bizId: this.formData.id,
            bizCode: this.$route.name,
            isShow: true,
          })
        })
        this.dialogDetailVisible = true
      },
      calculationDuration() {
        let startDate = Date.parse(this.formData.startDate)
        let endDate = Date.parse(this.formData.endDate)
        // 两个时间戳相差的毫秒数
        let usedTime = endDate - startDate
        // 计算出小时数
        let hours = Math.floor(usedTime / (3600 * 1000))
        this.formData.duration = hours + '小时'
        return this.formData.duration
      },
      calculationMileageTraveled() {
        this.formData.startKilometers = Number(
          this.formData.startKilometers
        ).toFixed(2)
        if (this.formData.returnKilometers) {
          this.formData.returnKilometers = Number(
            this.formData.returnKilometers
          ).toFixed(2)
          this.formData.mileageTraveled = (
            this.formData.returnKilometers - this.formData.startKilometers
          ).toFixed(2)
          return `${this.formData.mileageTraveled}km`
        } else {
          this.formData.mileageTraveled = '自动计算'
        }
      },
    },
  }
</script>

<style lang="scss" scoped></style>
