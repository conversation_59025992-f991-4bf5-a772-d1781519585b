<template>
  <el-dialog
    v-drag
    append-to-body
    center
    :close-on-click-modal="false"
    :title="title"
    top="5vh"
    :visible.sync="dialogFormVisible"
    width="1500px"
    @close="close"
  >
    <el-form
      ref="dataForm"
      v-loading="formloading"
      :inline="true"
      label-position="right"
      label-width="150px"
      :model="formData"
      :rules="rules"
    >
      <table v-loading="formloading" class="form-table">
        <tr class="title"><td colspan="3">基本信息</td></tr>
        <tr>
          <td>
            <el-form-item label="单号" prop="serialNumber">
              <el-input
                v-model="formData.serialNumber"
                disabled
                placeholder="自动生成"
                style="width: 280px"
              />
            </el-form-item>
          </td>

          <td>
            <el-form-item label="申请人" prop="createByName">
              <el-input v-model="formData.createByName" disabled readonly />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="申请日期" prop="applicationDate">
              <el-date-picker
                v-model="formData.applicationDate"
                format="yyyy-MM-dd"
                placeholder="选择时间"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="2">
            <el-form-item label="车牌号" prop="plateNumber">
              <!-- <el-input v-model="formData.licensePlate" style="width: 280px" /> -->
              <el-input
                v-model="formData.plateNumber"
                @click.native="showVehicleListInfo"
                style="width: 380px"
              />
            </el-form-item>
          </td>
          <!-- <td>
            <el-form-item label="兼职司机" prop="partTimeDriverName">
                <el-input v-model="formData.partTimeDriverName" disabled  />
            </el-form-item>
          </td> -->
          <td>
            <el-form-item label="油卡编号" prop="oilCardNo">
              <el-input v-model="formData.oilCardNo" disabled />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="2">
            <el-form-item label="费用所属项目" prop="projectName">
              <!-- <el-input v-model="formData.projectName" disabled /> -->
              <el-input
                v-model="formData.projectName"
                :disabled="!formData.isAdd"
                filterable
                placeholder="请选择"
                style="width: 380px"
                @click.native="selectContractProject"
              />
            </el-form-item>
            <!-- <el-form-item label="费用所属项目" prop="projectId">
              <el-select
                v-model="formData.projectId"
                filterable
                placeholder="请选择"
                style="width: 280px"
              >
                <el-option
                  v-for="item in projectDataList"
                  :key="item.id"
                  :label="item.projectName"
                  :value="item.id"
                />
              </el-select>
            </el-form-item> -->
          </td>
          <td v-if="formData.projectName">
            <el-form-item label="费用所属部门" prop="departName">
              <el-input v-model="formData.departName" disabled />
            </el-form-item>
          </td>
          <td v-if="!formData.projectName">
            <el-form-item label="费用所属部门" prop="depart">
              <t-form-tree
                v-if="dialogFormVisible"
                v-model="formData.depart"
                :treeData="departTreeData"
                :disabled="!formData.isAdd"
                :defaultProps="{ children: 'children', label: 'departName' }"
                :showTop="false"
                placeholder="请选择费用所属部门"
                @change="changeDepart"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="充值方式" prop="rechargeType">
              <el-select
                v-model="formData.rechargeType"
                placeholder="请选择充值方式"
              >
                <el-option
                  v-for="item in selectedRechargeType"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </td>
          <!-- <td>
            <el-form-item label="剩余金额(元)" prop="balance">
              <el-input
                v-model="formData.balance"
                placeholder="请输入金额(小数位不超过2位)"
                style="width: 280px"
              />
            </el-form-item>
          </td> -->
          <td>
            <el-form-item label="充值/加油金额(元)" prop="rechargeAmount">
              <el-input
                v-model="formData.rechargeAmount"
                placeholder="请输入金额(小数位不超过2位)"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="目前公里数(km)" prop="odometer">
              <el-input v-model="formData.odometer" />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label="车俩调配信息">
              <el-link
                v-if="
                  formData.vehicleAllocationCode !== null &&
                  formData.vehicleAllocationCode !== ''
                "
                type="primary"
                @click="showDetails()"
              >
                {{ formData.vehicleAllocationCode }}
              </el-link>
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label="仪表盘里程数照片">
              <div class="form-item-container">
                <div v-if="formData.picture" class="select-list">
                  <el-image
                    :preview-src-list="[formData.picture]"
                    :src="formData.picture"
                    style="width: 50px; height: 50px"
                  />
                </div>
              </div>
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="formData.remark"
                maxlength="1000"
                placeholder="请输入内容(不超过1000字)"
                style="width: 1180px"
                type="textarea"
              />
            </el-form-item>
          </td>
        </tr>
      </table>
    </el-form>
    <!-- 附件上传 -->
    <VehicleListPopup ref="VehicleListPopup" @getVehicleInfo="getVehicleInfo" />
    <VehiclePopup ref="VehiclePopup" :output-volume-list="outputVolumeList" />
    <UploadItem ref="uploadItemRef" @callbackSuccess="callbackSuccess" />
    <UploadLargeFileFdfsPopup ref="UploadLargeFileFdfsPopup" />
    <ProjectListPopup
      ref="projectListPopupRef"
      @getProjectInfo="getProjectInfo"
    />
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
  import { saveDataFuelCardRecharge } from '@/api/administrativeManagement/fuelCardRecharge-api'
  import {
    getDataList as getvehicleAllocationList,
    getData as getvehicleAllocation,
  } from '@/api/administrativeManagement/vehicleAllocation-api'
  import UploadLargeFileFdfsPopup from '@/views/common/UploadLargeFileFdfsPopup.vue'
  import UploadItem from '@/views/administrativeManagement/announcementManagement/UploadItem.vue'
  import VehiclePopup from '@/views/administrativeManagement/vehicleManagement/components/vehicleAllocationDetail.vue'
  import VehicleListPopup from '@/views/common/VehicleListPopup.vue'
  import { genUUID, parseTime } from '@/utils/th_utils.js'
  import { mapGetters } from 'vuex'
  import { getProjectList } from '@/api/project'
  import tableMix from '@/views/mixins/table'
  import ProjectListPopup from '@/views/common/ProjectListPopup.vue'
  import { getDepartList } from '@/api/system/depart-api'

  export default {
    name: 'TableEdit',
    components: {
      UploadLargeFileFdfsPopup,
      UploadItem,
      ProjectListPopup,
      VehicleListPopup,
      VehiclePopup,
    },
    mixins: [tableMix],
    props: {},
    data() {
      const validateReg = (rule, value, callback) => {
        const reg = /^(([1-9][0-9]*)|(([0]\.\d{1,2}|[1-9][0-9]*\.\d{1,2})))$/ //任意正整数，正小数（小数位不超过2位）
        if (value && !reg.test(value)) {
          callback(new Error('金额格式不对'))
        } else {
          callback()
        }
      }

      const validateRegZero = (rule, value, callback) => {
        const reg =
          /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/ //验证金额（含零），保留两位小数
        if (value && !reg.test(value)) {
          callback(new Error('金额格式不对'))
        } else {
          callback()
        }
      }

      return {
        outputVolumeList: [
          { label: '1.8及以上', value: '1', subsidyStandard: '1.6元/公里' },
          { label: '1.8以下', value: '2', subsidyStandard: '1.1元/公里' },
        ],
        departTreeData: [],
        projectDataList: [],
        title: '',
        dialogFormVisible: false,
        formloading: false,
        formData: {},
        applicationDate: '',
        selectedTavelModeName: [],
        selectedAccommodation: [],
        projectList: [],
        defaultProps: {
          children: 'children',
          label: 'label',
        },
        rules: {
          plateNumber: {
            required: true,
            message: '车牌号为必填',
            trigger: 'blur',
          },
          rechargeType: {
            required: true,
            message: '充值方式为必填',
            trigger: 'blur',
          },
          odometer: {
            required: true,
            message: '目前公里数为必填',
            trigger: 'blur',
          },
          rechargeAmount: [
            { required: true, message: '充值金额为必填', trigger: 'blur' },
            { validator: validateReg, trigger: 'change' },
          ],
          depart: [
            { required: true, message: '费用所属部门为必填', trigger: 'blur' },
          ],
          departName: [
            { required: true, message: '费用所属部门为必填', trigger: 'blur' },
          ],
        },
        duration: '',
        selectedIsDriver: [
          { label: '否', value: 0 },
          { label: '是', value: 1 },
        ],
        selectedRechargeType: [
          { label: '现金', value: 0 },
          { label: '油卡', value: 1 },
        ],
      }
    },
    computed: {
      ...mapGetters({
        userId: 'user/userId',
        userName: 'user/userName',
        departList: 'acl/departList',
      }),
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.getProjectList()
      this.getDepartList()
    },
    methods: {
      getDepartList() {
        getDepartList({}).then((response) => {
          this.departTreeData = response.result
        })
      },
      getProjectList() {
        const params = {
          approvalResult: '2',
        }
        getProjectList(params).then((response) => {
          this.projectDataList = response.result
        })
      },
      showEdit(row) {
        if (!row) {
          this.title = '新增油卡充值申请'
          this.dialogStatus = 'create'
          this.initForm()
        } else {
          this.title = '编辑油卡充值申请'
          this.dialogStatus = 'update'
          this.formData = Object.assign({ isAdd: false }, row)
        }
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs.UploadLargeFileFdfsPopup.getParamsData({
            bizId: this.formData.id,
            bizCode: 'fuelCardRecharge',
          })
        })
        // this.$parent.$refs.UploadLargeFileFdfsPopup.headRun()
        // this.getProjectTree()
        this.$nextTick(() => {
          this.$refs['dataForm'].clearValidate()
        })
      },
      // 初始化表单
      initForm() {
        this.formData = {
          id: genUUID(),
          licensePlate: '', //车牌号
          isDriver: '', //兼职司机(0:否 1:是)
          odometer: 0, //目前公里数（最大6位整数，2位小数）
          rechargeType: 0, //充值方式（0：自费 1：油卡）
          cardNumber: '', //油卡编号
          balance: '', //剩余金额（最大6位整数，2位小数）
          rechargeAmount: '', //充值金额（最大6位整数，2位小数）
          // applicant:'', //申请人
          projectId: '', //费用所属项目
          projectName: '',
          operationCode: 'YKCZ',
          vehicleAllocationCode: '',
          createTime: '',
          updateBy: '',
          updateTime: '',
          picture: '',
          createBy: this.userId, //申请人Id
          createByName: this.userName,
          viewUser: this.userId, //申请人名字
          depart: '', //部门id
          createDepart: this.departList[0].id, //部门id
          departCode: this.departList[0].departCode,
          departName: '',
          vehicleAllocationId: '',
          applicationDate: parseTime(new Date()), //申请日期
          serialNumberTable: 'fuel_card_recharge',
          remark: '',
          isAdd: true,
        }
      },
      selectContractProject() {
        this.$refs.projectListPopupRef.showDialog({
          selectIds: this.formData.projectId,
          projectName: this.formData.projectName
        })
      },
      changeDepart(id) {
        for (const item of this.departTreeData) {
          if (item.id === id) {
            this.formData.depart = item.id
            this.formData.departCode = item.departCode
            this.formData.departName = item.departName
          }
        }
      },
      async showDetails() {
        const { result } = await getvehicleAllocation({
          id: this.formData.vehicleAllocationId,
        })
        this.$refs.VehiclePopup.showDialog(result)
      },
      async getVehicleInfo(data) {
        console.log(data)
        if (data.length > 0) {
          const dataObj = data[0]
          const { id, plateNumber, oilCardNo } = dataObj
          this.formData = {
            ...this.formData,
            licensePlate: id,

            plateNumber,
            oilCardNo,
          }
          // const { result } = await getvehicleAllocationList({
          //   vehicleId: this.formData.licensePlate,
          //   approvalResult: '2',
          // })
          // if (result !== undefined && result.length > 0) {
          //   this.formData.vehicleAllocationId = result[0].id
          //   this.formData.vehicleAllocationCode = result[0].serialNumber
          // } else {
          //   this.formData.vehicleAllocationId = ''
          //   this.formData.vehicleAllocationCode = ''
          // }
          this.$refs.dataForm.clearValidate('plateNumber')
        }
      },
      showVehicleListInfo() {
        const queryMap = {
          stateList:[0,1],
        }
        let selectRows = []
        if(this.formData.licensePlate) {
          selectRows.push({
            id: this.formData.licensePlate,
            plateNumber: this.formData.plateNumber,
          })
        }
        this.$refs.VehicleListPopup.showDialog({
          queryMap,
          selectRows,
          selectIds: this.formData.licensePlate,
        })
      },
      async getProjectInfo(data) {
        console.log(data, 'data')
        if (data.length > 0) {
          const dataObj = data[0]
          const {
            id,
            projectName,
            serialNumber,
            depart,
            departName,
            departCode,
          } = dataObj
          this.formData = {
            ...this.formData,
            projectId: id,
            projectName,
            depart,
            departName,
            departCode,
            projectSerialNumber: serialNumber,
          }
          console.log(dataObj, 'dataObj')
          this.$refs.dataForm.clearValidate('projectName')
        }
      },
      showUploadItem() {
        const data = {
          bizId: this.formData.id,
          bizCode: 'fuelCardRechargePicture',
          isOneFile: true,
        }
        this.$refs.uploadItemRef.showUploadFileDialog(data)
      },
      callbackSuccess(data) {
        console.log('===========')
        console.log(data.urlPath)
        this.formData.picture = data.urlPath
      },
      save() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            // return
            saveDataFuelCardRecharge(this.formData)
              .then((response) => {
                this.$baseMessage(
                  this.formData.isAdd ? '新增成功！' : '修改成功!',
                  'success',
                  'vab-hey-message-success'
                )
                this.close()
                this.$emit('fetch-data')
              })
              .catch((err) => {
                this.$baseMessage(
                  this.formData.isAdd ? '新增失败！' : '修改失败!',
                  'error',
                  'vab-hey-message-error'
                )
              })
          }
        })
      },
      close() {
        this.$refs['dataForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
      },
    },
  }
</script>

<style lang="scss" scoped></style>
