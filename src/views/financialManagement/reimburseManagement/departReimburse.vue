<template>
  <div
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="80px"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item
            label="费用所属部门"
            label-width="108px"
            prop="departName"
          >
            <el-input
              v-model="queryForm.departName"
              clearable
              placeholder="请输入费用所属部门"
            />
          </el-form-item>
          <el-form-item
            label="报销人"
            label-width="66px"
            prop="reimbursementApplicantName"
          >
            <el-input
              v-model="queryForm.reimbursementApplicantName"
              clearable
              placeholder="请输入报销人名称"
            />
          </el-form-item>
          <el-form-item label="收款人" label-width="66px" prop="payerName">
            <el-input
              v-model="queryForm.payerName"
              clearable
              placeholder="请输入收款人名称"
            />
          </el-form-item>
          <el-form-item label="审批状态" prop="approvalResult">
            <el-select
              ref="selectFlow"
              v-model="queryForm.approvalResult"
              placeholder="请选择审批状态"
            >
              <el-option option value="">所有状态</el-option>
              <el-option
                v-for="item in approvalResultNameList"
                :key="item.id"
                :label="item.approvalResultName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item v-show="!fold" label="报销时间" prop="queryDate">
            <el-date-picker
              v-model="queryForm.queryDate"
              end-placeholder="结束时间"
              range-separator="至"
              start-placeholder="开始时间"
              style="width: 400px"
              type="daterange"
            />
          </el-form-item>

          <el-form-item>
            <el-button
              icon="el-icon-search"
              native-type="submit"
              type="primary"
              @click="handleQuery"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-right"
              @click.native="resetForm('form')"
            >
              重置
            </el-button>
          </el-form-item>
          <el-button
            style="margin: 0 0 0 10px !important"
            type="text"
            @click="handleFold"
          >
            <span v-if="fold">展开</span>
            <span v-else>合并</span>
            <vab-icon
              class="vab-dropdown"
              :class="{ 'vab-dropdown-active': fold }"
              icon="arrow-up-s-line"
            />
          </el-button>
        </el-form>
      </vab-query-form-top-panel>
      <vab-query-form-left-panel :span="12">
        <el-button
          v-permissions="{ permission: ['departReimburse:add'] }"
          icon="el-icon-plus"
          type="primary"
          @click="showAdd"
        >
          添加
        </el-button>
        <el-button
          :disabled="exportLoading"
          icon="el-icon-download"
          type="warning"
          @click.native="exportBtn('部门报销.xlsx')"
        >
          导出
        </el-button>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="12">
        <el-button
          style="margin: 0 10px 10px 0 !important"
          type="primary"
          @click="clickFullScreen"
        >
          <vab-icon
            :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'"
          />
          表格全屏
        </el-button>
        <el-popover
          ref="popover"
          popper-class="custom-table-checkbox"
          trigger="hover"
        >
          <el-radio-group v-model="lineHeight">
            <el-radio label="medium">大</el-radio>
            <el-radio label="small">中</el-radio>
            <el-radio label="mini">小</el-radio>
          </el-radio-group>
          <template #reference>
            <el-button style="margin: 0 10px 10px 0 !important" type="primary">
              <vab-icon icon="line-height" />
              表格尺寸
            </el-button>
          </template>
        </el-popover>
        <el-popover popper-class="custom-table-checkbox" trigger="hover">
          <el-checkbox-group v-model="checkList">
            <vab-draggable v-bind="dragOptions" :list="columns">
              <div v-for="(item, index) in columns" :key="item + index">
                <vab-icon icon="drag-drop-line" />
                <el-checkbox
                  :disabled="item.disableCheck === true"
                  :label="item.label"
                >
                  {{ item.label }}
                </el-checkbox>
              </div>
            </vab-draggable>
          </el-checkbox-group>
          <template #reference>
            <el-button
              icon="el-icon-setting"
              style="margin: 0 0 10px 0 !important"
              type="primary"
            >
              可拖拽列设置
            </el-button>
          </template>
        </el-popover>
      </vab-query-form-right-panel>
    </vab-query-form>
    <el-table
      ref="tableSort"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      show-summary
      :size="lineHeight"
      stripe
      :summary-method="getSummaries"
      @selection-change="setSelectRows"
    >
      <el-table-column align="center" label="序号" width="60">
        <template #default="{ $index }">
          {{ getIndex($index) }}
        </template>
      </el-table-column>
      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        :align="item.center ? item.center : 'center'"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'"
      >
        <template #default="{ row }">
          <span v-if="item.label === '审批状态'">
            <el-tag :type="fmtFlowType(row)">
              {{ row[item.prop] }}
            </el-tag>
          </span>
          <span v-else-if="item.prop === 'reimbursementAmount'">
            {{ row.reimbursementAmount | currencyFormat }}
          </span>
          <span v-else>
            {{ row[item.prop] }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="320">
        <template #default="{ row }">
          <el-button
            v-permissions="{ permission: ['departReimburse:update'] }"
            :disabled="isDisabledEditFun(row)"
            icon="el-icon-edit"
            style="margin: 0 10px 10px 0 !important"
            type="primary"
            @click="handleEdit(row)"
          >
            编辑
          </el-button>
          <el-button
            v-if="isAssignee(row)"
            icon="el-icon-edit-outline"
            style="margin: 0 10px 10px 0 !important"
            type="warning"
            @click.native.prevent="showApprovalDlg(row)"
          >
            审批
          </el-button>
          <el-button
            icon="el-icon-view"
            style="margin: 0 10px 10px 0 !important"
            type="success"
            @click="handleDetail(row)"
          >
            详情
          </el-button>
          <el-dropdown>
            <el-button size="small" type="success">
              <i class="el-icon-more" />
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-if="isStartFlowFlag(row)"
                @click.native.prevent="startWorkFlow(row)"
              >
                <i class="el-icon-video-play" />
                启动流程
              </el-dropdown-item>
              <el-dropdown-item
                v-if="isDeleteFlowFlag(row)"
                @click.native.prevent="deleteWorkFlow(row)"
              >
                <i class="el-icon-refresh-left" />
                撤回流程
              </el-dropdown-item>
              <el-dropdown-item
                v-if="isDeleteFlag(row)"
                v-permissions="{ permission: ['departReimburse:del'] }"
                divided
                @click.native.prevent="handleDelete(row)"
              >
                <span style="color: #fd5353">
                  <i class="el-icon-delete-solid" />
                  删除
                </span>
              </el-dropdown-item>
              <el-dropdown-item divided @click.native.prevent="showReport(row)">
                <i class="el-icon-s-order" />
                查看报表
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <table-detail ref="tableDetail" :budget-list="budgetList" />
    <table-edit
      ref="tableEdit"
      :budget-list="budgetList"
      @fetch-data="fetchData"
    />
    <!--    <type ref="type" @callBack="showAdd"/>-->
    <!-- 部门选择 -->
    <!--    <VabWorkFlowDepart ref="VabWorkFlowDepart"  />-->
    <!-- 流程启动动态审批条件处理 -->
    <VabStartFlowProcess ref="VabStartFlowProcess" />
    <VabTableExport ref="tableExport" />
  </div>
</template>

<script>
  import tableMix from '@/views/mixins/table'
  import tableDetail from './components/reimburseManagementDetail.vue'
  import tableEdit from './components/reimburseManagementEdit.vue'
  import { reportAddr } from '@/utils/constants'

  import {
    getDataListByPage,
    deleteData,
    exportData,
    getReimburseStatistics,
  } from '@/api/financialManagement/reimburseManagement'
  import { getDictList } from '@/api/system/dict-api'

  export default {
    name: 'DepartReimburse',
    components: {
      tableDetail,
      tableEdit,
    },
    mixins: [tableMix],
    data() {
      return {
        formMaxHeight: 2,
        hasCard: true,
        columns: [
          {
            label: '单号',
            prop: 'serialNumber',
            width: '200',
          },
          {
            label: '费用所属部门',
            prop: 'departName',
            width: '120',
          },
          {
            label: '报销类型',
            prop: 'reimbursementTypeName',
          },
          {
            label: '报销金额(元)',
            prop: 'reimbursementAmount',
            width: '160',
          },
          {
            label: '报销人',
            prop: 'reimbursementApplicantName',
          },
          {
            label: '收款人',
            prop: 'payeeNames',
          },
          {
            label: '付款单位',
            prop: 'payerIdName',
            width: '240',
          },
          {
            label: '报销事由',
            prop: 'reimbursementReason',
            width: '240',
          },
          {
            label: '备注',
            prop: 'remark',
            width: '240',
          },
          {
            label: '审批状态',
            prop: 'approvalResultName',
            width: '100',
          },
        ],
        queryForm: {
          type: 'depart',
          projectId: '',
          departName: '',
          reimbursementApplicantName: '',
          payerName: '',
          queryDate: [],
          queryDateStart: null,
          queryDateEnd: null,
        },
        //   数据字典
        // 成本预算清单
        budgetList: [],
        // 报销金额合计
        reimbursementAmount: 0,
      }
    },
    computed: {},
    created() {
      this.getDictDetails()
      this.initDefaultCheck()
      this.fetchData()
    },
    methods: {
      // 金额统计
      getSummaries(param) {
        const { columns } = param
        const sums = []
        columns.forEach((column, index) => {
          if (index === 0) {
            sums[index] = '合计'
            return
          }
          // 创建一个映射表，将 column.property 映射到对应的值
          const valueMap = {
            reimbursementAmount: this.reimbursementAmount,
          }

          // 使用映射表获取对应的值，并格式化
          sums[index] =
            valueMap[column.property] !== undefined
              ? this.formatMoney(valueMap[column.property])
              : ''
        })

        return sums
      },
      flowDataVariables() {
        return {
          amount: this.flowData.reimbursementAmount,
          reimbursementApplicant: this.flowData.reimbursementApplicant,
        }
      },
      // 查询字典 获取成本预算清单
      getDictDetails() {
        getDictList({ dictCode: 'budgetListing' }).then((response) => {
          this.budgetList = response.result[0].children
        })
      },
      showReport(data) {
        const params = '&id=' + data.id
        const url =
          reportAddr +
          '/ReportServer?reportlet=/oaNew/reimburseManagement/departReimburse.cpt' +
          params
        window.open(url)
      },
      async fetchData() {
        this.listLoading = true

        if (this.queryForm.queryDate.length >= 2) {
          this.queryForm.queryDateStart = this.queryForm.queryDate[0]
          this.queryForm.queryDateEnd = this.queryForm.queryDate[1]
        } else {
          this.queryForm.queryDateStart = ''
          this.queryForm.queryDateEnd = ''
        }
        const queryForm = {
          ...this.queryForm,
          curPage: this.pageInfo.curPage,
          pageSize: this.pageInfo.pageSize,
        }

        await getDataListByPage(queryForm).then((res) => {
          const {
            result: { total, records },
          } = res
          this.list = records
          this.pageInfo.total = Number(total)
        })

        // 报销金额统计
        await getReimburseStatistics(queryForm).then((response) => {
          const result = response.result
          this.reimbursementAmount = result ? result.reimbursementAmount : 0
        })
        this.listLoading = false
        this.$nextTick(() => {
          this.$refs.tableSort && this.$refs.tableSort.doLayout() // 强制刷新表格
        })
      },
      handleEdit(row) {
        this.$refs['tableEdit'].showEdit(row.type, row)
      },
      showAdd() {
        this.$refs['tableEdit'].showEdit('depart')
      },
      // handleAdd() {
      //   this.$refs['type'].showType()
      // },
      handleDetail(row) {
        this.$refs['tableDetail'].showDialog(row)
      },
      handleDelete(row) {
        if (row.id) {
          this.$baseConfirm('你确定要删除当前报销吗', null, async () => {
            deleteData({ ids: row.id })
              .then(() => {
                this.fetchData()
                this.$baseMessage(
                  '删除成功!',
                  'success',
                  'vab-hey-message-success'
                )
              })
              .catch(() => {
                this.$baseMessage('删除失败!', 'error', 'vab-hey-message-error')
              })
          })
        }
      },

      // 批删除
      batchDelete() {
        if (this.selectRows.length == 0) {
          this.$baseMessage(
            '请选中最少一条记录!',
            'error',
            'vab-hey-message-error'
          )
          return
        }
        const idArr = this.selectRows.map((item) => item.id).join(',')
        this.$baseConfirm('你确定要删除选中报销吗', null, async () => {
          deleteData({ ids: idArr })
            .then(() => {
              this.fetchData()
              this.$baseMessage(
                '删除成功!',
                'success',
                'vab-hey-message-success'
              )
            })
            .catch(() => {
              this.$baseMessage('删除失败!', 'error', 'vab-hey-message-error')
            })
        })
      },
      exportBtn(excelName) {
        this.queryForm.excelName = excelName
        this.$refs['tableExport'].showDialog(this.columns)
      },
      exportData(columns) {
        this.exportLoading = true
        exportData({ ...this.queryForm, ...columns }).then((response) => {
          const link = document.createElement('a')
          link.download = this.queryForm.excelName
          link.href = window.URL.createObjectURL(new Blob([response]))
          document.body.appendChild(link)
          link.click()
          link.download = ''
          document.body.removeChild(link)
          URL.revokeObjectURL(response)
        })
        this.exportLoading = false
      },
    },
  }
</script>

<style lang="scss" scoped>
  $base: '.depart-management';
  #{$base}-container {
    padding: 0 !important;
    background: $base-color-background !important;

    &.vab-fullscreen {
      padding: 20px !important;
    }
  }
  .expenseName {
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
</style>
