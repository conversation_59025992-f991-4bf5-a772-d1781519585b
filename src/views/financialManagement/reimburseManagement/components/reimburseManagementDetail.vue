<template>
  <el-dialog
    v-drag
    append-to-body
    center
    class="detailDialog"
    :close-on-click-modal="false"
    title="报销详情"
    top="5vh"
    :visible.sync="dialogDetailVisible"
    width="1400px"
  >
    <div
      style="
        display: flex;
        max-height: 75vh;
        overflow-y: scroll;
        overflow-x: hidden;
      "
    >
      <div style="flex: 1">
        <div v-loading="formLoading">
          <el-descriptions
            border
            class="margin-top"
            :column="3"
            :label-style="labelStyle"
            size="medium"
          >
            <el-descriptions-item>
              <template slot="label">单号</template>
              {{ formData.serialNumber }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">申请人</template>
              {{ formData.createByName }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">申请部门</template>
              {{ formData.createDepartName }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">申请日期</template>
              {{ formData.recordDate | dateformat('YYYY-MM-DD') }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">费用所属部门</template>
              {{ formData.departName }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">所属项目</template>
              {{ formData.projectName }}
            </el-descriptions-item>
            <el-descriptions-item v-if="formData.type === 'project'">
              <template slot="label">是否预算</template>
              <el-tag v-if="formData.isBudget === 0" type="danger">否</el-tag>
              <el-tag v-else type="success">是</el-tag>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">报销人</template>
              {{ formData.reimbursementApplicantName }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">报销金额(元)</template>
              {{ formData.reimbursementAmount | currencyFormat }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">付款单位</template>
              {{ formData.payerIdName }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">报销类型</template>
              {{ formData.reimbursementTypeName }}
            </el-descriptions-item>
            <el-descriptions-item v-if="formData.type === 'depart'">
              <template slot="label">是否有申请单</template>
              <el-tag v-if="formData.isApply === 0" type="danger">否</el-tag>
              <el-tag v-else type="success">是</el-tag>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">项目单号</template>
              {{ formData.projectSerialNumber }}
            </el-descriptions-item>
            <el-descriptions-item :span="3">
              <template slot="label">收款人</template>
              <div v-if="formData.payeeDetailRow.length > 0">
                <span
                  v-for="(item, index) in formData.payeeDetailRow"
                  :key="index"
                >
                  {{
                    item
                      ? item.userName +
                        '(' +
                        item.bankDeposit +
                        ':' +
                        item.cardNumber +
                        ')'
                      : ''
                  }}
                  <span>&nbsp;&nbsp;</span>
                </span>
              </div>
            </el-descriptions-item>

            <el-descriptions-item v-if="formData.isApply !== 0" :span="3">
              <template slot="label">关联申请单</template>
              <div v-if="formData.applyIdRow.length > 0">
                <span v-for="(item, index) in formData.applyIdRow" :key="index">
                  <el-link
                    v-if="!!item"
                    type="primary"
                    @click="showDetails(item)"
                  >
                    {{ item.applyNo }}
                  </el-link>
                  <span>&nbsp;&nbsp;</span>
                </span>
              </div>
            </el-descriptions-item>

            <el-descriptions-item :span="3">
              <template slot="label">报销事由</template>
              {{ formData.reimbursementReason }}
            </el-descriptions-item>
            <el-descriptions-item :span="3">
              <template slot="label">备注</template>
              {{ formData.remark }}
            </el-descriptions-item>
          </el-descriptions>
          <el-table
            ref="tableSort"
            border
            :data="dataList"
            stripe
            style="margin-top: 20px"
          >
            <el-table-column
              align="center"
              label="序号"
              type="index"
              width="80"
            />
            <el-table-column
              align="center"
              label="费用项目"
              prop="countName"
              width="150"
            />
            <el-table-column
              align="center"
              label="金额(元)"
              prop="money"
              width="110"
            >
              <template #default="{ row }">
                <span>
                  {{ row.money | currencyFormat }}
                </span>
              </template>
            </el-table-column>

            <el-table-column
              v-if="isBudget === 1"
              align="center"
              label="预算金额(元)"
              prop="amount"
              width="120"
            >
              <template #default="{ row }">
                <span>
                  {{ approvalResult === "2" ? row.budgetMoney : row.amount | currencyFormat }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              v-if="isBudget === 1"
              align="center"
              label="预算剩余金额(元)"
              prop="totalMoney"
              width="130"
            >
              <template #default="{ row }">
                <span>
                  {{ approvalResult === "2" ? row.residueBudgetMoney:(row.amount - row.totalMoney) | currencyFormat }}
                </span>
              </template>
            </el-table-column>
            <el-table-column align="center" label="备注" prop="remark" />
            <el-table-column align="center" label="操作" width="120">
              <template #default="{ row }">
                <el-button
                  icon="el-icon-link"
                  style="margin: 0 10px 10px 0 !important"
                  type="success"
                  @click="showUploadFileList(row)"
                >
                  附件
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <UploadLargeFileFdfsPopupDetail
            ref="UploadLargeFileFdfsPopupDetail"
            style="margin-top: 20px"
          />
        </div>
        <CommonUploadListPopup ref="CommonUploadListPopup" />
        <!-- 审批 -->
        <VabApproveFlow
          v-if="isApproval"
          ref="ApprovalFlow"
          @callBackRefresh="callBackRefresh"
        />
      </div>
      <div v-if="formData.approvalResult !== '0'">
        <!-- 审批记录 -->
        <VabWorkFlowApproveRecDlg ref="workFlwApprovalRecDlg" />
      </div>
      <!--      采购申请详情-->
      <purchase-application-detail
        v-if="showPurchaseApplicationDetail"
        ref="purchaseApplicationDetail"
      />
      <!--      出差申请详情-->
      <travel-detail v-if="showTravelDetail" ref="travelDetail" />
      <!--      招待申请详情-->
      <entertain-application-detail
        v-if="showEntertainApplicationDetail"
        ref="entertainApplicationDetail"
      />
      <!--   调休请假详情   -->
      <leave-detail v-if="showLeaveDetail" ref="leaveDetail" />
      <!--  油卡充值详情  -->
      <fuel-card-recharge-detail
        v-if="showFuelCardRechargeDetail"
        ref="fuelCardRechargeDetail"
      />
      <!--  短时外出  -->
      <temporary-outing-detail
        v-if="showTemporaryOutingDetail"
        ref="temporaryOutingDetail"
      />
      <!-- 公车申请 -->
      <official-car-delivery-detail
        v-if="showOfficialCarDeliveryDetail"
        ref="officialCarDeliveryDetail"
      />
      <!--项目车辆维保-->
      <project-vehicle-maintenance-detail
        v-if="showProjectVehicleMaintenanceDetail"
        ref="projectVehicleMaintenanceDetail"
      />
      <!--机关车辆维保-->
      <depart-vehicle-maintenance-detail
        v-if="showDepartVehicleMaintenanceDetail"
        ref="departVehicleMaintenanceDetail"
      />
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button type="danger" @click="dialogDetailVisible = false">
        关闭
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import UploadLargeFileFdfsPopupDetail from '@/views/common/UploadLargeFileFdfsPopupDetail.vue'
  import { getDataEvection } from '@/api/administrativeManagement/evection'
  import { getPurchaseData } from '@/api/administrativeManagement/purchaseApplication-api'
  import { getDataEntertainApplication } from '@/api/administrativeManagement/entertainApplication-api'
  import { getDataList as getReimburseListing } from '@/api/financialManagement/reimburseManagementListing'
  import CommonUploadListPopup from '@/views/common/CommonUploadListPopup.vue'
  import { getPayeeAndApplyData } from '@/api/financialManagement/reimburseManagement'
  // 采购详情组件
  import PurchaseApplicationDetail from '@/views/approvalManagement/purchasingManagement/components/purchaseApplicationDetail.vue'
  // 出差详情组件
  import TravelDetail from '@/views/approvalManagement/travelManagement/components/travelDepartDetail.vue'
  // 招待详情组件
  import EntertainApplicationDetail from '@/views/approvalManagement/entertainManagement/components/entertainApplicationDetail.vue'
  // 调休请假详情组件
  import { getData as getDataLeave } from '@/api/administrativeManagement/compensatoryLeave-api'
  import leaveDetail from '@/views/approvalManagement/components/leaveDetail.vue'
  // 油卡充值详情
  import { getDataFuelCardRecharge } from '@/api/administrativeManagement/fuelCardRecharge-api'
  import FuelCardRechargeDetail from '@/views/approvalManagement/components/fuelCardRechargeDetail.vue'
  // 短时外出
  import { getData as getDataTemporaryOuting } from '@/api/approvalManagement/temporaryOuting-api'
  import TemporaryOutingDetail from '@/views/approvalManagement/components/temporaryOutingDetail.vue'
  // 公车申请
  import { getData as getDataOfficialCarDelivery } from '@/api/approvalManagement/busApplication-api'
  import OfficialCarDeliveryDetail from '@/views/approvalManagement/components/busApplicationDetail.vue'
  // 项目车辆维保
  import { getData as getDataVehicleMaintenance } from '@/api/approvalManagement/vehicleMaintenance-api'
  import projectVehicleMaintenanceDetail from '@/views/approvalManagement/vehicleMaintenance/components/projectVehicleMaintenanceDetail.vue'
  // 机关车辆维保
  import departVehicleMaintenanceDetail from '@/views/approvalManagement/vehicleMaintenance/components/departVehicleMaintenanceDetail.vue'

  import { constantsExpose } from '@/utils/constantsExpose'

  export default {
    name: 'ReimburseManagementDetail',
    components: {
      PurchaseApplicationDetail,
      UploadLargeFileFdfsPopupDetail,
      CommonUploadListPopup,
      TravelDetail,
      EntertainApplicationDetail,
      leaveDetail,
      FuelCardRechargeDetail,
      TemporaryOutingDetail,
      OfficialCarDeliveryDetail,
      projectVehicleMaintenanceDetail,
      departVehicleMaintenanceDetail,
    },
    props: {
      budgetList: {
        type: Array,
        default: () => {
          return []
        },
      },
    },
    data() {
      return {
        // 自定义标签样式
        labelStyle: {
          width: '120px',
        },
        size: '',
        type: '',
        isBudget: 1,
        approvalResult: "0",
        dialogDetailVisible: false,
        formLoading: false,
        formData: {
          payeeDetailRow: [],
          applyIdRow: [],
        },
        activeName: 'first',
        dataList: [],
        isApproval: false,
        // 采购申请详情
        showPurchaseApplicationDetail: false,
        // 出差申请详情
        showTravelDetail: false,
        // 招待申请详情
        showEntertainApplicationDetail: false,
        // 调休请假详情
        showLeaveDetail: false,
        // 油卡充值详情
        showFuelCardRechargeDetail: false,
        // 短时外出
        showTemporaryOutingDetail: false,
        // 公车申请
        showOfficialCarDeliveryDetail: false,
        // 项目车辆维保
        showProjectVehicleMaintenanceDetail: false,
        // 机关车辆维保
        showDepartVehicleMaintenanceDetail: false,
      }
    },
    created() {},
    mounted() {},
    methods: {
      async showApprovalFlowByParams(bizKey, row, paramsMap) {
        await this.showDialog(row)
        this.isApproval = true
        this.$nextTick(() => {
          this.$refs.ApprovalFlow.showApprovalFlowByParams(
            bizKey,
            row,
            paramsMap
          )
        })
      },
      callBackRefresh() {
        this.$parent.fetchData()
        this.dialogDetailVisible = false
      },
      async showDialog(row) {
        this.isApproval = false
        this.formData = { ...row, payeeDetailRow: [], applyIdRow: [] }
        this.type = this.formData.type
        this.isBudget = this.formData.isBudget
        this.approvalResult = this.formData.approvalResult
        this.dialogDetailVisible = true
        this.formLoading = true
        await this.getPayeeData()
        await this.getReimburseListing()
        this.formLoading = false
        this.$nextTick(() => {
          if (this.formData.approvalResult !== '0') {
            this.$refs.workFlwApprovalRecDlg.getTaskProcessListByBizKey(
              this.formData.id
            )
          }
          this.$refs.UploadLargeFileFdfsPopupDetail.getParamsData({
            bizId: this.formData.id,
            bizCode: 'reimburseManagement',
            isShow: true,
          })
        })
      },
      // 对项目成本列表中数据根据费用明细管理的列表数据进行排序
      dataListSorting() {
        // 创建一个 Map，存储 费用明细管理的列表数据 中元素的顺序
        const orderMap = new Map()
        this.budgetList[0].children.forEach((item, index) => {
          orderMap.set(item.dictName, index)
        })

        // 根据 orderMap 中的顺序对 项目成本列表中数据 进行排序
        this.dataList.sort((a, b) => {
          const indexA = orderMap.get(a.countName)
          const indexB = orderMap.get(b.countName)
          return indexA - indexB
        })
      },
      // 获取报销对应的收款人、关联申请单
      async getPayeeData() {
        await getPayeeAndApplyData({ reimburseId: this.formData.id }).then(
          (response) => {
            this.formData.payeeDetailRow = response.result.payeeDetailIds
            this.formData.applyIdRow = response.result.applyIds
          }
        )
      },
      // 获取报销预算单\费用单
      async getReimburseListing() {
        await getReimburseListing({
          reimburseManagementId: this.formData.id,
          type: this.formData.type,
          isBudget: this.isBudget,
        }).then((response) => {
          this.dataList = response.result
          // 排序
          this.dataListSorting()
        })
      },
      showUploadFileList(row) {
        const data = {}
        data.bizId = row.id
        data.bizCode = 'reimbursementList'
        data.isShow = false
        this.$refs.CommonUploadListPopup.showUploadListDialog(data)
      },
      // 打开关联申请单详情
      showDetails(item) {
        switch (item.applyType) {
          // 采购
          case 'purchase':
            this.getPurchaseData(item.id)
            break
          // 出差
          case 'evection':
            this.getEvectionData(item.id)
            break
          // 招待
          case 'entertain':
            this.getEntertainData(item.id)
            break
          // 调休请假
          case 'leave':
            this.getLeaveData(item.id)
            break
          // 油卡充值
          case 'fuelCardRecharge':
            this.getFuelCardRechargeData(item.id)
            break
          // 短时外出
          case 'temporaryOuting':
            this.getTemporaryOutingData(item.id)
            break
          // 公车申请
          case 'officialCarDelivery':
            this.getOfficialCarDeliveryData(item.id)
            break
          // 车辆维保
          case 'vehicle_maintenance':
            this.getVehicleMaintenanceData(item.id)
            break
        }
      },
      // 获取采购申请单数据、打开详情页
      async getPurchaseData(id) {
        this.showPurchaseApplicationDetail = true
        await getPurchaseData({
          id: id,
          systemId: constantsExpose.SYSTEM_ID,
        })
          .then((res) => {
            const data = res.result
            this.$nextTick(() => {
              this.$refs.purchaseApplicationDetail.showDialog(data)
            })
          })
          .catch((error) => {
            console.log(error)
          })
      },
      // 获取出差申请单数据、打开详情页
      async getEvectionData(id) {
        this.showTravelDetail = true
        await getDataEvection({
          id: id,
          systemId: constantsExpose.SYSTEM_ID,
        })
          .then((res) => {
            const data = res.result
            this.$nextTick(() => {
              this.$refs.travelDetail.showDialog(data)
            })
          })
          .catch((error) => {
            console.log(error)
          })
      },
      // 获取招待申请单数据、打开详情页
      async getEntertainData(id) {
        this.showEntertainApplicationDetail = true
        await getDataEntertainApplication({
          id: id,
          systemId: constantsExpose.SYSTEM_ID,
        })
          .then((res) => {
            const data = res.result
            this.$nextTick(() => {
              this.$refs.entertainApplicationDetail.showDialog(data)
            })
          })
          .catch((error) => {
            console.log(error)
          })
      },
      // 获取调休请假申请单数据、打开详情页
      async getLeaveData(id) {
        this.showLeaveDetail = true
        await getDataLeave({
          id: id,
          systemId: constantsExpose.SYSTEM_ID,
        })
          .then((res) => {
            const data = res.result
            this.$nextTick(() => {
              this.$refs.leaveDetail.showDialog(data)
            })
          })
          .catch((error) => {
            console.log(error)
          })
      },
      //   获取油卡充值申请单数据、打开详情页
      async getFuelCardRechargeData(id) {
        this.showFuelCardRechargeDetail = true
        await getDataFuelCardRecharge({
          id: id,
          systemId: constantsExpose.SYSTEM_ID,
        })
          .then((res) => {
            const data = res.result
            this.$nextTick(() => {
              this.$refs.fuelCardRechargeDetail.showDialog(data)
            })
          })
          .catch((error) => {
            console.log(error)
          })
      },
      //   短时外出申请单数据、打开详情页
      async getTemporaryOutingData(id) {
        this.showTemporaryOutingDetail = true
        await getDataTemporaryOuting({
          id: id,
          systemId: constantsExpose.SYSTEM_ID,
        })
          .then((res) => {
            const data = res.result
            this.$nextTick(() => {
              this.$refs.temporaryOutingDetail.showDialog(data)
            })
          })
          .catch((error) => {
            console.log(error)
          })
      },
      //   公车申请单数据、打开详情页
      async getOfficialCarDeliveryData(id) {
        this.showOfficialCarDeliveryDetail = true
        await getDataOfficialCarDelivery({
          id: id,
          systemId: constantsExpose.SYSTEM_ID,
        })
          .then((res) => {
            const data = res.result
            this.$nextTick(() => {
              this.$refs.officialCarDeliveryDetail.showDialog(data)
            })
          })
          .catch((error) => {
            console.log(error)
          })
      },
      //   车辆维保单数据、打开详情页
      async getVehicleMaintenanceData(id) {
        await getDataVehicleMaintenance({
          id: id,
          systemId: constantsExpose.SYSTEM_ID,
        }).then((res) => {
          const data = res.result
          if (data.type === 'project') {
            this.showProjectVehicleMaintenanceDetail = true
            this.$nextTick(() => {
              this.$refs.projectVehicleMaintenanceDetail.showDialog(data)
            })
          } else if (data.type === 'depart') {
            this.showDepartVehicleMaintenanceDetail = true
            this.$nextTick(() => {
              this.$refs.departVehicleMaintenanceDetail.showDialog(data)
            })
          }
        })
      },
    },
  }
</script>
<style lang="scss" scoped>
  .detailDialog ::v-deep .el-dialog__body {
    padding-top: 10px;
  }
</style>
