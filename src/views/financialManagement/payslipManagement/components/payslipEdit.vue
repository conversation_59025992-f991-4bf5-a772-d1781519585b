<template>
  <el-dialog
    v-drag
    append-to-body
    center
    :close-on-click-modal="false"
    :title="title"
    top="5vh"
    :visible.sync="dialogFormVisible"
    width="1400px"
    @close="close"
  >
    <el-form
      ref="dataForm"
      v-loading="formLoading"
      :inline="true"
      label-position="right"
      label-width="130px"
      :model="formData"
      :rules="rules"
    >
      <table v-loading="formLoading" class="form-table">
        <tr class="title">
          <td colspan="3">基本信息</td>
        </tr>
        <tr>
          <td>
            <el-form-item label="姓名" prop="userId">
              <t-form-user
                v-model="formData.userId"
                :disabled="!formData.isAdd"
                placeholder="请选择"
                @update-user-info="handleUserInfoUpdate"
              />
            </el-form-item>
          </td>
          <td colspan="2">
            <el-form-item label="手机" prop="telephone">
              <el-input
                v-model="formData.telephone"
                disabled
                readonly
                style="width: 280px"
              />
            </el-form-item>
          </td>
        </tr>
        <tr class="title">
          <td colspan="3">工资</td>
        </tr>
        <tr>
          <td>
            <el-form-item label="工资部分" prop="basicSalary">
              <el-input-number
                v-model="formData.basicSalary"
                controls-position="right"
                :min="0"
                :precision="2"
                :step="0.01"
                style="width: 280px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="绩效工资" prop="performanceSalary">
              <el-input-number
                v-model="formData.performanceSalary"
                controls-position="right"
                :min="0"
                :precision="2"
                :step="0.01"
                style="width: 280px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="岗位工资" prop="positionSalary">
              <el-input-number
                v-model="formData.positionSalary"
                controls-position="right"
                :min="0"
                :precision="2"
                :step="0.01"
                style="width: 280px"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="保密津贴" prop="confidentialityAllowance">
              <el-input-number
                v-model="formData.confidentialityAllowance"
                controls-position="right"
                :min="0"
                :precision="2"
                :step="0.01"
                style="width: 280px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="全勤工资" prop="fullAttendanceSalary">
              <el-input-number
                v-model="formData.fullAttendanceSalary"
                controls-position="right"
                :min="0"
                :precision="2"
                :step="0.01"
                style="width: 280px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="小计" prop="totalSalary">
              <el-input-number
                v-model="formData.totalSalary"
                controls-position="right"
                disabled
                :min="0"
                :precision="2"
                :step="0.01"
                style="width: 280px"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="工资总额" prop="grossSalary">
              <el-input-number
                v-model="formData.grossSalary"
                controls-position="right"
                disabled
                :min="0"
                :precision="2"
                :step="0.01"
                style="width: 280px"
              />
            </el-form-item>
          </td>
        </tr>
        <tr class="title">
          <td colspan="3">其他</td>
        </tr>
        <tr>
          <td>
            <el-form-item label="流量费" prop="trafficFee">
              <el-input-number
                v-model="formData.trafficFee"
                controls-position="right"
                :min="0"
                :precision="2"
                :step="0.01"
                style="width: 280px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="偏远地区/风沙" prop="remoteAreaAllowance">
              <el-input-number
                v-model="formData.remoteAreaAllowance"
                controls-position="right"
                :min="0"
                :precision="2"
                :step="0.01"
                style="width: 280px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="工地津贴" prop="constructionAllowance">
              <el-input-number
                v-model="formData.constructionAllowance"
                controls-position="right"
                :min="0"
                :precision="2"
                :step="0.01"
                style="width: 280px"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="生日" prop="birthdayAllowance">
              <el-input-number
                v-model="formData.birthdayAllowance"
                controls-position="right"
                :min="0"
                :precision="2"
                :step="0.01"
                style="width: 280px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="出差" prop="businessTripAllowance">
              <el-input-number
                v-model="formData.businessTripAllowance"
                controls-position="right"
                :min="0"
                :precision="2"
                :step="0.01"
                style="width: 280px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="电脑" prop="computerAllowance">
              <el-input-number
                v-model="formData.computerAllowance"
                controls-position="right"
                :min="0"
                :precision="2"
                :step="0.01"
                style="width: 280px"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="兼职司机" prop="partTimeDriver">
              <el-input-number
                v-model="formData.partTimeDriver"
                controls-position="right"
                :min="0"
                :precision="2"
                :step="0.01"
                style="width: 280px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="餐补" prop="mealAllowance">
              <el-input-number
                v-model="formData.mealAllowance"
                controls-position="right"
                :min="0"
                :precision="2"
                :step="0.01"
                style="width: 280px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="车辆补贴" prop="vehicleSubsidy">
              <el-input-number
                v-model="formData.vehicleSubsidy"
                controls-position="right"
                :min="0"
                :precision="2"
                :step="0.01"
                style="width: 280px"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="绩效工资" prop="otherPerformanceSalary">
              <el-input-number
                v-model="formData.otherPerformanceSalary"
                controls-position="right"
                :min="0"
                :precision="2"
                :step="0.01"
                style="width: 280px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="其他" prop="other">
              <el-input-number
                v-model="formData.other"
                controls-position="right"
                :min="0"
                :precision="2"
                :step="0.01"
                style="width: 280px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="小计" prop="totalAllowance">
              <el-input-number
                v-model="formData.totalAllowance"
                controls-position="right"
                disabled
                :min="0"
                :precision="2"
                :step="0.01"
                style="width: 280px"
              />
            </el-form-item>
          </td>
        </tr>
        <tr class="title">
          <td colspan="3">扣款</td>
        </tr>
        <tr>
          <td>
            <el-form-item label="绩效工资" prop="performanceDeduction">
              <el-input-number
                v-model="formData.performanceDeduction"
                controls-position="right"
                :min="0"
                :precision="2"
                :step="0.01"
                style="width: 280px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="其他" prop="otherDeductions">
              <el-input-number
                v-model="formData.otherDeductions"
                controls-position="right"
                :min="0"
                :precision="2"
                :step="0.01"
                style="width: 280px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="小计" prop="deductions">
              <el-input-number
                v-model="formData.deductions"
                controls-position="right"
                disabled
                :min="0"
                :precision="2"
                :step="0.01"
                style="width: 280px"
              />
            </el-form-item>
          </td>
        </tr>
        <tr class="title">
          <td colspan="3">五险一金</td>
        </tr>
        <tr>
          <td>
            <el-form-item label="养老保险" prop="pensionInsurance">
              <el-input-number
                v-model="formData.pensionInsurance"
                controls-position="right"
                :min="0"
                :precision="2"
                :step="0.01"
                style="width: 280px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="医疗保险" prop="medicalInsurance">
              <el-input-number
                v-model="formData.medicalInsurance"
                controls-position="right"
                :min="0"
                :precision="2"
                :step="0.01"
                style="width: 280px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="失业保险" prop="unemploymentInsurance">
              <el-input-number
                v-model="formData.unemploymentInsurance"
                controls-position="right"
                :min="0"
                :precision="2"
                :step="0.01"
                style="width: 280px"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="住房公积金" prop="housingFund">
              <el-input-number
                v-model="formData.housingFund"
                controls-position="right"
                :min="0"
                :precision="2"
                :step="0.01"
                style="width: 280px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="小计" prop="totalSocialSecurity">
              <el-input-number
                v-model="formData.totalSocialSecurity"
                controls-position="right"
                disabled
                :min="0"
                :precision="2"
                :step="0.01"
                style="width: 280px"
              />
            </el-form-item>
          </td>
        </tr>

        <tr class="title">
          <td colspan="3">核计</td>
        </tr>
        <tr>
          <td>
            <el-form-item label="代扣个税" prop="withheldTax">
              <el-input-number
                v-model="formData.withheldTax"
                controls-position="right"
                :min="0"
                :precision="2"
                :step="0.01"
                style="width: 280px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="已发" prop="paidAmount">
              <el-input-number
                v-model="formData.paidAmount"
                controls-position="right"
                :min="0"
                :precision="2"
                :step="0.01"
                style="width: 280px"
              />
            </el-form-item>
          </td>
          <td colspan="3">
            <el-form-item label="实发工资" prop="actualSalary">
              <el-input-number
                v-model="formData.actualSalary"
                controls-position="right"
                disabled
                :min="0"
                :precision="2"
                :step="0.01"
                style="width: 280px"
              />
            </el-form-item>
          </td>
        </tr>
      </table>
    </el-form>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
  import { savePayslip } from '@/api/financialManagement/payslipManagement-api'
  import { genUUID } from '@/utils/th_utils.js'
  import { mapGetters } from 'vuex'
  import tableMix from '@/views/mixins/baseTable'
  import TFormUser from '@/vab/components/TFormUser/index.vue'

  export default {
    name: 'PayslipEdit',
    components: { TFormUser },
    mixins: [tableMix],
    props: {},
    data() {
      const validateReg = (rule, value, callback) => {
        const formData = { ...this.formData }
        console.log('value', value)
        // 其他合计
        const totalAllowance =
          formData.trafficFee +
          formData.remoteAreaAllowance +
          formData.constructionAllowance +
          formData.birthdayAllowance +
          formData.businessTripAllowance +
          formData.computerAllowance +
          formData.partTimeDriver +
          formData.mealAllowance +
          formData.vehicleSubsidy +
          formData.otherPerformanceSalary +
          formData.other
        this.formData.totalAllowance = parseFloat(totalAllowance.toFixed(2))
        // 奖金合计
        // const totalBonus =
        //   formData.performanceBonus +
        //   formData.yearEndBonus +
        //   formData.specialContributionBonus +
        //   formData.otherBonus
        // this.formData.totalBonus = parseFloat(totalBonus.toFixed(2))
        // 专项扣除合计
        // const totalDeductions =
        //   formData.childEducation +
        //   formData.continuingEducation +
        //   formData.mortgageInterest +
        //   formData.rent +
        //   formData.infantCare +
        //   formData.elderSupport
        // this.formData.totalDeductions = parseFloat(totalDeductions.toFixed(2))
        // 社保合计
        const totalSocialSecurity =
          formData.pensionInsurance +
          formData.medicalInsurance +
          formData.unemploymentInsurance +
          formData.housingFund
        this.formData.totalSocialSecurity = parseFloat(
          totalSocialSecurity.toFixed(2)
        )
        // 薪资合计
        const totalSalary =
          formData.basicSalary +
          formData.performanceSalary +
          formData.positionSalary +
          formData.confidentialityAllowance +
          formData.fullAttendanceSalary
        this.formData.totalSalary = parseFloat(totalSalary.toFixed(2))
        // 工资总额
        const grossSalary = totalSalary + totalAllowance - formData.deductions
        this.formData.grossSalary = parseFloat(grossSalary.toFixed(2))
        //扣款
        const deductions =
          formData.performanceDeduction + formData.otherDeductions
        this.formData.deductions = parseFloat(deductions.toFixed(2))

        // 实发工资
        const actualSalary =
          grossSalary -
          totalSocialSecurity -
          formData.withheldTax -
          formData.paidAmount
        this.formData.actualSalary = parseFloat(actualSalary.toFixed(2))
        callback()
      }
      return {
        title: '',
        cumulativeInvoice: 0,
        dialogFormVisible: false,
        formLoading: false,
        formData: {
          id: '',
          userId: '',
          userName: '',
          telephone: '',
          basicSalary: 0, //基本工资
          performanceSalary: 0, //绩效工资
          positionSalary: 0, //岗位工资
          technicalSalary: 0, //技术工资
          confidentialityAllowance: 0, //保密津贴
          fullAttendanceSalary: 0, //全勤工资
          totalSalary: 0, //工资合计
          trafficFee: 0, //流量费
          remoteAreaAllowance: 0, //偏远地区/风沙
          constructionAllowance: 0, //工地津贴
          birthdayAllowance: 0, //生日
          businessTripAllowance: 0, //出差
          computerAllowance: 0, //电脑
          partTimeDriver: 0, //兼职司机
          mealAllowance: 0, //餐补
          vehicleSubsidy: 0, //车辆补贴
          otherPerformanceSalary: 0, //其他_绩效工资
          other: 0, //其他
          totalAllowance: 0, //其他_合计
          performanceBonus: 0, //绩效奖
          yearEndBonus: 0, //年终及半年度奖
          specialContributionBonus: 0, //特殊贡献奖
          otherBonus: 0, //其他奖金
          totalBonus: 0, //奖金合计
          performanceDeduction: 0, //扣款_绩效扣款
          otherDeductions: 0, //扣款_其他
          deductions: 0, //扣款
          grossSalary: 0, //工资总额
          pensionInsurance: 0, //养老保险
          medicalInsurance: 0, //医疗保险
          unemploymentInsurance: 0, //失业保险
          housingFund: 0, //住房公积金
          lastMonthAdjustment: 0, //上月调整
          totalSocialSecurity: 0, //社保合计
          childEducation: 0, //子女教育
          continuingEducation: 0, //继续教育
          mortgageInterest: 0, //房贷利息
          rent: 0, //房租
          infantCare: 0, //婴幼儿照护费用
          elderSupport: 0, //赡养父母
          totalDeductions: 0, //专项扣除合计
          withheldTax: 0, //代扣个税
          paidAmount: 0, //已发
          actualSalary: 0, //实发工资
        },
        departTreeData: [],
        defaultProps: {
          children: 'children',
          label: 'label',
        },
        rules: {
          userId: [
            { required: true, message: '姓名为必选', trigger: 'change' },
          ],
          basicSalary: [
            { required: true, message: '基本工资为必填', trigger: 'blur' },
            { validator: validateReg, trigger: 'blur' },
          ], //基本工资
          performanceSalary: [
            { required: true, message: '绩效工资为必填', trigger: 'blur' },
            { validator: validateReg, trigger: 'blur' },
          ], //绩效工资
          positionSalary: [
            { required: true, message: '岗位工资为必填', trigger: 'blur' },
            { validator: validateReg, trigger: 'blur' },
          ], //岗位工资
          technicalSalary: [
            { required: true, message: '技术工资为必填', trigger: 'blur' },
            { validator: validateReg, trigger: 'blur' },
          ], //技术工资
          confidentialityAllowance: [
            { required: true, message: '保密津贴为必填', trigger: 'blur' },
            { validator: validateReg, trigger: 'blur' },
          ], //保密津贴
          fullAttendanceSalary: [
            { required: true, message: '全勤工资为必填', trigger: 'blur' },
            { validator: validateReg, trigger: 'blur' },
          ], //全勤工资
          trafficFee: [
            { required: true, message: '流量费为必填', trigger: 'blur' },
            { validator: validateReg, trigger: 'blur' },
          ], //流量费
          remoteAreaAllowance: [
            { required: true, message: '偏远地区/风沙为必填', trigger: 'blur' },
            { validator: validateReg, trigger: 'blur' },
          ], //偏远地区/风沙
          constructionAllowance: [
            { required: true, message: '工地津贴为必填', trigger: 'blur' },
            { validator: validateReg, trigger: 'blur' },
          ], //工地津贴
          birthdayAllowance: [
            { required: true, message: '生日为必填', trigger: 'blur' },
            { validator: validateReg, trigger: 'blur' },
          ], //生日
          businessTripAllowance: [
            { required: true, message: '出差为必填', trigger: 'blur' },
            { validator: validateReg, trigger: 'blur' },
          ], //出差
          computerAllowance: [
            { required: true, message: '电脑为必填', trigger: 'blur' },
            { validator: validateReg, trigger: 'blur' },
          ], //电脑
          partTimeDriver: [
            { required: true, message: '兼职司机为必填', trigger: 'blur' },
            { validator: validateReg, trigger: 'blur' },
          ], //兼职司机
          mealAllowance: [
            { required: true, message: '餐补为必填', trigger: 'blur' },
            { validator: validateReg, trigger: 'blur' },
          ], //餐补
          performanceBonus: [
            { required: true, message: '绩效奖为必填', trigger: 'blur' },
            { validator: validateReg, trigger: 'blur' },
          ], //绩效奖
          yearEndBonus: [
            {
              required: true,
              message: '年终及半年度奖为必填',
              trigger: 'blur',
            },
            { validator: validateReg, trigger: 'blur' },
          ], //年终及半年度奖
          specialContributionBonus: [
            { required: true, message: '特殊贡献奖为必填', trigger: 'blur' },
            { validator: validateReg, trigger: 'blur' },
          ], //特殊贡献奖
          otherBonus: [
            { required: true, message: '其他奖金为必填', trigger: 'blur' },
            { validator: validateReg, trigger: 'blur' },
          ], //其他奖金
          pensionInsurance: [
            { required: true, message: '养老保险为必填', trigger: 'blur' },
            { validator: validateReg, trigger: 'blur' },
          ], //养老保险
          medicalInsurance: [
            { required: true, message: '医疗保险为必填', trigger: 'blur' },
            { validator: validateReg, trigger: 'blur' },
          ], //医疗保险
          unemploymentInsurance: [
            { required: true, message: '失业保险为必填', trigger: 'blur' },
            { validator: validateReg, trigger: 'blur' },
          ], //失业保险
          housingFund: [
            { required: true, message: '住房公积金为必填', trigger: 'blur' },
            { validator: validateReg, trigger: 'blur' },
          ], //住房公积金
          lastMonthAdjustment: [
            { required: true, message: '上月调整为必填', trigger: 'blur' },
            { validator: validateReg, trigger: 'blur' },
          ], //上月调整
          childEducation: [
            { required: true, message: '子女教育为必填', trigger: 'blur' },
            { validator: validateReg, trigger: 'blur' },
          ], //子女教育
          continuingEducation: [
            { required: true, message: '继续教育为必填', trigger: 'blur' },
            { validator: validateReg, trigger: 'blur' },
          ], //继续教育
          mortgageInterest: [
            { required: true, message: '房贷利息为必填', trigger: 'blur' },
            { validator: validateReg, trigger: 'blur' },
          ], //房贷利息
          rent: [
            { required: true, message: '房租为必填', trigger: 'blur' },
            { validator: validateReg, trigger: 'blur' },
          ], //房租
          infantCare: [
            {
              required: true,
              message: '婴幼儿照护费用为必填',
              trigger: 'blur',
            },
            { validator: validateReg, trigger: 'blur' },
          ], //婴幼儿照护费用
          elderSupport: [
            { required: true, message: '赡养父母为必填', trigger: 'blur' },
            { validator: validateReg, trigger: 'blur' },
          ], //赡养父母
          withheldTax: [
            { required: true, message: '代扣个税为必填', trigger: 'blur' },
            { validator: validateReg, trigger: 'blur' },
          ], //代扣个税
          performanceDeduction: [
            { required: true, message: '绩效扣款为必填', trigger: 'blur' },
            { validator: validateReg, trigger: 'blur' },
          ], //绩效扣款
          paidAmount: [
            { required: true, message: '已发为必填', trigger: 'blur' },
            { validator: validateReg, trigger: 'blur' },
          ], //已发
          vehicleSubsidy: [
            { required: true, message: '车辆补贴为必填', trigger: 'blur' },
            { validator: validateReg, trigger: 'blur' },
          ], //车辆补贴
          otherPerformanceSalary: [
            { required: true, message: '工资部分小计为必填', trigger: 'blur' },
            { validator: validateReg, trigger: 'blur' },
          ], //其他_绩效工资
          other: [
            { required: true, message: '其他为必填', trigger: 'blur' },
            { validator: validateReg, trigger: 'blur' },
          ], //其他_其他
          otherDeductions: [
            { required: true, message: '其他为必填', trigger: 'blur' },
            { validator: validateReg, trigger: 'blur' },
          ], //扣款_其他
        },
      }
    },
    computed: {
      ...mapGetters({
        userId: 'user/userId',
        userName: 'user/userName',
        departList: 'acl/departList',
      }),
    },
    created() {},
    methods: {
      showEdit(row, bizId) {
        if (!row) {
          this.title = '新增工资条'
          this.dialogStatus = 'create'
          this.initForm(bizId)
        } else {
          this.title = '编辑工资条'
          this.dialogStatus = 'update'
          this.formData = Object.assign({ isAdd: false }, row)
        }
        this.dialogFormVisible = true
      },
      // 初始化表单
      initForm(bizId) {
        this.formData = {
          id: genUUID(),
          bizId: bizId,
          userId: '',
          userName: '',
          telephone: '',
          basicSalary: 0, //基本工资
          performanceSalary: 0, //绩效工资
          positionSalary: 0, //岗位工资
          technicalSalary: 0, //技术工资
          confidentialityAllowance: 0, //保密津贴
          fullAttendanceSalary: 0, //全勤工资
          totalSalary: 0, //工资合计
          trafficFee: 0, //流量费
          remoteAreaAllowance: 0, //偏远地区/风沙
          constructionAllowance: 0, //工地津贴
          birthdayAllowance: 0, //生日
          businessTripAllowance: 0, //出差
          computerAllowance: 0, //电脑
          partTimeDriver: 0, //兼职司机
          mealAllowance: 0, //餐补
          vehicleSubsidy: 0, //车辆补贴
          otherPerformanceSalary: 0, //其他_绩效工资
          other: 0, //其他
          totalAllowance: 0, //其他_合计
          performanceBonus: 0, //绩效奖
          yearEndBonus: 0, //年终及半年度奖
          specialContributionBonus: 0, //特殊贡献奖
          otherBonus: 0, //其他奖金
          totalBonus: 0, //奖金合计
          performanceDeduction: 0, //扣款_绩效扣款
          otherDeductions: 0, //扣款_其他
          deductions: 0, //扣款
          grossSalary: 0, //工资总额
          pensionInsurance: 0, //养老保险
          medicalInsurance: 0, //医疗保险
          unemploymentInsurance: 0, //失业保险
          housingFund: 0, //住房公积金
          lastMonthAdjustment: 0, //上月调整
          totalSocialSecurity: 0, //社保合计
          childEducation: 0, //子女教育
          continuingEducation: 0, //继续教育
          mortgageInterest: 0, //房贷利息
          rent: 0, //房租
          infantCare: 0, //婴幼儿照护费用
          elderSupport: 0, //赡养父母
          totalDeductions: 0, //专项扣除合计
          withheldTax: 0, //代扣个税
          paidAmount: 0, //已发
          actualSalary: 0, //实发工资
          isAdd: true,
        }
      },
      save() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            savePayslip(this.formData)
              .then(() => {
                this.$baseMessage(
                  this.formData.isAdd ? '新增成功！' : '修改成功!',
                  'success',
                  'vab-hey-message-success'
                )
                this.close()
                this.$emit('fetch-data')
              })
              .catch(() => {
                this.$baseMessage(
                  this.formData.isAdd ? '新增失败！' : '修改失败!',
                  'error',
                  'vab-hey-message-error'
                )
              })
          }
        })
      },
      close() {
        this.cumulativeInvoice = 0
        this.$refs['dataForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
      },
      async handleUserInfoUpdate(userInfo) {
        if (userInfo.length > 0) {
          const selectedUser = userInfo[0] //单选
          // 更新formData
          this.formData.telephone = selectedUser.telephone
            ? selectedUser.telephone
            : ''
        }
      },
    },
  }
</script>

<style lang="scss" scoped></style>
