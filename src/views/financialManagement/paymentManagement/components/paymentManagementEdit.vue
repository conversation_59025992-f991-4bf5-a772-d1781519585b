<template>
  <el-dialog
    v-drag
    append-to-body
    :close-on-click-modal="false"
    :title="actionMap[dialogStatus]"
    top="1vh"
    :visible.sync="dialogFormVisible"
    width="1420px"
    @close="close"
  >
    <div style="max-height: 75vh; overflow-y: scroll; overflow-x: hidden">
      <el-tabs
        v-model="activeName"
        v-loading="formLoading"
        :before-leave="beforeLeave"
        type="card"
        @tab-click="handleTabClick"
      >
        <el-tab-pane label="基本信息" name="first">
          <el-form
            ref="dataForm"
            v-loading="formLoading"
            :inline="true"
            label-position="right"
            label-width="140px"
            :model="formData"
            :rules="rules"
          >
            <table v-if="selectType === 1" class="form-table">
              <tr>
                <td>
                  <el-form-item label="单号" prop="serialNumber">
                    <el-input
                      v-model="formData.serialNumber"
                      disabled
                      placeholder="自动生成"
                    />
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="申请人" prop="createBy">
                    <t-form-user
                      v-model="formData.createBy"
                      disabled
                      placeholder="请选择"
                    />
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="申请日期" prop="recordDate">
                    <el-date-picker
                      v-model="formData.recordDate"
                      format="yyyy-MM-dd"
                      placeholder="请选择"
                      value-format="yyyy-MM-dd 00:00:00"
                    />
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td>
                  <el-form-item label="所属项目" prop="projectId">
                    <el-input
                      v-model="formData.projectCode"
                      :disabled="!formData.isAdd"
                      placeholder="选择所属项目"
                      style="width: 250px"
                      @click.native="handleProject"
                    />
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="项目名称" prop="projectName">
                    <el-input
                      v-model="formData.projectName"
                      disabled
                      placeholder="自动关联所属项目"
                    />
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="付款事由" prop="paymentReason">
                    <el-input
                      v-model="formData.paymentReason"
                      placeholder="请输入付款事由"
                    />
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td>
                  <el-form-item label="所属部门" prop="flowDepart">
                    <t-form-tree
                      v-model="formData.flowDepart"
                      :default-props="{
                        children: 'children',
                        label: 'departName',
                      }"
                      disabled
                      placeholder="自动关联所属项目"
                      :show-top="false"
                      :tree-data="departTreeData"
                      @change="changeDepart"
                    />
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="付款单位" prop="payerId">
                    <t-form-tree
                      v-model="formData.payerId"
                      :default-props="{
                        children: 'children',
                        label: 'departFullName',
                      }"
                      :show-top="false"
                      :tree-data="payeeIdDepartTreeData"
                    />
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="付款金额(元)" prop="paymentAmount">
                    <el-input
                      v-model="formData.paymentAmount"
                      disabled
                      placeholder=""
                    />
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td>
                  <el-form-item label="付款场景" prop="paymentScenario">
                    <el-select
                      v-model="formData.paymentScenario"
                      filterable
                      placeholder="请选择"
                      @change="changePaymentScenario"
                    >
                      <el-option
                        v-for="item in selectedPaymentScenario"
                        :key="item.id"
                        :label="item.dictName"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="付款日期" prop="paymentTime">
                    <el-date-picker
                      v-model="formData.paymentTime"
                      format="yyyy-MM-dd"
                      placeholder="请选择"
                      value-format="yyyy-MM-dd 00:00:00"
                    />
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="开票金额(元)" prop="invoicedAmount">
                    <el-input
                      v-model.number="formData.invoicedAmount"
                      :disabled="
                        formData.paymentScenario ===
                          '3f3a72d2-8039-4549-83d2-cf0a8e57c3be' ||
                        formData.paymentScenario === ''
                      "
                      placeholder=""
                      type="number"
                    />
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td colspan="3">
                  <el-form-item label="收款人">
                    <div
                      v-if="
                        formData.payeeDetailRow && formData.payeeDetailRow.id
                      "
                      style="width: 700px"
                    >
                      <span style="margin-right: 10px">
                        {{ formData.payeeDetailRow.name }}
                      </span>
                      <span style="margin-right: 10px">
                        {{ formData.payeeDetailRow.bank }}
                      </span>
                      <span style="margin-right: 10px">
                        {{ formData.payeeDetailRow.bankAccount }}
                      </span>
                    </div>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td colspan="3">
                  <el-form-item label="关联合同" prop="applyIds">
                    <el-button
                      :disabled="formData.isAdd === false"
                      icon="el-icon-plus"
                      type="primary"
                      @click="showApplyFormSelect"
                    >
                      选择采购合同
                    </el-button>
                    <div
                      v-if="formData.applyIdRow.length > 0"
                      style="width: 700px"
                    >
                      <span
                        v-for="(item, index) in formData.applyIdRow"
                        :key="item.id"
                      >
                        {{ item.applyNo }}
                        <span
                          v-if="formData.isAdd === true"
                          style="color: #f00; cursor: pointer"
                          @click="clearApplyForm(index)"
                        >
                          <i class="el-icon-delete"></i>
                        </span>
                      </span>
                    </div>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td colspan="3">
                  <el-form-item label="备注" prop="remark">
                    <el-input
                      v-model="formData.remark"
                      maxlength="1000"
                      placeholder="请输入内容"
                      style="width: 1170px"
                      type="textarea"
                    />
                  </el-form-item>
                </td>
              </tr>
            </table>
            <table v-else-if="selectType === 2" class="form-table">
              <tr>
                <td>
                  <el-form-item label="单号" prop="serialNumber">
                    <el-input
                      v-model="formData.serialNumber"
                      disabled
                      placeholder=""
                    />
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="申请人" prop="createBy">
                    <t-form-user
                      v-model="formData.createBy"
                      disabled
                      placeholder="请选择"
                    />
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="申请日期" prop="recordDate">
                    <el-date-picker
                      v-model="formData.recordDate"
                      format="yyyy-MM-dd"
                      placeholder="请选择"
                      value-format="yyyy-MM-dd 00:00:00"
                    />
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td>
                  <el-form-item label="付款事由" prop="paymentReason">
                    <el-input
                      v-model="formData.paymentReason"
                      placeholder="请输入付款事由"
                    />
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="所属部门" prop="depart">
                    <t-form-tree
                      v-model="formData.depart"
                      :default-props="{
                        children: 'children',
                        label: 'departName',
                      }"
                      :show-top="false"
                      :tree-data="departTreeData"
                      @change="changeDepart"
                    />
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="付款单位" prop="payerId">
                    <t-form-tree
                      v-model="formData.payerId"
                      :default-props="{
                        children: 'children',
                        label: 'departFullName',
                      }"
                      :show-top="false"
                      :tree-data="payeeIdDepartTreeData"
                    />
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td>
                  <el-form-item label="付款金额(元)" prop="paymentAmount">
                    <el-input
                      v-model="formData.paymentAmount"
                      disabled
                      placeholder=""
                    />
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="付款场景" prop="paymentScenario">
                    <el-select
                      v-model="formData.paymentScenario"
                      filterable
                      placeholder="请选择"
                      @change="changePaymentScenario"
                    >
                      <el-option
                        v-for="item in selectedPaymentScenario"
                        :key="item.id"
                        :label="item.dictName"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="开票金额(元)" prop="invoicedAmount">
                    <el-input
                      v-model.number="formData.invoicedAmount"
                      :disabled="
                        formData.paymentScenario ===
                          '3f3a72d2-8039-4549-83d2-cf0a8e57c3be' ||
                        formData.paymentScenario === ''
                      "
                      placeholder=""
                      type="number"
                    />
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td v-show="formData.contractFlag === 0" colspan="2">
                  <el-form-item label="收款人" prop="payeeDetailId">
                    <el-button
                      :disabled="!formData.isAdd"
                      icon="el-icon-plus"
                      type="primary"
                      @click="showPayeeDetailDialog"
                    >
                      添加供应商信息
                    </el-button>
                    <div
                      v-if="
                        formData.payeeDetailRow && formData.payeeDetailRow.id
                      "
                      style="width: 700px"
                    >
                      <span style="margin-right: 10px">
                        {{ formData.payeeDetailRow.name }}
                      </span>
                      <span style="margin-right: 10px">
                        {{ formData.payeeDetailRow.bank }}
                      </span>
                      <span style="margin-right: 10px">
                        {{ formData.payeeDetailRow.bankAccount }}
                      </span>
                    </div>
                  </el-form-item>
                </td>
                <td v-show="formData.contractFlag === 1" colspan="2">
                  <el-form-item label="收款人">
                    <div
                      v-if="
                        formData.payeeDetailRow && formData.payeeDetailRow.id
                      "
                      style="width: 700px"
                    >
                      <span style="margin-right: 10px">
                        {{ formData.payeeDetailRow.name }}
                      </span>
                      <span style="margin-right: 10px">
                        {{ formData.payeeDetailRow.bank }}
                      </span>
                      <span style="margin-right: 10px">
                        {{ formData.payeeDetailRow.bankAccount }}
                      </span>
                    </div>
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="付款日期" prop="paymentTime">
                    <el-date-picker
                      v-model="formData.paymentTime"
                      format="yyyy-MM-dd"
                      placeholder="请选择"
                      value-format="yyyy-MM-dd 00:00:00"
                    />
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td colspan="3">
                  <el-form-item label="是否有合同" prop="contractFlag">
                    <el-radio
                      v-model="formData.contractFlag"
                      :disabled="!formData.isAdd"
                      :label="1"
                      @input="contractFlagChange"
                    >
                      有合同
                    </el-radio>
                    <el-radio
                      v-model="formData.contractFlag"
                      :disabled="!formData.isAdd"
                      :label="0"
                      @input="contractFlagChange"
                    >
                      有申请单
                    </el-radio>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td colspan="3">
                  <el-form-item
                    :label="
                      formData.contractFlag === 1 ? '关联合同' : '关联申请单'
                    "
                    prop="applyIds"
                  >
                    <el-button
                      :disabled="formData.isAdd === false"
                      icon="el-icon-plus"
                      type="primary"
                      @click="showApplyFormSelect"
                    >
                      请选择
                    </el-button>
                    <div
                      v-if="formData.applyIdRow.length > 0"
                      style="width: 700px"
                    >
                      <span
                        v-for="(item, index) in formData.applyIdRow"
                        :key="item.id"
                      >
                        {{ item.applyNo }}
                        <span
                          v-if="formData.isAdd === true"
                          style="color: #f00; cursor: pointer"
                          @click="clearApplyForm(index)"
                        >
                          <i class="el-icon-delete"></i>
                        </span>
                      </span>
                    </div>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td colspan="3">
                  <el-form-item label="备注" prop="remark">
                    <el-input
                      v-model="formData.remark"
                      maxlength="1000"
                      placeholder="请输入内容"
                      style="width: 1170px"
                      type="textarea"
                    />
                  </el-form-item>
                </td>
              </tr>
            </table>
            <UploadLargeFileFdfsPopup ref="UploadLargeFileFdfsPopup" />
          </el-form>
        </el-tab-pane>
        <el-tab-pane
          :label="selectType === 1 ? '项目付款单' : '机关付款单'"
          name="second"
        >
          <el-button
            :disabled="dateList.length > 0"
            icon="el-icon-plus"
            style="margin-bottom: 10px; margin-top: 20px"
            type="primary"
            @click="handleAdd"
          >
            导入付款清单
          </el-button>

          <el-table ref="tableSort" border :data="dateList" stripe>
            <el-table-column
              align="center"
              header-align="center"
              label="序号"
              type="index"
              width="80"
            />
            <el-table-column
              align="center"
              header-align="center"
              label="费用项目"
              prop="listingTypeName"
            />
            <el-table-column
              align="center"
              header-align="center"
              label="金额"
              prop="money"
            />
            <el-table-column
              v-if="selectType === 1"
              align="center"
              header-align="center"
              label="剩余预算金额"
              prop="residueAmount"
            />
            <el-table-column
              v-if="selectType === 1"
              align="center"
              header-align="center"
              label="截至本期支付"
              prop="currentPeriodAmount"
            />
            <el-table-column
              v-if="selectType === 1"
              align="center"
              header-align="center"
              label="预算总金额"
              prop="aggregateAmount"
            />
            <el-table-column
              align="center"
              header-align="center"
              label="备注"
              prop="remark"
            />
            <el-table-column align="center" label="操作" width="300">
              <template #default="{ row }">
                <el-button
                  icon="el-icon-edit"
                  style="margin: 0 10px 10px 0 !important"
                  type="primary"
                  @click="handleEdit(row)"
                >
                  编辑
                </el-button>
                <el-button
                  icon="el-icon-link"
                  style="margin: 0 10px 10px 0 !important"
                  type="success"
                  @click="showUploadFileList(row)"
                >
                  附件
                </el-button>
                <el-button
                  :disabled="
                    formData.selectType === 1 || formData.contractFlag === 1
                  "
                  icon="el-icon-delete-solid"
                  style="margin: 0 10px 10px 0 !important"
                  type="danger"
                  @click="handleDelete(row)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </div>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <!--      <el-button type="primary" @click="save">确认</el-button>-->
      <el-button type="primary" @click="saveAndContinue">
        {{ activeName === 'second' ? '确认' : '保存并继续' }}
      </el-button>
    </template>
    <supplierFormSelect
      ref="supplierFormSelect"
      @confirm="confirmPayeeDetail"
    />
    <paymentManagementListingEdit
      ref="paymentManagementListingEdit"
      :project-id="formData.projectId"
      :select-type="selectType"
      @change="changePaymentListingImport"
    />
    <paymentManagementListingImport
      ref="paymentManagementListingImport"
      :project-id="formData.projectId"
      :select-type="selectType"
      @change="changePaymentListingImport"
    />
    <CommonUploadListPopup ref="CommonUploadListPopup" />
    <paymentManagementApplyFormSelect
      ref="paymentManagementApplyFormSelect"
      @confirm="confirmApplyForm"
    />
    <ProjectListPopup
      ref="projectListPopupRef"
      @getProjectInfo="getProjectInfo"
    />
  </el-dialog>
</template>

<script>
  import { mapGetters } from 'vuex'
  import moment from 'moment'
  import {
    getPayeeAndApplyData,
    saveData,
  } from '@/api/financialManagement/paymentManagement'
  import {
    deleteData,
    getDataList as getPaymentListing,
  } from '@/api/financialManagement/paymentManagementListing'
  import { genUUID } from '@/utils/th_utils.js'
  import { getProjectList } from '@/api/project'
  import { getDepartList } from '@/api/system/depart-api'
  import { getDataById as getPayeeData } from '@/api/provider/providerInfo'
  import UploadLargeFileFdfsPopup from '@/views/common/UploadLargeFileFdfsPopup.vue'
  import paymentManagementListingEdit from '../../components/paymentManagementListingEdit.vue'
  import paymentManagementListingImport from '../../components/paymentManagementListingImport.vue'
  import CommonUploadListPopup from '@/views/common/CommonUploadListPopup.vue' //附件
  import { getDictList } from '@/api/system/dict-api'
  import paymentManagementApplyFormSelect from '@/views/financialManagement/components/paymentManagementApplyFormSelect.vue'
  import TFormTree from '@/vab/components/TFormTree/index.vue'
  import TFormUser from '@/vab/components/TFormUser/index.vue'
  import supplierFormSelect from './supplierFormSelect.vue'
  import ProjectListPopup from '@/views/common/ProjectListPopup.vue'

  export default {
    name: 'PaymentManagementEdit',
    components: {
      ProjectListPopup,
      TFormUser,
      TFormTree,
      paymentManagementApplyFormSelect,
      supplierFormSelect,
      paymentManagementListingEdit,
      paymentManagementListingImport,
      UploadLargeFileFdfsPopup,
      CommonUploadListPopup,
    },
    props: {},
    data() {
      return {
        dialogFormVisible: false,
        formLoading: false,
        activeName: 'first',
        formData: {
          applyIdRow: [],
        },
        actionMap: {
          update: '编辑付款',
          create: '新增付款',
        },
        dialogStatus: '',
        selectedPaymentScenario: [],
        projectDataList: [],
        departTreeData: [],
        payeeIdDepartTreeData: [],
        listLoading: false,
        rules: {
          projectId: [
            { required: true, message: '所属项目为必填', trigger: 'change' },
          ],
          depart: [
            {
              required: true,
              message: '费用所属部门为必填',
              trigger: 'change',
            },
          ],
          payeeDetailId: [
            {
              required: true,
              message: '收款人为必选',
              trigger: 'blur',
            },
          ],
          applyIds: [
            {
              required: true,
              type: 'array',
              message: '申请单为必选',
              trigger: 'change',
            },
          ],
          paymentReason: [
            {
              required: true,
              message: '付款事由为必填',
              trigger: 'blur',
            },
          ],
          payerId: [
            {
              required: true,
              message: '付款单位为必选',
              trigger: 'change',
            },
          ],
          paymentScenario: [
            {
              required: true,
              message: '付款场景为必填',
              trigger: 'blur',
            },
          ],
          paymentTime: [
            {
              required: true,
              message: '付款日期为必填',
              trigger: 'blur',
            },
          ],
          recordDate: [
            {
              required: true,
              message: '申请日期为必填',
              trigger: 'blur',
            },
          ],
          invoicedAmount: [
            {
              required: true,
              message: '开票金额为必填',
              trigger: 'blur',
            },
          ],
        },
        dateList: [],
        // 类型（1.项目型，2部门机关）
        selectType: null,
      }
    },
    computed: {
      ...mapGetters({
        userId: 'user/userId',
        userName: 'user/userName',
        departList: 'user/departList',
      }),
    },
    mounted() {
      this.activeName = 'first'
    },
    async created() {
      this.getProjectList()
      this.getDepartList()
      this.getDictDetails()
    },
    methods: {
      getProjectList() {
        getProjectList({}).then((response) => {
          this.projectDataList = response.result
        })
      },
      getDepartList() {
        getDepartList({}).then((response) => {
          this.departTreeData = response.result
          this.payeeIdDepartTreeData = this.departTreeData.filter(
            (departInfo) => {
              return departInfo.departType.indexOf('2') === -1
            }
          )
        })
      },
      getDictDetails() {
        getDictList({ dictCode: 'paymentScenario' }).then((response) => {
          this.selectedPaymentScenario = response.result[0].children
        })
      },
      beforeLeave() {
        if (this.formData.isAdd === true) {
          this.$message({
            message: '请先保存付款基本信息',
            type: 'warning',
          })
          return false
        } else {
          return true
        }
      },
      handleTabClick(tab) {
        switch (tab.name) {
          case 'first':
            this.getPayeeData()
            break
          case 'second':
            this.getPaymentListing()
            break
        }
      },
      // 保存下一步
      saveAndContinue() {
        switch (this.activeName) {
          case 'first':
            this.save()
            break
          case 'second':
            this.closeDialog()
            break
        }
      },
      // 保存后关闭弹窗
      closeDialog() {
        this.close()
      },
      showApplyFormSelect() {
        if (this.selectType === 1 && this.formData.projectId === '') {
          this.$message({
            message: '请选择项目!',
            type: 'error',
          })
          return
        } else if (this.selectType === 2 && this.formData.depart === '') {
          this.$message({
            message: '请选择部门!',
            type: 'error',
          })
          return
        }
        this.$refs.paymentManagementApplyFormSelect.showDialog(
          this.selectType === 1
            ? 'purchase'
            : this.selectType === 2
            ? 'organ_purchase'
            : '',
          this.formData.applyIds,
          '',
          this.selectType === 1 ? this.formData.projectId : '',
          this.selectType === 2 ? this.formData.depart : '',
          this.formData.contractFlag
        )
      },
      // 切换是否存在申请单
      contractFlagChange(e) {
        this.$refs.dataForm.clearValidate(['payeeDetailId'])
        if (this.formData.applyIdRow.length > 0) {
          // 当前存在申请单，但切换为没有申请单的状态：则清空申请单
          this.$confirm(
            '当前已选择' +
              (this.formData.contractFlag === 1 ? '合同' : '申请单') +
              '，是否清空' +
              (this.formData.contractFlag === 1 ? '申请单?' : '合同?'),
            '提示',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            }
          )
            .then(() => {
              this.formData.applyIdRow = []
              this.formData.applyIds = []
            })
            .catch(() => {
              switch (e) {
                case 1:
                  this.formData.contractFlag = 0
                  break
                case 0:
                  this.formData.contractFlag = 1
                  break
              }
            })
        }
      },
      // 选择合同
      confirmApplyForm(row) {
        if (row && row.id) {
          this.formData.applyIds = []
          this.formData.applyIdRow = []
          this.formData.applyIds.push(row.id)
          this.formData.applyIdRow.push(row)
          if (this.formData.contractFlag === 1) {
            this.formData.payeeDetailId = row.payeeDetailId
            this.formData.payeeDetailRow = { ...row }
          }
        }
        // 使用 this.$refs.form.validateField 方法校验字段
        this.$refs.dataForm.validateField('applyIds', () => {})
      },
      clearApplyForm(id) {
        this.formData.applyIds.splice(id, 1)
        this.formData.applyIdRow.splice(id, 1)
      },
      // 显示收款人弹窗 后改为从付款单中取 收款人 暂时注释保留
      // showPayeeDetailDialog() {
      //   this.$refs.supplierFormSelect.showDialog(this.formData.payeeDetailRow)
      // },
      showPayeeDetailDialog() {
        this.$refs.supplierFormSelect.showDialog(this.formData.payeeDetailId)
      },
      confirmPayeeDetail(row) {
        this.formData.payeeDetailId = row.id
        this.formData.payeeDetailRow = { ...row }
        // 使用 this.$refs.form.validateField 方法校验字段
        this.$refs.dataForm.validateField('payeeDetailId', () => {})
      },
      showUploadFileList(row) {
        const data = {}
        data.bizId = row.id
        data.bizCode = 'paymentManagementListing'
        this.$refs.CommonUploadListPopup.showUploadListDialog(data)
      },
      handleProject() {
        this.$refs.projectListPopupRef.showDialog({
          selectIds: this.formData.projectId,
          projectName: this.formData.projectName,
        })
      },
      getProjectInfo(data) {
        if (data.length > 0 && data[0].id !== this.formData.projectId) {
          const dataObj = data[0]
          const {
            id,
            projectName,
            serialNumber,
            depart,
            departCode,
            departName,
          } = dataObj
          this.formData = {
            ...this.formData,
            projectId: id,
            projectName,
            projectCode: serialNumber,
            flowDepart: depart,
            departCode: departCode,
            departName: departName,
          }
          this.$refs.dataForm.clearValidate('projectCode')
        }
      },
      changeDepart(depart) {
        for (const item of this.departTreeData) {
          if (item.id === depart) {
            this.formData.departCode = item.departCode
          }
        }
      },
      changePaymentScenario(paymentScenario) {
        switch (paymentScenario) {
          // 未到票付款
          case '3f3a72d2-8039-4549-83d2-cf0a8e57c3be':
            this.formData.invoicedAmount = 0
            break
          // 全部到票付款
          case '197ec491-2398-467d-8813-e9578fb8ea58':
            this.formData.invoicedAmount = this.formData.paymentAmount
            break
        }
      },
      handleAdd() {
        this.$refs['paymentManagementListingImport'].showDialog(
          this.formData.id,
          this.formData.projectId,
          this.formData.depart,
          this.formData.applyIds[0],
          this.formData.payeeDetailRow
        )
      },
      handleEdit(row) {
        this.$refs['paymentManagementListingEdit'].showEdit(
          this.selectType,
          row,
          false
        )
      },
      handleDelete(row) {
        this.$baseConfirm('你确定要删除当前付款清单吗', null, async () => {
          deleteData({ id: row.id })
            .then(() => {
              this.getPaymentListing()
              this.$nextTick(() => {
                if (this.dateList || this.dateList.length === 0) {
                  this.formData.payeeDetailId = ''
                }
              })
              this.$baseMessage(
                '删除成功!',
                'success',
                'vab-hey-message-success'
              )
            })
            .catch(() => {
              this.$baseMessage('删除失败!', 'error', 'vab-hey-message-error')
            })
          await this.getPaymentListing()
          this.sumPaymentAmount()
        })
      },
      changePaymentListingImport() {
        this.getPaymentListing()
      },
      sumPaymentAmount() {
        let paymentAmount = 0
        this.dateList.forEach((item) => {
          paymentAmount = paymentAmount + parseFloat(item.money)
        })
        this.formData.paymentAmount = paymentAmount
      },
      async getPayeeData() {
        await getPayeeData({ id: this.formData.payeeDetailId })
          .then((response) => {
            this.formData.payeeDetailRow = response.result
          })
          .catch((err) => {
            console.log('getPayeeData', err)
          })
      },
      async getPaymentListing() {
        await getPaymentListing({
          paymentManagementId: this.formData.id,
          projectId: this.formData.projectId,
          selectType: this.selectType,
          purchaseId: this.formData.applyIds[0],
        }).then((response) => {
          this.dateList = response.result
          this.$nextTick(() => {
            if (this.dateList && this.dateList.length > 0) {
              this.formData.paymentAmount = this.dateList.reduce(
                (sum, item) => sum + (item.money || 0),
                0
              )
            }
          })
        })
      },
      async showEdit(selectType, row) {
        this.selectType = selectType
        if (!row) {
          this.dialogStatus = 'create'
          this.initForm()
        } else {
          this.dialogStatus = 'update'

          this.formData = {
            ...row,
            payeeDetailRow: {},
            applyIdRow: [],
            applyIds: [],
            isAdd: false,
          }
          this.formLoading = true
          this.getPayeeAndApplyData(this.formData.id)
          await this.getPayeeData()
          // await this.getPaymentListing()
          this.formLoading = false
        }
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs.UploadLargeFileFdfsPopup.getParamsData({
            bizId: this.formData.id,
            bizCode: 'paymentManagement',
          })
          this.$refs['dataForm'].clearValidate()
        })
      },
      // 初始化表单
      initForm() {
        this.formData = {
          id: genUUID(),
          createBy: this.userId,
          viewUser: this.userId,
          recordDate: moment().format('YYYY-MM-DD 00:00:00'),
          projectId: '',
          projectCode: '',
          paymentReason: '',
          depart: '',
          flowDepart: '',
          payerId: '',
          paymentAmount: 0,
          paymentScenario: '',
          paymentTime: moment().format('YYYY-MM-DD 00:00:00'),
          payeeDetailId: '',
          payeeDetailRow: {},
          providerInfoId: '',
          remark: '',
          selectType: this.selectType,
          applyIdRow: [],
          applyIds: [],
          invoicedAmount: 0,
          serialNumberTable: 'payment_management',
          operationCode: this.selectType === 1 ? 'XMFK' : 'JGFK',
          createDepart: this.departList[0].id,
          createDepartName: this.departList[0].departName,
          contractFlag: 1,
          isAdd: true,
        }
      },
      // 获取当前报销信息的收款人、清单
      getPayeeAndApplyData(id) {
        getPayeeAndApplyData({ paymentId: id }).then((res) => {
          // this.formData.payeeDetailRow = res.result.payeeDetailIds
          // this.formData.payeeDetailIds = []
          // this.formData.payeeDetailRow.forEach(item => {
          //   this.formData.payeeDetailIds.push(item.id)
          // })
          this.formData.applyIdRow = []
          this.formData.applyIds = []
          if (res.result.applyIds[0]) {
            this.formData.applyIdRow = res.result.applyIds
            this.formData.applyIdRow.forEach((item) => {
              this.formData.applyIds.push(item.id)
            })
          }
        })
      },
      save() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            let data = { ...this.formData }
            saveData(data)
              .then(() => {
                this.$baseMessage(
                  this.formData.isAdd ? '新增成功！' : '修改成功!',
                  'success',
                  'vab-hey-message-success'
                )
                if (this.activeName === 'second') {
                  this.closeDialog()
                }
                this.formData.isAdd = false
                this.getPaymentListing()
                this.activeName = 'second'
              })
              .catch(() => {
                this.$baseMessage(
                  this.formData.isAdd ? '新增失败！' : '修改失败!',
                  'error',
                  'vab-hey-message-error'
                )
              })
          }
        })
      },
      close() {
        this.$emit('fetch-data')
        this.$refs['dataForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dateList = []
        this.activeName = 'first'
        this.dialogFormVisible = false
      },
    },
  }
</script>

<style lang="scss" scoped>
  .dropTree {
    width: 100%;
    max-height: 300px;
    overflow: auto;
  }
</style>
