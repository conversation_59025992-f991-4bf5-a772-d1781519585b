<template>
  <el-dialog
    v-drag
    append-to-body
    :close-on-click-modal="false"
    title="导入预算管理信息"
    top="10vh"
    :visible.sync="dialogFormVisible"
    width="1200px"
    @close="close"
  >
    <el-form
      ref="form"
      :inline="true"
      :model="queryForm"
      @submit.native.prevent
    >
      <el-form-item label="所属项目" prop="projectName">
        <el-input
          v-model="queryForm.projectName"
          clearable
          placeholder="请输入所属项目"
        />
      </el-form-item>
      <el-form-item label="所属部门" prop="departName">
        <el-input
          v-model="queryForm.departName"
          clearable
          placeholder="请输入所属部门"
        />
      </el-form-item>

      <el-form-item>
        <el-button
          icon="el-icon-search"
          native-type="submit"
          type="primary"
          @click="handleQuery"
        >
          查询
        </el-button>
        <el-button
          icon="el-icon-refresh-right"
          @click.native="resetForm('form')"
        >
          重置
        </el-button>
      </el-form-item>
    </el-form>
    <el-table
      ref="tableSort"
      v-loading="listLoading"
      border
      :data="list"
      :max-height="450"
      :size="lineHeight"
      stripe
      @selection-change="setSelectRows"
    >
      <el-table-column v-if="!multiple" align="center" label="选择" width="55">
        <template #default="{ row }">
          <el-radio
            v-model="radio"
            :label="row.id"
            @change.native="getCurrentRow(row)"
          >
            {{ '' }}
          </el-radio>
        </template>
      </el-table-column>
      <el-table-column v-else align="center" type="selection" width="55" />
      <el-table-column
        align="center"
        label="单号"
        prop="serialNumber"
        width="240"
      />
      <el-table-column
        align="center"
        label="项目名称"
        prop="projectName"
        width="240"
      />
      <el-table-column align="center" label="项目所属部门" prop="departName" />
      <el-table-column
        align="center"
        label="预计合同金额(元)"
        prop="budgetAmount"
      >
        <template #default="{ row }">
          {{ row.contractTotalAmount.contractAmount }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="项目负责人"
        prop="projectPrincipalName"
        width="120"
      />
    </el-table>
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />

    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
  import tableMix from '@/views/mixins/table'
  import { getDataListByPage } from '@/api/financialManagement/budgetManagement'

  export default {
    name: 'BudgetManagementImport',
    components: {},
    mixins: [tableMix],
    props: {
      multiple: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        dialogFormVisible: false,
        projectDataList: [],
        queryForm: {
          dataType: 'budget',
          approvalResult: '2',
          projectName: '',
          departName: '',
        },
        radio: '',
        // 选中的数据、id
        selectRow: null,
        selectedId: '',
      }
    },
    watch: {},
    async created() {},
    methods: {
      showDialog(payeeDetailId) {
        this.selectedId = payeeDetailId
        this.fetchData()
        this.dialogFormVisible = true
      },
      fetchData() {
        this.listLoading = true
        const queryForm = {
          ...this.queryForm,
          curPage: this.pageInfo.curPage,
          pageSize: this.pageInfo.pageSize,
        }

        getDataListByPage(queryForm).then((res) => {
          this.listLoading = false
          const {
            result: { total, records },
          } = res
          this.list = records
          this.initSelectRow()
          this.pageInfo.total = Number(total)
        })
      },
      // 初始化选中状态
      initSelectRow() {
        if (this.multiple) {
          // 多选情况
          const selected = this.list.filter((item) =>
            this.selectedId.includes(item.id)
          )
          this.$nextTick(() => {
            selected.forEach((row) => {
              this.$refs.tableSort.toggleRowSelection(row, true)
            })
          })
        } else {
          this.selectRow = ''
          // 单选情况 初始化选中状态及初始数据
          this.radio = this.selectedId
          this.list.forEach((item) => {
            if (item.id === this.selectedId) {
              this.selectRow = item
            }
          })
        }
      },
      // 获取选中数据(多选\单选）
      setSelectRows(val) {
        this.selectRow = val
      },
      getCurrentRow(row) {
        //获取选中数据(单选）
        this.selectRow = row
      },
      save() {
        if (
          (!this.selectRow.length > 0 && this.multiple) ||
          (!this.selectRow && !this.multiple)
        ) {
          return
        }
        this.$emit('confirm', this.selectRow)
        this.$emit('change', this.selectRow)
        this.close()
      },
      close() {
        this.queryForm = this.$options.data().queryForm
        this.pageInfo = this.$options.data().pageInfo
        this.selectRow = this.$options.data().selectRow
        this.radio = this.$options.data().radio
        this.dialogFormVisible = false
      },
    },
  }
</script>

<style lang="scss" scoped>
  .dropTree {
    width: 100%;
    max-height: 300px;
    overflow: auto;
  }
</style>
