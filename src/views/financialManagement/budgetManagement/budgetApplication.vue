<template>
  <div
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="80px"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item label="所属项目" prop="projectName">
            <el-input
              v-model="queryForm.projectName"
              clearable
              placeholder="请输入所属项目"
            />
          </el-form-item>
          <el-form-item label="所属部门" prop="departName">
            <el-input
              v-model="queryForm.departName"
              clearable
              placeholder="请输入所属部门"
            />
          </el-form-item>
          <el-form-item
            label="项目负责人"
            label-width="92px"
            prop="projectPrincipalName"
          >
            <el-input
              v-model="queryForm.projectPrincipalName"
              placeholder="请输入项目负责人"
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="审批状态" prop="approvalResult">
            <el-select
              ref="selectFlow"
              v-model="queryForm.approvalResult"
              placeholder="请选择审批状态"
            >
              <el-option option value="">所有状态</el-option>
              <el-option
                v-for="item in approvalResultNameList"
                :key="item.id"
                :label="item.approvalResultName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item v-show="!fold" label="申请人" prop="createByName">
            <el-input
              v-model="queryForm.createByName"
              placeholder="请输入申请人"
              style="width: 200px"
            />
          </el-form-item>

          <el-form-item>
            <el-button
              icon="el-icon-search"
              native-type="submit"
              type="primary"
              @click="handleQuery"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-right"
              @click.native="resetForm('form')"
            >
              重置
            </el-button>
          </el-form-item>
          <el-button
            style="margin: 0 0 0 10px !important"
            type="text"
            @click="handleFold"
          >
            <span v-if="fold">展开</span>
            <span v-else>合并</span>
            <vab-icon
              class="vab-dropdown"
              :class="{ 'vab-dropdown-active': fold }"
              icon="arrow-up-s-line"
            />
          </el-button>
        </el-form>
      </vab-query-form-top-panel>
      <vab-query-form-left-panel :span="12">
        <el-button
          v-permissions="{ permission: ['budgetApplication:add'] }"
          icon="el-icon-plus"
          type="primary"
          @click="handleAdd"
        >
          添加
        </el-button>
        <!--        </el-button>-->
        <!--        <el-button-->
        <!--          :disabled="exportLoading"-->
        <!--          icon="el-icon-refresh-right"-->
        <!--          type="warning"-->
        <!--          @click.native="exportBtn"-->
        <!--        >-->
        <!--          导出-->
        <!--        </el-button>-->
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="12">
        <el-button
          style="margin: 0 10px 10px 0 !important"
          type="primary"
          @click="clickFullScreen"
        >
          <vab-icon
            :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'"
          />
          表格全屏
        </el-button>
        <el-popover
          ref="popover"
          popper-class="custom-table-checkbox"
          trigger="hover"
        >
          <el-radio-group v-model="lineHeight">
            <el-radio label="medium">大</el-radio>
            <el-radio label="small">中</el-radio>
            <el-radio label="mini">小</el-radio>
          </el-radio-group>
          <template #reference>
            <el-button style="margin: 0 10px 10px 0 !important" type="primary">
              <vab-icon icon="line-height" />
              表格尺寸
            </el-button>
          </template>
        </el-popover>
        <el-popover popper-class="custom-table-checkbox" trigger="hover">
          <el-checkbox-group v-model="checkList">
            <vab-draggable v-bind="dragOptions" :list="columns">
              <div v-for="(item, index) in columns" :key="item + index">
                <vab-icon icon="drag-drop-line" />
                <el-checkbox
                  :disabled="item.disableCheck === true"
                  :label="item.label"
                >
                  {{ item.label }}
                </el-checkbox>
              </div>
            </vab-draggable>
          </el-checkbox-group>
          <template #reference>
            <el-button
              icon="el-icon-setting"
              style="margin: 0 0 10px 0 !important"
              type="primary"
            >
              可拖拽列设置
            </el-button>
          </template>
        </el-popover>
      </vab-query-form-right-panel>
    </vab-query-form>
    <el-table
      ref="tableSort"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      :size="lineHeight"
      stripe
      @selection-change="setSelectRows"
    >
      <el-table-column align="center" label="序号" width="60">
        <template #default="{ $index }">
          {{ getIndex($index) }}
        </template>
      </el-table-column>
      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        :align="item.center ? item.center : 'center'"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'"
      >
        <template #default="{ row }">
          <span
            v-if="item.prop === 'startTime' || item.prop === 'expirationTime'"
          >
            {{ row[item.prop] | dateformat('YYYY-MM') }}
          </span>
          <!--  审批状态 -->
          <span v-else-if="item.label === '审批状态'">
            <el-tag :type="fmtFlowType(row)">
              {{ row[item.prop] }}
            </el-tag>
          </span>
          <span v-else-if="item.prop === 'budgetAmount'">
            {{ row.contractTotalAmount.contractAmount | currencyFormat }}
          </span>
          <span v-else-if="item.prop === 'grossProfit'">
            {{ row.grossProfit | currencyFormat }}
          </span>
          <span v-else-if="item.prop === 'netProfit'">
            {{ row.netProfit | currencyFormat }}
          </span>
          <span v-else>
            {{ row[item.prop] }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" fixed="right" label="操作" width="320">
        <template #default="{ row }">
          <el-button
            v-permissions="{ permission: ['budgetApplication:update'] }"
            :disabled="isDisabledEditFun(row)"
            icon="el-icon-edit"
            style="margin: 0 10px 10px 0 !important"
            type="primary"
            @click="handleEdit(row)"
          >
            编辑
          </el-button>
          <el-button
            v-if="isAssignee(row)"
            icon="el-icon-edit-outline"
            style="margin: 0 10px 10px 0 !important"
            type="warning"
            @click.native.prevent="showApprovalDlg(row)"
          >
            审批
          </el-button>
          <el-button
            icon="el-icon-view"
            style="margin: 0 10px 10px 0 !important"
            type="success"
            @click="handleDetail(row)"
          >
            详情
          </el-button>
          <el-dropdown>
            <el-button size="mini" type="success">
              <i class="el-icon-more" />
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-if="isStartFlowFlag(row)"
                @click.native.prevent="startWorkFlow(row)"
              >
                <i class="el-icon-video-play" />
                启动流程
              </el-dropdown-item>
              <el-dropdown-item
                v-if="isDeleteFlowFlag(row)"
                @click.native.prevent="deleteWorkFlow(row)"
              >
                <i class="el-icon-refresh-left" />
                撤回流程
              </el-dropdown-item>
              <el-dropdown-item
                v-if="isDeleteFlag(row)"
                v-permissions="{ permission: ['budgetApplication:del'] }"
                divided
                @click.native.prevent="handleDelete(row)"
              >
                <span style="color: #fd5353">
                  <i class="el-icon-delete-solid" />
                  删除
                </span>
              </el-dropdown-item>
              <el-dropdown-item divided @click.native.prevent="showReport(row)">
                <i class="el-icon-s-order" />
                查看报表
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <table-detail ref="tableDetail" />
    <table-edit ref="tableEdit" @fetch-data="fetchData" />
    <budgetListing-edit ref="budgetListingEdit" @fetch-data="fetchData" />
    <!-- 部门选择 -->
    <!--    <VabWorkFlowDepart ref="VabWorkFlowDepart"  />-->
    <!-- 流程启动动态审批条件处理 -->
    <VabStartFlowProcess ref="VabStartFlowProcess" />
  </div>
</template>

<script>
  import moment from 'moment'
  import tableMix from '@/views/mixins/table'
  import tableDetail from './components/budgetApplicationDetail.vue'
  import tableEdit from './components/budgetApplicationEdit.vue'
  import budgetListingEdit from './components/budgetListingEdit.vue'
  import {
    getDataListByPage,
    deleteData,
    getDataList,
    exportBudgetManagementInformation,
  } from '@/api/financialManagement/budgetManagement'
  import { reportAddr } from '@/utils/constants'

  export default {
    name: 'BudgetApplication',
    components: {
      tableDetail,
      tableEdit,
      budgetListingEdit,
    },
    mixins: [tableMix],
    data() {
      return {
        formMaxHeight: 2,
        hasCard: true,
        columns: [
          {
            label: '单号',
            prop: 'serialNumber',
            width: '250',
          },
          {
            label: '项目名称',
            prop: 'projectName',
            width: '320',
          },
          {
            label: '项目所属部门',
            prop: 'departName',
            width: '140',
          },
          {
            label: '预计合同金额(元)',
            prop: 'budgetAmount',
            width: '160',
          },
          {
            label: '负责人',
            prop: 'projectPrincipalName',
          },
          {
            label: '毛利润（元）',
            prop: 'grossProfit',
            width: '160',
          },
          {
            label: '净利润（元）',
            prop: 'netProfit',
            width: '160',
          },
          {
            label: '审批状态',
            prop: 'approvalResultName',
            width: '100',
          },
        ],
        queryForm: {
          dataType: 'budget',
          projectName: '',
          departName: '',
          projectPrincipalName: '',
          budgetAmount: '',
          createByName: '',
        },
        // 已有预算的项目id
        projectIdList: [],
      }
    },
    computed: {},
    created() {
      this.initDefaultCheck()
      this.fetchData()
    },
    methods: {
      // 查看报表
      showReport(data) {
        const params = '&id=' + data.id
        const url =
          reportAddr +
          '/ReportServer?reportlet=/oaNew/budgement/budgetApplication.cpt' +
          params
        window.open(url)
      },
      fetchData() {
        this.listLoading = true

        const queryForm = {
          ...this.queryForm,
          curPage: this.pageInfo.curPage,
          pageSize: this.pageInfo.pageSize,
        }

        getDataListByPage(queryForm).then((res) => {
          this.listLoading = false
          const {
            result: { total, records },
          } = res
          this.list = records
          this.pageInfo.total = Number(total)
        })
      },
      handleEdit(row) {
        this.$refs['tableEdit'].showEdit(row)
      },
      handleAdd() {
        this.$refs['tableEdit'].showEdit()
      },
      handleDetail(row) {
        this.$refs['tableDetail'].showDialog(row)
      },
      handleDelete(row) {
        if (row.id) {
          this.$baseConfirm('你确定要删除当前预算吗', null, async () => {
            deleteData({ id: row.id })
              .then(() => {
                this.fetchData()
                this.$baseMessage(
                  '删除成功!',
                  'success',
                  'vab-hey-message-success'
                )
              })
              .catch(() => {
                this.$baseMessage('删除失败!', 'error', 'vab-hey-message-error')
              })
          })
        }
      },

      // 批删除
      batchDelete() {
        if (this.selectRows.length === 0) {
          this.$baseMessage(
            '请选中最少一条记录!',
            'error',
            'vab-hey-message-error'
          )
          return
        }
        const idArr = this.selectRows.map((item) => item.id).join(',')
        this.$baseConfirm('你确定要删除选中预算吗', null, async () => {
          deleteData({ id: idArr })
            .then(() => {
              this.fetchData()
              this.$baseMessage(
                '删除成功!',
                'success',
                'vab-hey-message-success'
              )
            })
            .catch(() => {
              this.$baseMessage('删除失败!', 'error', 'vab-hey-message-error')
            })
        })
      },
      exportBtn() {
        this.exportLoading = true
        getDataList(this.queryForm).then((response) => {
          let exportDataList = response.result.map((item, index) => {
            return {
              numericalOrder: index + 1,
              departName: item.departName,
              projectName: item.projectName,
              projectPrincipalName: item.projectPrincipalName,
              budgetAmount: item.budgetAmount,
              startTime:
                item.startTime !== '' && item.startTime != null
                  ? moment(item.startTime).format('YYYY-MM')
                  : '',
              expirationTime:
                item.expirationTime !== '' && item.expirationTime != null
                  ? moment(item.expirationTime).format('YYYY-MM')
                  : '',
              remark: item.remark,
            }
          })

          if (!exportDataList.length) {
            this.exportLoading = false
            return
          }
          const data = {
            list: exportDataList,
          }
          exportBudgetManagementInformation(data).then((response) => {
            this.exportLoading = false
            const link = document.createElement('a')
            link.download = '预算管理.xlsx'
            link.href = window.URL.createObjectURL(new Blob([response]))
            document.body.appendChild(link)
            link.click()
            link.download = ''
            document.body.removeChild(link)
            URL.revokeObjectURL(response)
          })
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  $base: '.depart-management';
  #{$base}-container {
    padding: 0 !important;
    background: $base-color-background !important;

    &.vab-fullscreen {
      padding: 20px !important;
    }
  }
</style>
