<template>
  <el-dialog
    v-drag
    append-to-body
    center
    class="detailDialog"
    :close-on-click-modal="false"
    :title="pageTitle"
    :visible.sync="dialogDetailVisible"
    width="1500px"
  >

    <div style="flex: 1">
      <el-tabs v-model="activeName" v-loading="formLoading" type="card">
        <el-tab-pane label="付款详情" name="first">
          <div
            class="custom-table-container"
            :class="{ 'vab-fullscreen': isFullscreen }"
          >
            <el-table
              ref="materialTable"
              border
              :data="paymentDataList"
              label-width="90px"
              :max-height="450"
              stripe
            >
              <el-table-column
                align="center"
                label="单号"
                type="serialNumber"
                width="200"
              />
              <el-table-column
                align="center"
                label="所属部门"
                prop="departName"
              />
              <el-table-column
                align="center"
                label="项目名称"
                prop="projectName"
              />
              <el-table-column
                align="center"
                label="付款事由"
                prop="paymentReason"
              />
              <el-table-column
                align="center"
                label="付款金额"
                prop="paymentAmount"
              />
              <el-table-column
                align="center"
                label="申请人"
                prop="createByName"
              />
              <el-table-column
                align="center"
                label="收款人"
                prop="payeeName"
              />
              <el-table-column
                align="center"
                label="付款单位"
                prop="payerName"
              />
              <el-table-column
                align="center"
                label="付款场景"
                prop="paymentScenarioName"
              />
              <el-table-column align="center" label="操作" width="200">
                <template #default="{ row }">
                  <el-button
                    icon="el-icon-view"
                    style="margin: 0 10px 10px 0 !important"
                    type="success"
                    @click="handlePaymentDetail(row)"
                  >
                    详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination
              :current-page="pageInfo.curPage"
              :layout="layout"
              :page-size="pageInfo.pageSize"
              :total="pageInfo.total"
              @current-change="handleCurrentChange"
              @size-change="handleSizeChange"
            />
            <table-payment-detail ref="tablePaymentDetail" :select-type="selectType"/>
          </div>
        </el-tab-pane>
        <el-tab-pane label="报销详情" name="second">
          <div
            class="custom-table-container"
            :class="{ 'vab-fullscreen': isFullscreen }"
          >
            <el-table
              ref="tableSort"
              border
              :data="reimburseDataList"
              :max-height="450"
              stripe
            >
              <el-table-column
                align="center"
                label="单号"
                type="serialNumber"
                width="200"
              />
              <el-table-column
                align="center"
                label="项目名称"
                prop="projectName"
              />
              <el-table-column
                align="center"
                label="报销类型"
                prop="reimbursementTypeName"
              />
              <el-table-column
                align="center"
                label="报销金额"
                prop="reimbursementAmount"
              />
              <el-table-column
                align="center"
                label="报销人"
                prop="reimbursementApplicantName"
              />
              <el-table-column
                align="center"
                label="收款人"
                prop="payeeNames"
              />
              <el-table-column
                align="center"
                label="付款单位"
                prop="payerIdName"
              />
              <el-table-column align="center" label="操作" width="200">
                <template #default="{ row }">
                  <el-button
                    icon="el-icon-view"
                    style="margin: 0 10px 10px 0 !important"
                    type="success"
                    @click="handleReimburseDetail(row)"
                  >
                    详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination
              :current-page="pageInfo1.curPage"
              :layout="layout"
              :page-size="pageInfo1.pageSize"
              :total="pageInfo1.total"
              @current-change="handleCurrentChange1"
              @size-change="handleSizeChange1"
            />
            <table-reimburse-detail ref="tableReimburseDetail" :budget-list="budgetList"/>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="danger" @click="dialogDetailVisible = false">
        关闭
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import tablePaymentDetail from '@/views/financialManagement/paymentManagement/components/paymentManagementDetail.vue'
import tableReimburseDetail
  from '@/views/financialManagement/reimburseManagement/components/reimburseManagementDetail.vue'

import {
  getDataListByPage as getPaymentManagementListByPage,
} from '@/api/financialManagement/paymentManagement'


import {
  getDataListByPage as getReimburseManagementListByPage,
} from '@/api/financialManagement/reimburseManagement'
import {getDictList} from "@/api/system/dict-api";

export default {
  name: 'SettlementDetail',
  components: {
    tablePaymentDetail,
    tableReimburseDetail
  },
  props: {},
  computed: {
    pageTitle() {
      return '报销付款详情'
    }
  },
  data() {
    return {
      // 自定义标签样式
      labelStyle: {
        width: '120px',
      },
      contentStyle: {
        minWidth: '120px',
      },
      size: '',
      dialogDetailVisible: false,
      formLoading: false,
      activeName: 'first',
      dateList: [],
      isFullscreen: false,
      // 表格列
      columns: [],
      monthsList: [],
      isApproval: false,
      selectType: 1,
      layout: 'total, sizes, prev, pager, next, jumper',
      pageInfo: {
        curPage: 1,
        pageSize: 10,
        total: 0,
      },
      pageInfo1: {
        curPage: 1,
        pageSize: 10,
        total: 0,
      },
      // 付款
      paymentDataList: [],
      // 报销
      reimburseDataList: [],
      // 成本预算清单
      budgetList: [],
      settlementFirmCostId: ''
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    // 查询字典 获取成本预算清单
    getDictDetails() {
      getDictList({dictCode: 'budgetListing'}).then((response) => {
        this.budgetList = response.result[0].children
      })
    },
    //付款详情
    handlePaymentDetail(row) {
      this.$refs['tablePaymentDetail'].showDialog(row)
    },
    handleReimburseDetail(row) {
      this.$refs['tableReimburseDetail'].showDialog(row)
    },
    callBackRefresh() {
      this.$parent.fetchData()
      this.dialogDetailVisible = false
    },
    async showDialog(row) {
      this.activeName = 'first'
      this.isApproval = false
      this.settlementFirmCostId = row
      this.dialogDetailVisible = true
      this.formLoading = true
      await this.getDictDetails()
      await this.getPaymentManagementListByPage(this.settlementFirmCostId)
      await this.getReimburseManagementListByPage(this.settlementFirmCostId)

      this.formLoading = false
    },
    // 获取付款数据
    async getPaymentManagementListByPage(settlementFirmCostId) {
      const params = {
        settlementFirmCostId: settlementFirmCostId,
        curPage: this.pageInfo.curPage,
        pageSize: this.pageInfo.pageSize,
        selectType: this.selectType,
        approvalResult: 2
      }
      await getPaymentManagementListByPage(params).then((res) => {
        const {
          result: {total, records},
        } = res
        this.paymentDataList = records
        this.pageInfo.total = Number(total)
      })
    },
    // 获取报销数据
    async getReimburseManagementListByPage(settlementFirmCostId) {
      const params = {
        settlementFirmCostId: settlementFirmCostId,
        curPage: this.pageInfo1.curPage,
        pageSize: this.pageInfo1.pageSize,
      }
      await getReimburseManagementListByPage(params).then((res) => {
        const {
          result: {total, records},
        } = res
        this.reimburseDataList = records
        // console.log(301,this.reimburseDataList)
        this.pageInfo1.total = Number(total)
      })
    },
    handleSizeChange(val) {
      this.pageInfo.pageSize = val
      this.pageInfo.curPage = 1
      this.getPaymentManagementListByPage(this.settlementFirmCostId)
    },
    handleCurrentChange(val) {
      this.pageInfo.curPage = val
      this.getPaymentManagementListByPage(this.settlementFirmCostId)
    },
    handleSizeChange1(val) {
      this.pageInfo.pageSize = val
      this.pageInfo.curPage = 1
      this.getReimburseManagementListByPage(this.settlementFirmCostId)
    },
    handleCurrentChange1(val) {
      this.pageInfo.curPage = val
      this.getReimburseManagementListByPage(this.settlementFirmCostId)
    },
  },
}
</script>
<style lang="scss" scoped>
.detailDialog ::v-deep .el-dialog__body {
  padding-top: 10px;
}

.statistics {
  width: 100%;
  font-size: 18px;
  margin: 10px 0;
  display: flex;
  justify-content: space-around;

  .item {
    display: flex;
    justify-content: space-between;

    .number {
      color: #e53f3f;
    }
  }
}
</style>
