<template>
  <el-dialog
    v-drag
    append-to-body
    center
    :close-on-click-modal="false"
    :title="actionMap[dialogStatus]"
    top="5vh"
    :visible.sync="dialogFormVisible"
    width="1520px"
    @close="close"
  >
    <div style="max-height: 75vh; overflow-y: scroll; overflow-x: hidden">
      <el-tabs
        v-model="activeName"
        v-loading="formLoading"
        :before-leave="beforeLeave"
        type="card"
        @tab-click="handleTabClick"
      >
        <el-tab-pane label="基本信息" name="first">
          <el-form
            ref="dataForm"
            v-loading="formLoading"
            :inline="true"
            label-position="right"
            label-width="160px"
            :model="formData"
            :rules="rules"
          >
            <table class="form-table">
              <tr>
                <td>
                  <el-form-item label="单号" prop="serialNumber">
                    <el-input
                      v-model="formData.serialNumber"
                      disabled
                      placeholder="自动生成"
                    />
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="申请人" prop="createByName">
                    <el-input v-model="formData.createByName" disabled/>
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="申请部门" prop="createDepart">
                    <el-input v-model="formData.createDepartName" disabled/>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td>
                  <el-form-item label="申请日期" prop="recordDate">
                    <el-date-picker
                      v-model="formData.recordDate"
                      format="yyyy-MM-dd"
                      placeholder="请选择"
                      value-format="yyyy-MM-dd"
                    />
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="所属项目" prop="projectId">
                    <el-input
                      v-model="formData.projectName"
                      :disabled="!formData.isAdd || showProjectInit"
                      placeholder="选择所属项目"
                      @click.native="selectProject"
                    />
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="业主名称" prop="proprietorName">
                    <el-input
                      v-model="formData.proprietorName"
                      disabled
                    />
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td>
                  <el-form-item label="项目所属部门" prop="departName">
                    <el-input
                      v-model="formData.departName"
                      disabled
                    />
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="项目负责人" prop="projectPrincipal">
                    <el-input
                      v-model="formData.projectPrincipal"
                      disabled
                    />
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="" prop="">
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td :colspan="4">
                  <el-form-item label="备注" prop="remark">
                    <el-input
                      v-model="formData.remark"
                      maxlength="1000"
                      placeholder="请输入内容(不超过1000字）"
                      style="width: 1170px"
                      type="textarea"
                    />
                  </el-form-item>
                </td>
              </tr>
            </table>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="合同金额" name="second">
          <budget-contract-amount-edit
            ref="budgetContractAmountEdit"
            :delete-able="deleteAble"
            :is-adjustment="isAdjustment"
          />
        </el-tab-pane>
        <el-tab-pane label="材料成本" name="third">
          <el-button
            style="margin-bottom: 10px; margin-top: 20px"
            type="primary"
            @click="addMaterialCosts"
          >
            新增材料成本
          </el-button>
          <el-table
            ref="tableSort"
            border
            :data="MaterialDataList"
            :max-height="450"
            stripe
            :show-summary="true"
            :summary-method="getMaterialSummaries"
          >
            <el-table-column
              align="center"
              label="序号"
              type="index"
              width="60"
            />
            <el-table-column
              align="center"
              label="供应商单位名称"
              prop="supplierUnitName"
              width="200"
            />
            <el-table-column
              align="center"
              label="预算金额"
              prop="contractAmount"
            >
              <template slot-scope="scope">
                {{scope.row.contractAmount | currencyFormat }}
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              label="结算金额"
              prop="settlementAmount"
            >
              <template slot-scope="scope">
                  {{scope.row.settlementAmount | currencyFormat }}
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              label="已支付金额"
              prop="amountPaid"
            >
              <template slot-scope="scope">
                <el-link
                  type="primary"
                  @click="viewDetail(scope.row.id)"
                >
                  {{scope.row.amountPaid | currencyFormat }}
                </el-link>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              label="开票金额"
              prop="invicedAmount"
            >
              <template slot-scope="scope">
                {{scope.row.invicedAmount | currencyFormat }}
              </template>
            </el-table-column>
            <el-table-column align="center" label="备注" prop="remarks"/>
            <el-table-column align="center" label="操作" width="300">
              <template #default="{ row }">
                <el-button
                  icon="el-icon-edit"
                  style="margin: 0 10px 10px 0 !important"
                  type="primary"
                  @click="editMaterialCosts(row)"
                >
                  编辑
                </el-button>
                <el-button
                  v-if="deleteAble"
                  icon="el-icon-delete-solid"
                  style="margin: 0 10px 10px 0 !important"
                  type="danger"
                  @click="deleteMaterialCosts(row)"
                >
                  删除
                </el-button>
                <el-button
                  icon="el-icon-link"
                  style="margin: 0 10px 10px 0 !important"
                  type="success"
                  @click="showUploadFileList(row)"
                >
                  附件
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- <div class="statistics">
            <div>小计</div>
            <div class="item">
              预算总金额（元）：
              <div class="number">{{ totalContractAmount }}</div>
            </div>
            <div class="item">
              结算总金额（元）：
              <div class="number">{{ totalSettlementAmount }}</div>
            </div>
           <div class="item">
            税金（元）：
             <div class="number">{{ totalTax }}</div>
           </div>
          </div> -->
          <!--    新增编辑材料成本      -->
          <material-costs-edit
            ref="materialCostsEdit"
            :is-adjustment="isAdjustment"
            @updateMaterialData="materialGetDataList"
          />
          <!--          材料成本附件-->
          <CommonUploadListPopup ref="CommonUploadListPopup"/>
        </el-tab-pane>
        <el-tab-pane label="项目成本" name="four">
          <bussiness-costs-edit
            ref="bussinessCostsEdit"
            :delete-able="deleteAble"
            :is-adjustment="isAdjustment"
            @closeDialog="closeDialog"
            @checkIsWarning="checkIsWarning"
          />
        </el-tab-pane>
      </el-tabs>
      <!-- 预算详情 -->
      <PaymentList ref="PaymentList" />
      <UploadLargeFileFdfsPopup ref="UploadLargeFileFdfsPopup"/>
      <!--      项目选择  -->
      <ProjectListPopup
        ref="projectListPopupRef"
        @getProjectInfo="changeProject"
      />
    </div>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="saveAndContinue">
        {{ activeName === 'four' ? '确认' : '保存，下一步' }}
      </el-button>
    </template>
  </el-dialog>

</template>

<script>
import { mapGetters } from 'vuex'
import moment from 'moment'
import {
  deleteMaterialData,
  materialGetInclusionStatisticsList,
  saveData,
} from '@/api/financialManagement/settlementManagement'
import {
  getDataList as getPaymentManagementDataList
} from '@/api/financialManagement/paymentManagement'
import {
  getBudgetInfoIncludeDetails,
  getDataList
} from '@/api/financialManagement/budgetManagement'
import { genUUID } from '@/utils/th_utils.js'
// 附件
import UploadLargeFileFdfsPopup from '@/views/common/UploadLargeFileFdfsPopup.vue'
// 合同金额类型
import budgetContractAmountEdit from './settlementContractAmountEdit.vue'
// 材料成本新增编辑
import materialCostsEdit from './materialCostsEdit.vue'
// 项目成本
import bussinessCostsEdit from './businessCostsEdit.vue'
import CommonUploadListPopup from '@/views/common/CommonUploadListPopup.vue'
// 项目列表
import ProjectListPopup from './ProjectListPopup.vue'

// //付款详情
import PaymentList from '@/views/financialManagement/settlementManagement/components/PaymentList.vue'



export default {
  name: 'SettlementManagementEdit',
  components: {
    CommonUploadListPopup,
    UploadLargeFileFdfsPopup,
    materialCostsEdit,
    bussinessCostsEdit,
    budgetContractAmountEdit,
    ProjectListPopup,
    PaymentList
  },
  props: {
    // 是否显示删除按钮
    deleteAble: {
      type: Boolean,
      default: true,
    },
    // 是否是预算修正（默认不是预算修正而是预算申请）
    isAdjustment: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      activeName: 'first',
      // 结算数据id
      settlementId: '',
      // 上一期预算数据id
      connectId: '',
      contentStyle: {
        minWidth: '120px',
      },
      showProjectInit:false,
      dialogFormVisible: false,
      formLoading: false,
      // 基本信息
      formData: {
        // taxAmount: 0,
        // materialCost: 0,
        // firmCost: 0,
        // relateTax: 0,
        // grossProfit: 0,
        // netProfit: 0,
        // outputTax: 0,
        // materialCostTax: 0,
        // amountExcludingTax: 0,
        // grossProfitRate: 0,
        // netProfitRate: 0,
        // amountReturned: 0,
        // amountInvoiced: 0,
        // settlementMaterialCosts: 0,
        // settlementProjectCosts: 0,
        // settlementRelatedTaxesFees: 0,
        // settlementMaterialGrossProfit: 0,
        // settlementProjectGrossProfit: 0,
        // settlementMaterialNetProfit: 0,
        // settlementProjectNetProfit: 0,
        // relateTaxList: {
        //   addedTax: 0,
        //   cityTax: 0,
        //   enterpriseCost: 0,
        //   enterpriseIncomeTax: 0,
        // },
        // contractTotalAmount: {
        //   amountExcludingTax: 0,
        //   contractAmount: 0,
        //   taxAmount: 0,
        // },
      },
      // 材料成本数据
      MaterialDataList: [],
      // 合同总金额
      totalContractAmount: 0,
      //结算总金额
      totalSettlementAmount: 0,
      // 不含税总金额
      totalExcludingTax: 0,
      // 总税金
      totalTax: 0,
      actionMap: {
        update: '编辑结算',
        create: '新增结算',
      },
      dialogStatus: '',
      // 校验项目成本金额是否填写有误
      isWarning: false,
      // 已有预算的项目ID
      projectIdList: [],
      // 发票类型
      invoiceList: [
        {value: 1, name: '增值税普通发票'},
        {value: 2, name: '增值税专用发票'},
      ],
      // 税率
      taxRateList: [
        {value: 0.01, name: '1%'},
        {value: 0.03, name: '3%'},
        {value: 0.06, name: '6%'},
        {value: 0.09, name: '9%'},
        {value: 0.13, name: '13%'},
      ],
      // 合同类型税率
      contractTaxRate: [
        {value: 0.06, name: '6%'},
        {value: 0.09, name: '9%'},
        {value: 0.13, name: '13%'},
      ],
      rules: {
        projectId: [
          {required: true, message: '所属项目为必填', trigger: 'change'},
        ],
        // departId: [
        //   {
        //     required: true,
        //     message: '项目所属部门为必填',
        //     trigger: 'change',
        //   },
        // ],
        // projectPrincipal: [
        //   {
        //     required: true,
        //     message: '项目负责人为必填',
        //     trigger: 'change',
        //   },
        // ],
        taxRate: [
          {required: true, message: '税率为必选', trigger: 'change'},
        ],
      },
    }
  },
  computed: {
    ...mapGetters({
      userId: 'user/userId',
      userName: 'user/userName',
      departList: 'user/departList',
    }),
    // 合同总金额
    // budgetAmountTotal() {
    //   if (!this.formData.contractList) {
    //     return 0
    //   }
    //   return this.formData.contractList
    //     .reduce((total, item) => {
    //       return total + item.contractAmount
    //     }, 0)
    //     .toFixed(2)
    // },
    // 不含税收入
    // taxExclusive() {
    //   if (!this.formData.contractList) {
    //     return 0
    //   }
    //   return this.formData.contractList
    //     .reduce((total, item) => {
    //       return total + item.amountExcludingTax
    //     }, 0)
    //     .toFixed(2)
    // },
    // 税金
    // taxCount() {
    //   if (!this.formData.contractList) {
    //     return 0
    //   }
    //   return this.formData.contractList
    //     .reduce((total, item) => {
    //       return total + item.taxAmount
    //     }, 0)
    //     .toFixed(2)
    // },
  },
  created() {
  },
  methods: {
    //查看付款详情
    async viewDetail(id) {
      // const map ={
      //   settlementMaterialCostId: id
      // }
      //通过settlementMaterialCostId查询付款记录
      // console.log(493,map)
      // getPaymentManagementDataList(id).then(res => {
      //   // console.log(771,res.result[0])
      //   // this.$refs.PaymentList.showDialog(res.result[0])
      //   console.log(496,res.result)
      // })
      this.$refs.PaymentList.showDialog(id)
    },
    formatTaxRate(taxRate) {
      return (taxRate * 100).toFixed(2) + '%'; // 格式化为两位小数并加上%
    },
    // 切换tab
    handleTabClick(tab) {
      switch (tab.name) {
        case 'first':
          break
        case 'second':
          this.$refs.budgetContractAmountEdit.showEdit(this.settlementId, this.connectId)
          break
        case 'third':
          this.materialGetDataList()
          break
        case 'four':
          this.$refs.bussinessCostsEdit.showEdit(this.settlementId, this.connectId)
          break
      }
    },
    // 切换标签之前的钩子
    beforeLeave() {
      if (this.settlementId === '' && this.formData.isAdd === true) {
        this.$message({
          message: '请先保存结算基本信息',
          type: 'warning',
        })
        return false
      } else {
        return true
      }
    },
    // 选择项目
    selectProject() {
      this.$refs.projectListPopupRef.showDialog({
        selectIds: this.formData.projectId,
        projectName: this.formData.projectName
      })
    },
    // 切换项目
    changeProject(data) {
      if (data.length > 0 && data[0].id !== this.formData.projectId) {
        const project = data[0]
        const map = {
          projectId: project.id,
          dataType: 'budget'
        }
        //根据项目id获取相关预算信息
        getDataList(map).then(res => {
          // console.log(690, res.result[0])
          this.formData.projectId = res.result[0].projectId
          this.formData.projectName = res.result[0].projectName
          this.formData.proprietorName = res.result[0].proprietorName
          this.formData.departName = res.result[0].departName
          this.formData.projectPrincipal = res.result[0].projectPrincipalName
          // this.formData.contractTotalAmount = res.result[0].contractTotalAmount
          // this.formData.contractList = res.result[0].contractList
          // this.formData.materialCost = res.result[0].materialCost
          // this.formData.firmCost = res.result[0].firmCost
          // this.formData.relateTax = res.result[0].relateTax
          // this.formData.grossProfit = res.result[0].grossProfit
          // this.formData.grossProfitRate = res.result[0].grossProfitRate
          // this.formData.netProfit = res.result[0].netProfit
          // this.formData.netProfitRate = res.result[0].netProfitRate
        })
        //查询已回款金额，已开票金额，结算材料成本，结算项目成本，结算相关税费(元)：，结算毛利润：结算毛利率：结算净利润，结算净利率
      }
    },
    projectInfoShow(data){
      this.showProjectInit = true
      this.showEdit()
      this.changeProject([{id:data.id}])
    },
    showEdit(row) {
      this.isWarning = false
      this.projectIdList = []
      if (!row) {
        this.dialogStatus = 'create'
        this.settlementId = ''
        this.initForm()
      } else {
        this.dialogStatus = 'update'
        this.formData = {...row, isAdd: false}
        this.settlementId = this.formData.id
        this.connectId = this.formData.connectId
        this.materialGetDataList()
      }
      this.dialogFormVisible = true
      this.$nextTick(() => {
        this.$refs.UploadLargeFileFdfsPopup.getParamsData({
          bizId: this.formData.id,
          bizCode: 'settlementManagement',
        })
        this.$refs['dataForm'].clearValidate()
      })
    },
    // 初始化表单
    initForm() {
      this.formData = {
        id: genUUID(),
        serialNumberTable: 'settlement_management',
        createBy: this.userId,
        createByName: this.userName,
        viewUser: this.userId,
        createDepart: this.departList[0].id,
        createDepartName: this.departList[0].departName,
        recordDate: moment().format('YYYY-MM-DD'),
        projectId: '',
        projectName: '',
        departId: '',
        departCode: this.departList[0].departCode,
        operationCode: 'JSGL',
        remark: '',
        isAdd: true,
      }
    },
    // 保存下一步
    saveAndContinue() {
      switch (this.activeName) {
        case 'first':
          this.save()
          break
        case 'second':
          this.activeName = 'third'
          this.materialGetDataList()
          break
        case 'third':
          this.activeName = 'four'
          this.$refs.bussinessCostsEdit.showEdit(this.settlementId)
          break
        case 'four':
          if (this.isWarning) {
            this.$message({
              message: '金额填写有误',
              type: 'error',
            })
            return
          }
          this.closeDialog()
          break
      }
    },
    // 项目成本金额填写有误
    checkIsWarning(val) {
      this.isWarning = val
    },
    // 保存基本信息
    save() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          let data = {...this.formData}
          saveData(data)
            .then(() => {
              this.$baseMessage(
                this.formData.isAdd ? '保存基本信息成功！' : '修改成功!',
                'success',
                'vab-hey-message-success'
              )
              // 新增成功后修改isAdd状态
              this.formData.isAdd = false
              // 保存成功进入下一步：合同类型金额
              this.activeName = 'second'
              this.settlementId = this.formData.id
              this.$refs.budgetContractAmountEdit.showEdit(this.settlementId)
              this.$emit('fetch-data')
            })
            .catch((err) => {
              this.$baseMessage(
                this.formData.isAdd
                  ? '保存基本信息失败!' + err.message
                  : '修改失败!' + err.message,
                'error',
                'vab-hey-message-error'
              )
            })
        }
      })
    },
    close() {
      if (this.activeName === 'third') {
        this.$emit('fetch-data')
      }
      this.$refs['dataForm'].resetFields()
      this.formData = this.$options.data().formData
      this.dialogFormVisible = false
      this.activeName = 'first'
    },
    // 保存项目成本后关闭弹窗
    closeDialog() {
      this.$emit('fetch-data')
      this.close()
    },
    // 新增材料成本
    addMaterialCosts() {
      this.$refs.materialCostsEdit.showEdit(this.settlementId, null)
    },
    // 编辑材料成本
    editMaterialCosts(row) {
      this.$refs.materialCostsEdit.showEdit(this.settlementId, row)
    },
    // 删除材料成本
    deleteMaterialCosts(row) {
      if (row.id) {
        this.$baseConfirm(
          '你确定要删除当前材料成本数据吗',
          null,
          async () => {
            deleteMaterialData({id: row.id})
              .then(() => {
                this.materialGetDataList()
                this.$baseMessage(
                  '删除成功!',
                  'success',
                  'vab-hey-message-success'
                )
              })
              .catch(() => {
                this.$baseMessage(
                  '删除失败!',
                  'error',
                  'vab-hey-message-error'
                )
              })
          }
        )
      }
    },
    // 材料成本附件
    showUploadFileList(row) {
      const data = {}
      data.bizId = row.id
      data.bizCode = 'materialCost'
      data.isShow = true
      this.$refs.CommonUploadListPopup.showUploadListDialog(data)
    },
    // 获取材料成本数据list
    materialGetDataList() {
      const params = {
        settlementId: this.settlementId,
      }
      // console.log(981,params)
      materialGetInclusionStatisticsList(params).then((response) => {
        this.MaterialDataList = response.result.dataList
        this.totalContractAmount = response.result.contractAmount
        this.totalSettlementAmount = response.result.settlementAmount
        this.totalExcludingTax = response.result.amountExcludingTax.toFixed(2)
        this.totalTax = response.result.taxAmount.toFixed(2)
      })
    },
    // 金额格式化
    formatMoney(value) {
      if (value !== null && value !== undefined) {
        return new Intl.NumberFormat('zh-CN', {
          style: 'currency',
          currency: 'CNY',
        }).format(value)
      } else {
        return ''
      }
    },
    getMaterialSummaries(param) {
      let summariesObj = null
      const { columns } = param;
      const sums = [];
      summariesObj = {
        contractAmount: this.totalContractAmount, //合同金额
        settlementAmount: this.totalSettlementAmount, //不含税金额
      }
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计';
          return;
        }
        sums[index] = summariesObj[column.property] !== undefined ? this.formatMoney(summariesObj[column.property]) : ''
      })
      return sums
    },
  },
}
</script>

<style lang="scss" scoped>
.dropTree {
  width: 100%;
  max-height: 300px;
  overflow: auto;
}

.statistics {
  width: 100%;
  font-size: 18px;
  margin: 10px 0;
  display: flex;
  justify-content: space-around;

  .item {
    display: flex;
    justify-content: space-between;

    .number {
      color: #e53f3f;
    }
  }
}

::v-deep .form-table {
  .el-input__inner {
    padding-right: 0;

    &::-webkit-inner-spin-button {
      margin: 0;
      -webkit-appearance: none !important;
    }

    &::-webkit-outer-spin-button {
      margin: 0;
      -webkit-appearance: none !important;
    }
  }

  .el-form-item__error {
    position: absolute !important;
    top: 45px;
    left: 10px;
  }

}

</style>
