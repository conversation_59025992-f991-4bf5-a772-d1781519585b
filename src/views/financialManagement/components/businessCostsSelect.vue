<template>
  <el-dialog
    v-drag
    append-to-body
    :close-on-click-modal="false"
    title="选择预算管理中项目成本"
    top="10vh"
    :visible.sync="dialogFormVisible"
    width="820px"
    @close="close"
  >
    <el-table
      ref="tableSort"
      v-loading="listLoading"
      border
      :data="list"
      :size="lineHeight"
      stripe
      @selection-change="setSelectRows"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column align="center" label="序号" type="index" width="55" />
      <el-table-column label="费用项目" prop="countName" />
      <el-table-column label="预算金额(元)" prop="amount" />
    </el-table>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
  import tableMix from '@/views/mixins/table'
  import { getImportBudgetFirmCost } from '@/api/financialManagement/reimburseManagement'

  export default {
    name: 'ApplyFormSelect',
    components: {},
    mixins: [tableMix],
    props: {},
    data() {
      return {
        dialogFormVisible: false,
        listLoading: false,
        queryForm: {
          applyNo: '',
          accountType: '',
          applyUserId: '',
          departId: '',
          projectId: '',
        },
        radio: '',
        selectRow: {},
        selectedIds: [],
        selectedManageCosts: [],
      }
    },
    watch: {},
    async created() {},
    methods: {
      showDialog(selectKeys, projectId, registrant, selectedManageCosts) {
        this.selectedIds = selectKeys
        this.selectedManageCosts = selectedManageCosts
        this.getBudgetAmount(projectId)
        this.dialogFormVisible = true
      },
      // 项目报销：获取企业预算成本
      getBudgetAmount(projectId) {
        this.listLoading = true
        const params = {
          projectId: projectId,
        }
        getImportBudgetFirmCost(params).then((res) => {
          const result = res.result
          this.list = result.filter(
            (item) => !this.selectedIds.includes(item.id)
          )
          this.dataListSorting()
          this.listLoading = false
        })
      },
      // 对项目成本列表中数据根据 费用明细管理的列表 数据进行排序
      dataListSorting() {
        // 创建一个 Map，存储 费用明细管理的列表数据 中元素的顺序
        const orderMap = new Map()
        this.selectedManageCosts[0].children.forEach((item, index) => {
          orderMap.set(item.dictName, index)
        })

        // 根据 orderMap 中的顺序对 项目成本列表中数据 进行排序
        this.list.sort((a, b) => {
          const indexA = orderMap.get(a.countName)
          const indexB = orderMap.get(b.countName)
          return indexA - indexB
        })
      },
      // 初始化选中状态
      initSelectRow() {
        const selected = this.list.filter((item) =>
          this.selectedIds.includes(item.id)
        )
        this.$nextTick(() => {
          selected.forEach((row) => {
            this.$refs.tableSort.toggleRowSelection(row, true)
          })
        })
      },
      // 获取选中数据(多选）
      setSelectRows(val) {
        this.selectRow = val
      },
      getCurrentRow(row) {
        //获取选中数据
        this.selectRow = row
      },
      save() {
        if (!this.selectRow.length > 0) {
          return
        }
        this.$emit('confirm', this.selectRow)
        this.$emit('change', this.selectRow)
        this.close()
      },
      close() {
        this.queryForm = this.$options.data().queryForm
        this.pageInfo = this.$options.data().pageInfo
        this.selectRow = this.$options.data().selectRow
        this.radio = this.$options.data().radio
        this.dialogFormVisible = false
      },
    },
  }
</script>

<style lang="scss" scoped>
  .dropTree {
    width: 100%;
    max-height: 300px;
    overflow: auto;
  }
</style>
