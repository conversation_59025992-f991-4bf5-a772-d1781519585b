<template>
  <el-dialog
    v-drag
    append-to-body
    :close-on-click-modal="false"
    :title="actionMap[dialogStatus]"
    top="1vh"
    :visible.sync="dialogFormVisible"
    width="1420px"
    @close="close"
  >
    <div>
      <el-form
        ref="dataForm"
        v-loading="formLoading"
        :inline="true"
        label-position="right"
        label-width="140px"
        :model="formData"
        :rules="rules"
      >
        <table class="form-table">
          <tr>
            <td>
              <el-form-item label="费用项目" prop="listingTypeName">
                <el-input v-model="formData.listingTypeName" disabled />
              </el-form-item>
            </td>
            <td>
              <el-form-item label="金额" prop="money">
                <el-input
                  v-model.number="formData.money"
                  placeholder="请输入金额"
                  type="number"
                />
              </el-form-item>
            </td>
            <td v-if="selectType === 1">
              <el-form-item label="剩余预算金额" prop="residueAmount">
                <el-input v-model="formData.residueAmount" disabled />
              </el-form-item>
            </td>
            <td v-if="selectType === 2" />
          </tr>
          <tr v-if="selectType === 1">
            <td>
              <el-form-item label="预算总金额" prop="aggregateAmount">
                <el-input v-model="formData.aggregateAmount" disabled />
              </el-form-item>
            </td>
            <td></td>
            <td></td>
          </tr>
          <tr>
            <td colspan="3">
              <el-form-item label="备注" prop="remark">
                <el-input
                  v-model="formData.remark"
                  maxlength="1000"
                  placeholder="请输入内容"
                  style="width: 1100px"
                  type="textarea"
                />
              </el-form-item>
            </td>
          </tr>
        </table>
        <UploadLargeFileFdfsPopup ref="UploadLargeFileFdfsPopup" />
      </el-form>
    </div>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">保存</el-button>
    </template>
  </el-dialog>
</template>

<script>
  import { mapGetters } from 'vuex'
  import { genUUID } from '@/utils/th_utils.js'
  import { getDictList } from '@/api/system/dict-api'
  import UploadLargeFileFdfsPopup from '@/views/common/UploadLargeFileFdfsPopup'
  import {
    checkMoney,
    saveData,
  } from '@/api/financialManagement/paymentManagementListing'
  export default {
    name: 'PaymentManagementListingEdit',
    components: {
      UploadLargeFileFdfsPopup,
    },
    props: {
      // 类型（1.项目型，2部门机关）
      projectId: {
        type: String,
        default() {
          return ''
        },
      },
    },
    data() {
      const validateMoney = (rule, value, callback) => {
        const data = { ...this.formData }
        checkMoney(data).then((response) => {
          if (response.result) {
            console.log(response)
            callback()
          } else {
            console.log(response)
            callback(new Error('付款金额大于剩余预算金额!'))
          }
        })
        // if (this.selectType === 2) {
        //   callback()
        // }
        // if (
        //   parseFloat(value) >
        //   parseFloat(this.formData.oldMoney) +
        //     parseFloat(this.formData.residueAmount)
        // ) {
        //   callback(new Error('付款金额大于剩余预算金额！'))
        //   return
        // }
        // if (parseFloat(value) > parseFloat(this.formData.aggregateAmount)) {
        //   callback(new Error('付款金额大于预算金额！'))
        //   return
        // }
        // callback()
      }
      return {
        dialogFormVisible: false,
        formLoading: false,
        formData: {},
        actionMap: {
          update: '编辑付款清单',
          create: '新增付款清单',
        },
        dialogStatus: '',
        selectedInvoiceType: [],
        rules: {
          money: [
            { required: true, message: '金额为必填', trigger: 'blur' },
            { validator: validateMoney, trigger: 'blur' },
          ],
          recordDate: [
            { required: true, message: '日期为必填', trigger: 'change' },
          ],
        },
        rowData: null,
        selectType: null,
      }
    },
    computed: {
      ...mapGetters({
        userId: 'user/userId',
        userName: 'user/userName',
        departList: 'user/departList',
      }),
    },
    async created() {
      this.getDictDetails()
    },
    methods: {
      getDictDetails() {
        getDictList({ dictCode: 'invoiceType' }).then((response) => {
          this.selectedInvoiceType = response.result[0].children
        })
      },
      showEdit(selectType, row, isAdd) {
        this.selectType = selectType
        if (isAdd === true) {
          this.dialogStatus = 'create'
          this.rowData = row
          this.initForm(row)
        } else {
          this.dialogStatus = 'update'
          this.formData = { ...row, isAdd: false, selectType: selectType }
        }
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs.UploadLargeFileFdfsPopup.getParamsData({
            bizId: this.formData.id,
            bizCode: 'paymentManagementListing',
          })
          this.$refs['dataForm'].clearValidate()
        })
      },
      // 初始化表单
      initForm(row) {
        this.formData = {
          id: genUUID(),
          listingType: '',
          listingTypeName: '',
          aggregateAmount: 0,
          money: '',
          remark: '',
          recordDate: '',
          invoiceType: '',
          invoiceTypeName: '',
          paymentManagementId: row.id,
          isAdd: true,
          selectType: this.selectType,
        }
      },
      save() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            let data = { ...this.formData }
            saveData(data)
              .then(() => {
                this.$baseMessage(
                  this.formData.isAdd ? '导入成功！' : '修改成功!',
                  'success',
                  'vab-hey-message-success'
                )
                this.close()
                this.$emit('fetch-data')
              })
              .catch(() => {
                this.$baseMessage(
                  this.formData.isAdd ? '导入失败！' : '修改失败!',
                  'error',
                  'vab-hey-message-error'
                )
              })
          }
        })
      },
      close() {
        this.$emit('change')
        this.$refs['dataForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
      },
      saveAndContinue() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.$emit('confirm', { ...this.formData })
            this.initForm(this.rowData)
            this.$nextTick(() => {
              this.$refs.UploadLargeFileFdfsPopup.getParamsData({
                bizId: this.formData.id,
                bizCode: 'paymentManagementListing',
              })
              this.$refs['dataForm'].clearValidate()
            })
          }
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .dropTree {
    width: 100%;
    max-height: 300px;
    overflow: auto;
  }
</style>
