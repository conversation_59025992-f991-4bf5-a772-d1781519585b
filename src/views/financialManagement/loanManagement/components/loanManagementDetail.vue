<template>
  <el-dialog
    v-drag
    append-to-body
    center
    class="detailDialog"
    :close-on-click-modal="false"
    title="借款详情"
    top="5vh"
    :visible.sync="dialogDetailVisible"
    width="1400px"
  >
    <div
      style="
        display: flex;
        max-height: 75vh;
        overflow-y: scroll;
        overflow-x: hidden;
      "
    >
      <div style="flex: 1">
        <el-descriptions border class="margin-top" :column="3" size="medium">
          <el-descriptions-item>
            <template slot="label">单号</template>
            {{ formData.serialNumber }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">申请人</template>
            {{ formData.createByName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">申请日期</template>
            {{ formData.recordDate | dateformat('YYYY-MM-DD') }}
          </el-descriptions-item>
          <el-descriptions-item v-if="formData.type === 'project'" :span="2">
            <template slot="label">所属项目</template>
            {{ formData.projectName }}
          </el-descriptions-item>
          <el-descriptions-item v-if="formData.type === 'project'">
            <template slot="label">项目单号</template>
            {{ formData.projectCode }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">费用所属部门</template>
            {{ formData.departName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">被借款单位</template>
            {{ formData.borrowerName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">借款金额(元)</template>
            {{ formData.loanAmount | currencyFormat }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">借款日期</template>
            {{ formData.loanTime | dateformat('YYYY-MM-DD') }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">还款日期</template>
            {{ formData.paymentTime | dateformat('YYYY-MM-DD') }}
          </el-descriptions-item>

          <el-descriptions-item>
            <template slot="label">收款人</template>
            <div v-if="formData.payeeDetailRow && formData.payeeDetailRow.id">
              <span style="margin-right: 10px">
                {{ formData.payeeDetailRow.userName }}
              </span>
              <span style="margin-right: 10px">
                {{ formData.payeeDetailRow.bankDeposit }}
              </span>
              <span style="margin-right: 10px">
                {{ formData.payeeDetailRow.cardNumber }}
              </span>
            </div>
          </el-descriptions-item>
          <el-descriptions-item :span="3">
            <template slot="label">借款事由</template>
            {{ formData.reason }}
          </el-descriptions-item>
          <el-descriptions-item :span="3">
            <template slot="label">备注</template>
            {{ formData.remark }}
          </el-descriptions-item>
        </el-descriptions>
        <UploadLargeFileFdfsPopupDetail
          ref="UploadLargeFileFdfsPopupDetail"
          style="margin-top: 20px"
        />
        <!-- 审批 -->
        <VabApproveFlow
          v-if="isApproval"
          ref="ApprovalFlow"
          @callBackRefresh="callBackRefresh"
        />
      </div>
      <div v-if="formData.approvalResult !== '0'">
        <!-- 审批记录 -->
        <VabWorkFlowApproveRecDlg ref="workFlwApprovalRecDlg" />
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="danger" @click="dialogDetailVisible = false">
        关闭
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import UploadLargeFileFdfsPopupDetail from '@/views/common/UploadLargeFileFdfsPopupDetail.vue'
  import { getData as getDataCard } from '@/api/staffManagement/rosterApi'

  export default {
    name: 'LoanManagementDetail',
    components: {
      UploadLargeFileFdfsPopupDetail,
    },
    props: {},
    data() {
      return {
        size: '',
        dialogDetailVisible: false,
        formLoading: false,
        formData: {},
        isApproval: false,
      }
    },
    created() {},
    mounted() {},
    methods: {
      showApprovalFlowByParams(bizKey, row, paramsMap) {
        this.showDialog(row)
        this.isApproval = true
        this.$nextTick(() => {
          this.$refs.ApprovalFlow.showApprovalFlowByParams(
            bizKey,
            row,
            paramsMap
          )
        })
      },
      callBackRefresh() {
        this.$parent.fetchData()
        this.dialogDetailVisible = false
      },
      async showDialog(row) {
        this.isApproval = false
        this.formData = { ...row, payeeDetailRow: {} }
        this.dialogDetailVisible = true
        this.formLoading = true
        await this.getPayeeData()
        this.formLoading = false
        this.$nextTick(() => {
          if (this.formData.approvalResult !== '0') {
            this.$refs.workFlwApprovalRecDlg.getTaskProcessListByBizKey(
              this.formData.id
            )
          }
          this.$refs.UploadLargeFileFdfsPopupDetail.getParamsData({
            bizId: this.formData.id,
            bizCode: 'loanManagement',
            isShow: true,
          })
        })
      },
      async getPayeeData() {
        if (this.formData.payeeDetailId) {
          await getDataCard({ id: this.formData.payeeDetailId }).then(
            (response) => {
              this.formData.payeeDetailRow = response.result
            }
          )
        }
      },
    },
  }
</script>
<style lang="scss" scoped>
  .detailDialog ::v-deep .el-dialog__body {
    padding-top: 10px;
  }
</style>
