<template>
  <!-- 上传器 -->
  <el-dialog
    v-drag
    title="文件上传"
    append-to-body
    center
    :close-on-click-modal="false"
    :visible.sync="uploadVisible" width="60%" @close="refresh">
    <uploader
      v-if="uploadVisible"
      ref="uploader"
      :options="options"
      :auto-start="false"
      :file-status-text="fileStatusText"
      class="uploader-ui"
      @file-added="onFileAdded"
      @file-success="onFileSuccess"
      @file-progress="onFileProgress"
      @file-error="onFileError"
    >
      <uploader-unsupport />
      <uploader-drop>
        <uploader-btn v-if="fileType === 'image'" id="global-uploader-btn" ref="uploadBtn" :attrs="attrsImg">选择文件<i class="el-icon-upload el-icon--right" /></uploader-btn>
        <uploader-btn v-else-if="fileType === 'video'" id="global-uploader-btn" ref="uploadBtn" :attrs="attrsVideo">选择文件<i class="el-icon-upload el-icon--right" /></uploader-btn>
        <uploader-btn v-else id="global-uploader-btn" ref="uploadBtn" :attrs="attrs">选择文件<i class="el-icon-upload el-icon--right" /></uploader-btn>
      </uploader-drop>
      <uploader-list />
    </uploader>
  </el-dialog>
</template>

<script>
import { ACCEPT_CONFIG } from '@/utils/upload/config.js'
import SparkMD5 from 'spark-md5'
import { saveSysUpload } from '@/api/system/uploadFile-api'
import { getToken } from '@/utils/token'
import { baseURL } from '@/config'
import { folderName } from '@/utils/constants'

export default {
  name: 'CommonUploadLargeFileFdfsPopup',
  props: {
    // 文件类型
    fileType: {
      type: String,
      default: 'all'
    }
  },
  data() {
    return {
      options: {
        processParams:(file)=>{
          return {
            fileName:file.filename,
            chunkNumber:file.chunkNumber,
            currentSize:file.chunkSize,
            totalChunks:file.totalChunks,
            totalSize:file.totalSize,
            chunkSize:file.chunkSize,
            identifier:file.identifier,
            relativePath: file.relativePath,
            currentChunkSize: file.currentChunkSize,
            bizId:this.searchCondition.bizId,
            bizCode:this.searchCondition.bizCode,
            folder: folderName,
            fileTAG:file.filename + '-' + this.fileTime
          }
        },
        // 目标上传 URL，默认POST
        target: baseURL + '/base/uploadFile/chunkUploadMinio',
        // 同时上传的分片数量
        simultaneousUploads: 1,
        // 分块大小(单位：字节)
        chunkSize: '6291456',
        // 上传文件时文件内容的参数名，对应chunk里的Multipart对象名，默认对象名为file
        // fileParameterName: 'upfile',
        // 失败后最多自动重试上传次数
        maxChunkRetries: 3,
        // 是否开启服务器分片校验，对应GET类型同名的target URL
        testChunks: false,

        headers: { 'AUTH-TOKEN': getToken() },
        /*
          服务器分片校验函数，判断秒传及断点续传,传入的参数是Uploader.Chunk实例以及请求响应信息
          reponse码是successStatuses码时，才会进入该方法
          reponse码如果返回的是permanentErrors 中的状态码，不会进入该方法，直接进入onFileError函数 ，并显示上传失败
          reponse码是其他状态码，不会进入该方法，正常走标准上传
          checkChunkUploadedByResponse函数直接return true的话，不再调用上传接口
          */
        // checkChunkUploadedByResponse: function(chunk, response_msg) {
        //   const objMessage = JSON.parse(response_msg)
        //   if (objMessage.skipUpload) {
        //     return true
        //   }
        //   return (objMessage.uploadedChunks || []).indexOf(chunk.offset + 1) >= 0
        // }
      },
      attrs: {
        accept: ACCEPT_CONFIG.getAll()
      },
      attrsImg: {
        accept: ACCEPT_CONFIG.getImg()
      },
      attrsVideo: {
        accept: ACCEPT_CONFIG.getVideo()
      },
      fileStatusText: {
        success: '上传成功',
        error: '上传失败',
        uploading: '上传中',
        paused: '暂停',
        waiting: '等待上传'
      },
      uploadVisible: false,
      searchCondition: {
        fileName: '',
        bizId: '',
        bizCode: ''
      },
    }
  },
  methods: {
    refresh() {
      // console.log("关闭")
      this.$emit('refreshUploadFileList')
    },
    /**
     * 获取文件扩展名
     * @param {Object} file - 文件对象
     * @returns {string} 文件扩展名（包含点号，如 .jpg）
     */
    getFileExtension(file) {
      const fileName = file.name || file.filename || ''
      const lastDotIndex = fileName.lastIndexOf('.')
      return lastDotIndex !== -1 ? fileName.substring(lastDotIndex).toLowerCase() : ''
    },
    /**
     * 验证文件类型是否被允许
     * @param {Object} file - 文件对象
     * @returns {boolean} 是否允许上传
     */
    validateFileType(file) {
      const fileExtension = this.getFileExtension(file)

      if (!fileExtension) {
        this.$message({
          showClose: true,
          message: '无法识别文件类型，请选择有效的文件',
          type: 'error'
        })
        file.cancel()
        return false
      }

      let allowedTypes = []

      // 根据 fileType prop 获取允许的文件类型
      switch (this.fileType) {
        case 'image':
          allowedTypes = ACCEPT_CONFIG.image
          break
        case 'video':
          allowedTypes = ACCEPT_CONFIG.video
          break
        case 'document':
          allowedTypes = ACCEPT_CONFIG.document
          break
        case 'appSource':
          allowedTypes = ACCEPT_CONFIG.appSource
          break
        case 'all':
        default:
          allowedTypes = ACCEPT_CONFIG.getAll()
          break
      }

      if (!allowedTypes.includes(fileExtension)) {
        const allowedTypesText = allowedTypes.join(', ')
        this.$message({
          showClose: true,
          message: `不支持的文件类型 "${fileExtension}"。允许的文件类型：${allowedTypesText}`,
          type: 'error'
        })
        file.cancel()
        return false
      }

      return true
    },
    showUploadLargeDialog(data) {
      this.uploadVisible = true
      this.searchCondition.bizId = data.bizId
      this.searchCondition.bizCode = data.bizCode
    },
    onFileAdded(file) {
      // 验证文件类型
      if (!this.validateFileType(file)) {
        return
      }
      this.computeMD5(file)
    },
    /*
      第一个参数 rootFile 就是成功上传的文件所属的根 Uploader.File 对象，它应该包含或者等于成功上传文件；
      第二个参数 file 就是当前成功的 Uploader.File 对象本身；
      第三个参数就是 message 就是服务端响应内容，永远都是字符串；
      第四个参数 chunk 就是 Uploader.Chunk 实例，它就是该文件的最后一个块实例，如果你想得到请求响应码的话，chunk.xhr.status就是
      */
    onFileSuccess(rootFile, file, response, chunk) {
      // // console.log(response)
      //
      // // refProjectId为预留字段，可关联附件所属目标，例如所属档案，所属工程等
      // file.bizId = this.searchCondition.bizId
      // file.bizCode = this.searchCondition.bizCode
      // // console.log(file)
      // // 重要! 服务器文件地址回传记录到表中
      // file.path = JSON.parse(response).path
      // saveSysUpload(file).then(responseData => {
      //   if (responseData.data.code === 415) {
      //     console.log('合并操作未成功，结果码：' + responseData.data.code)
      //   }
      // }).catch(function(error) {
      //   console.log('合并后捕获的未知异常：' + error)
      // })
    },
    onFileError(rootFile, file, response, chunk) {
      console.log('上传完成后异常信息：' + response)
    },

    /**
       * 计算md5，实现断点续传及秒传
       * @param file
       */
    computeMD5(file) {
      file.pause()

      // 单个文件的大小限制2G
      const fileSizeLimit = 10 * 1024 * 1024 * 1024 * 1024
      // console.log('文件大小：' + file.size)
      // console.log('限制大小：' + fileSizeLimit)
      if (file.size > fileSizeLimit) {
        this.$message({
          showClose: true,
          message: '文件大小不能超过2G'
        })
        file.cancel()
      }

      const fileReader = new FileReader()
      const time = new Date().getTime()
      const blobSlice = File.prototype.slice || File.prototype.mozSlice || File.prototype.webkitSlice
      let currentChunk = 0
      const chunkSize = 6 * 1024 * 1024
      const chunks = Math.ceil(file.size / chunkSize)
      const spark = new SparkMD5.ArrayBuffer()
      // 由于计算整个文件的Md5太慢，因此采用只计算第1块文件的md5的方式
      const chunkNumberMD5 = 1

      loadNext()

      fileReader.onload = e => {
        spark.append(e.target.result)

        if (currentChunk < chunkNumberMD5) {
          loadNext()
        } else {
          const md5 = spark.end()
          file.uniqueIdentifier = md5
          file.resume()
          // console.log(`MD5计算完毕：${file.name} \nMD5：${md5} \n分片：${chunks} 大小:${file.size} 用时：${new Date().getTime() - time} ms`)
        }
      }

      fileReader.onerror = function() {
        this.error(`文件${file.name}读取出错，请检查该文件`)
        file.cancel()
      }

      function loadNext() {
        const start = currentChunk * chunkSize
        const end = ((start + chunkSize) >= file.size) ? file.size : start + chunkSize

        fileReader.readAsArrayBuffer(blobSlice.call(file.file, start, end))
        currentChunk++
        // console.log('计算第' + currentChunk + '块')
      }
    },
    close() {
      this.uploader.cancel()
    },
    onFileProgress() {

    },
    error(msg) {
      this.$baseMessage(msg, 'error', 'vab-hey-message-error')
    }
  }
}
</script>

<style>
  .uploader-ui {
    padding: 15px;
    margin: auto;
    font-size: 12px;
    font-family: Microsoft YaHei;
  }
  .uploader-ui .uploader-btn {
    margin-right: 4px;
    font-size: 12px;
    border-radius: 3px;
    color: #FFF;
    background-color: #409EFF;
    border-color: #409EFF;
    display: inline-block;
    line-height: 1;
    white-space: nowrap;
  }
  .uploader-ui .uploader-list {
    max-height: 440px;
    overflow: auto;
    overflow-x: hidden;
    overflow-y: auto;
  }
  #global-uploader-btn{
    height: 32px;
    line-height: 30px;
    padding: 0px 8px;
  }
  #global-uploader-btn:hover{
    background-color: #1890ff;
  }
</style>
