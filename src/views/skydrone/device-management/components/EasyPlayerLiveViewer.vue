<template>
  <el-dialog
    v-drag
    :before-close="handleClose"
    class="easyplayer-live-modal"
    :close-on-click-modal="false"
    :title="deviceName"
    top="5vh"
    :visible.sync="dialogVisible"
    width="890px"
  >
    <div class="modal-header">
      <div class="stream-info">
        <h3>{{ deviceName }}</h3>
        <!--        <p v-if="currentStreamUrl" class="stream-url">-->
        <!--          {{ currentStreamUrl }}-->
        <!--          <span v-if="isCurrentStreamAI" class="stream-type ai-stream">[AI流]</span>-->
        <!--          <span v-else-if="streamConnected" class="stream-type normal-stream">[普通流]</span>-->
        <!--        </p>-->
        <!--        <p v-if="lastPlayedUrl && lastPlayedUrl !== currentStreamUrl" class="last-played-url">-->
        <!--          上次播放: {{ lastPlayedUrl }}-->
        <!--        </p>-->
      </div>
      <div class="live-status">
        <el-tag v-if="isLoading" effect="dark" size="medium" type="info">
          <i class="el-icon-loading" />
          加载中
        </el-tag>
        <el-tag
          v-else-if="streamError"
          effect="dark"
          size="medium"
          type="danger"
        >
          <i class="el-icon-warning" />
          连接断开
        </el-tag>
        <el-tag
          v-else-if="isReconnecting"
          effect="dark"
          size="medium"
          type="warning"
        >
          <i class="el-icon-loading" />
          重新连接中
        </el-tag>
        <el-tag
          v-else-if="isUpgrading && isPlaying && streamConnected"
          effect="dark"
          size="medium"
          type="primary"
        >
          <i class="el-icon-upload" />
          检测ai流中
        </el-tag>
        <el-tag
          v-else-if="isPlaying"
          effect="dark"
          size="medium"
          type="success"
        >
          <i class="el-icon-video-play" />
          直播中
        </el-tag>
        <el-tag v-else effect="dark" size="medium" type="info">
          <i class="el-icon-video-pause" />
          等待连接
        </el-tag>
      </div>
    </div>

    <div class="player-content">
      <!-- EasyPlayer播放器容器 -->
      <div v-show="showPlayer" class="player-wrapper">
        <div :id="playerId" class="easy-player-container"></div>
      </div>
      <!--      <div v-show="!currentStreamUrl || isDestroyed || !showPlayer" class="waiting-container">-->
      <!--        <div class="waiting-content">-->
      <!--          <i class="el-icon-video-pause waiting-icon"></i>-->
      <!--          <p>等待连接直播流...</p>-->
      <!--        </div>-->
      <!--      </div>-->
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button
        v-if="playerInfo && isPlaying"
        icon="el-icon-camera"
        type="success"
        :loading="screenshotLoading"
        @click="takeScreenshot"
      >
        {{ screenshotLoading ? '截图上传中...' : '截图上传' }}
      </el-button>
      <el-button icon="el-icon-close" type="primary" @click="handleClose">
        关闭
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { uploadMinio } from '@/api/system/uploadFile-api'
  import { folderName } from '@/utils/constants'

  export default {
    name: 'EasyPlayerLiveViewer',
    props: {
      visible: {
        type: Boolean,
        default: false,
      },
      device: {
        type: Object,
        default: () => ({}),
      },
      maxRetries: {
        type: Number,
        default: 10,
      },
      retryInterval: {
        type: Number,
        default: 3000,
      },
    },
    data() {
      return {
        playerInfo: null,
        isLoading: false,
        retryCount: 0,
        retryTimer: null,
        checkingUrl: false,
        streamConnected: false,
        aiStreamConnected: false,
        currentStreamUrl: null,
        isDestroyed: false,
        showPlayer: true,
        streamError: false,
        isReconnecting: false,
        isPlaying: false,
        playerId: 'easyplayer-' + Date.now() + '-' + Math.random().toString(36),
        lastPlayedUrl: null, // 记录上次播放的流地址
        isUpgrading: false, // 是否正在升级到更好的流
        playerInitialized: false, // 播放器是否已初始化
        aiStreamFailedAt: null, // AI流失败的时间戳
        aiStreamCooldown: 3000, // AI流冷却时间（30秒）
        screenshotLoading: false, // 截图上传状态
      }
    },
    computed: {
      dialogVisible: {
        get() {
          return this.visible
        },
        set(val) {
          this.$emit('update:visible', val)
        },
      },
      deviceName() {
        if (!this.device) return '设备直播'
        return (
          this.device.nickname ||
          (this.device.sn ? `设备 ${this.device.sn} 直播流` : '设备直播')
        )
      },

      // 判断当前播放的流是否为AI流
      isCurrentStreamAI() {
        if (!this.currentStreamUrl) return false

        // 基于URL路径判断是否为AI流
        const isAiByUrl = this.currentStreamUrl.includes('/ai/') ||
          this.currentStreamUrl.includes('live/ai')

        // 结合状态标志判断
        const isAiByState = this.aiStreamConnected

        const finalResult = isAiByUrl || isAiByState

        // 只在结果变化时输出日志，避免过多日志
        if (this._lastStreamTypeResult !== finalResult) {
          console.log('🔍 流类型判断:', {
            currentStreamUrl: this.currentStreamUrl,
            isAiByUrl,
            isAiByState,
            finalResult
          })
          this._lastStreamTypeResult = finalResult
        }

        // 优先使用URL判断，因为它更准确
        return finalResult
      }
    },
    watch: {
      visible(newVal) {
        if (newVal) {
          console.log('打开EasyPlayer直播窗口，开始流检测')
          this.isDestroyed = false
          // 如果播放器不存在，重置状态；如果存在，保持播放器实例
          if (!this.playerInfo) {
            this.resetPlayerState()
          } else {
            console.log('播放器已存在，复用播放器实例')
            // 只重置流相关状态，保持播放器实例
            this.isLoading = false
            this.streamError = false
            this.isReconnecting = false
            this.isUpgrading = false
            this.streamConnected = false
            this.aiStreamConnected = false
            this.currentStreamUrl = null
            this.lastPlayedUrl = null
          }
          this.$nextTick(() => {
            this.startStreamCheck()
          })
        } else {
          console.log('关闭EasyPlayer直播窗口，停止检测但保持播放器')
          this.isDestroyed = true
          this.stopRetryTimer()
          this.stopPlayer() // 只停止播放，不销毁播放器
        }
      },
    },
    beforeDestroy() {
      console.log('EasyPlayer组件开始销毁')
      this.isDestroyed = true
      this.showPlayer = false
      this.stopRetryTimer()
      this.destroyPlayer()
      console.log('EasyPlayer组件销毁完成')
    },
    methods: {
      resetPlayerState() {
        this.isLoading = false
        this.retryCount = 0
        this.streamConnected = false
        this.aiStreamConnected = false
        this.currentStreamUrl = null
        this.streamError = false
        this.isReconnecting = false
        this.isPlaying = false
        this.showPlayer = true
        this.lastPlayedUrl = null
        this.isUpgrading = false
        this.aiStreamFailedAt = null
        // 注意：不重置 playerInitialized，保持播放器实例
      },
      stopRetryTimer() {
        if (this.retryTimer) {
          clearTimeout(this.retryTimer)
          this.retryTimer = null
        }
      },

      destroyPlayer() {
        console.log('销毁EasyPlayer播放器')
        this.showPlayer = false

        if (this.playerInfo) {
          try {
            if (typeof this.playerInfo.destroy === 'function') {
              this.playerInfo.destroy()
            }
          } catch (error) {
            console.warn('销毁EasyPlayer播放器时出错:', error)
          }
          this.playerInfo = null
        }

        // 清除记录的流地址和初始化标志
        this.lastPlayedUrl = null
        this.playerInitialized = false

        console.log('EasyPlayer播放器已完全移除')
      },

      // 停止播放但不销毁播放器
      stopPlayer() {
        if (this.playerInfo) {
          try {
            console.log('停止播放器播放')
            this.playerInfo.pause()
            this.isPlaying = false
          } catch (error) {
            console.warn('停止播放器时出错:', error)
          }
        }
      },

      createOrUpdatePlayer(sourceType = '') {
        if (this.isDestroyed) {
          console.warn('组件已销毁，无法操作播放器')
          return
        }

        // 检查流地址是否与当前播放的一致
        if (
          this.playerInfo &&
          this.lastPlayedUrl === this.currentStreamUrl &&
          this.isPlaying
        ) {
          console.log(
            `流地址未变化且正在播放，跳过操作: ${this.currentStreamUrl}`
          )
          return
        }

        // 如果播放器已存在，直接使用play方法切换流
        if (this.playerInfo) {
          console.log(
            `播放器已存在，使用play方法切换到新流 - ${sourceType}:`,
            this.currentStreamUrl
          )
          this.playStream()
          return
        }

        // 首次创建播放器
        console.log(
          `首次创建EasyPlayer播放器 - ${sourceType}:`,
          this.currentStreamUrl
        )

        try {
          if (typeof window.EasyPlayerPro === 'undefined') {
            console.error('EasyPlayerPro未加载，请检查js文件引入')
            this.handleStreamError()
            return
          }

          // 确保容器显示
          this.showPlayer = true

          this.$nextTick(() => {
            if (this.isDestroyed) return

            // 等待DOM更新后再查找容器
            setTimeout(() => {
              if (this.isDestroyed) return

              const container = document.getElementById(this.playerId)
              if (!container) {
                console.error('播放器容器未找到，playerId:', this.playerId)
                console.error(
                  'DOM中的容器:',
                  document.querySelector('.easy-player-container')
                )
                this.handleStreamError()
                return
              }

              this.initializePlayer(container)
            }, 100)
          })
        } catch (error) {
          console.error('创建EasyPlayer播放器失败:', error)
          this.handleStreamError()
        }
      },

      initializePlayer(container) {
        try {
          const config = {
            isLive: true,
            hasAudio: true,
            isMute: false,
            stretch: true,
            bufferTime: 1,
            loadTimeOut: 10,
            loadTimeReplay: -1,
            MSE: true,
            WCS: false,
            WASM: false,
            debug: false,
            watermark: {
              text: {
                content: '',
              },
              right: 10,
              top: 10,
            },
          }

          console.log('正在创建EasyPlayer播放器实例...', container)
          this.playerInfo = new window.EasyPlayerPro(container, config)
          this.playerInitialized = true

          this.bindPlayerEvents()

          console.log('播放器创建成功，准备播放流:', this.currentStreamUrl)
          // 创建完成后立即播放当前流
          if (this.currentStreamUrl) {
            this.playStream()
          }
        } catch (error) {
          console.error('初始化EasyPlayer播放器失败:', error)
          this.handleStreamError()
        }
      },

      bindPlayerEvents() {
        if (!this.playerInfo) return

        this.playerInfo.on('play', () => {
          console.log('✅ EasyPlayer播放开始')
          this.isPlaying = true
          this.isLoading = false
          this.streamError = false
          this.isReconnecting = false
          this.retryCount = 0 // 播放成功时重置重试计数

          // 修改截图按钮事件
          this.modifyScreenshotButton()

          // 检查当前播放的流类型
          const isAiStream =
            this.currentStreamUrl &&
            (this.currentStreamUrl.includes('/ai/') || this.aiStreamConnected)

          if (isAiStream) {
            console.log('🎯 AI流播放成功，停止所有后台检测')
            console.log('当前播放的AI流地址:', this.currentStreamUrl)
            this.aiStreamConnected = true
            this.stopAllDetection()
          } else {
            console.log('📡 普通流播放成功')
            // 普通流播放成功时，如果还没有AI流，可以继续检测
            if (!this.aiStreamConnected) {
              this.isUpgrading = false // 重置升级状态，等待下次检测
            }
          }
        })

        this.playerInfo.on('error', (error) => {
          console.error(`❌ EasyPlayer播放错误: ${error}`)

          // 检测是否为网络获取错误，需要重新查询流地址
          if (this.isFetchError(error)) {
            console.log('检测到fetchError，流地址可能失效，将重新查询')
            this.handleStreamError(error)
          } else {
            this.handleStreamError(error)
          }
        })

        this.playerInfo.on('fullscreen', (flag) => {
          console.log('全屏状态:', flag)
        })
      },

      async playStream() {
        if (!this.playerInfo || !this.currentStreamUrl || this.isDestroyed) {
          console.warn('无法播放流 - 播放器未创建或流地址为空或组件已销毁')
          return
        }

        try {
          // 如果是相同的流地址，跳过
          if (this.lastPlayedUrl === this.currentStreamUrl && this.isPlaying) {
            console.log('相同流地址且正在播放，跳过:', this.currentStreamUrl)
            return
          }

          console.log('使用play方法播放流:', this.currentStreamUrl)
          console.log('上次播放的流:', this.lastPlayedUrl)
          console.log('AI流状态:', this.aiStreamConnected)

          await this.playerInfo.play(this.currentStreamUrl)
          // 记录成功播放的流地址
          this.lastPlayedUrl = this.currentStreamUrl
          console.log('流播放成功，当前URL:', this.currentStreamUrl)
        } catch (error) {
          console.error('播放流失败:', error)
          this.handleStreamError(error.message || error)
        }
      },

      isFetchError(error) {
        const errorStr = String(error).toLowerCase()
        return (
          errorStr.includes('fetcherror') ||
          errorStr.includes('fetch') ||
          errorStr.includes('network') ||
          errorStr.includes('404') ||
          errorStr.includes('403') ||
          errorStr.includes('500')
        )
      },

      handleStreamError(error = null) {
        this.streamError = true
        this.isLoading = false
        this.isPlaying = false
        this.isReconnecting = false
        this.isUpgrading = false

        // 检查是否是AI流错误
        const wasAIStream = this.aiStreamConnected

        console.log(`流播放错误，错误信息: ${error}`)

        // 停止所有检测
        this.stopAllDetection()

        // 不销毁播放器，只是停止播放
        this.stopPlayer()

        if (wasAIStream) {
          // AI流失败的特殊处理
          this.handleAIStreamFailure(error)
        } else {
          // 普通流失败的处理
          this.handleNormalStreamFailure(error)
        }
      },

      // 处理AI流失败
      handleAIStreamFailure(error) {
        console.log('🚫 AI流播放失败，设置冷却时间并切换到普通流', error)

        // 设置AI流冷却时间
        this.aiStreamFailedAt = Date.now()

        // 清除AI流状态
        this.aiStreamConnected = false
        this.lastPlayedUrl = null
        this.currentStreamUrl = null
        this.streamConnected = false

        // 直接尝试检测并播放普通流
        console.log('🔄 尝试切换到普通流')
        this.checkAndPlayStream()
      },

      // 处理普通流失败
      handleNormalStreamFailure(error) {
        console.log('📡 普通流播放失败，准备重试', error)

        // 清除当前播放记录
        this.lastPlayedUrl = null
        this.currentStreamUrl = null
        this.streamConnected = false
        this.aiStreamConnected = false

        // 进入重试逻辑
        if (!this.isDestroyed && this.retryCount < this.maxRetries) {
          this.scheduleRetry()
        } else if (this.retryCount >= this.maxRetries) {
          console.error(`已达到最大重试次数 ${this.maxRetries}，停止重试`)
        }
      },

      scheduleRetry() {
        if (this.isDestroyed) return

        this.retryCount++
        this.isReconnecting = true

        console.log(
          `准备第${this.retryCount}次重试，${this.retryInterval}ms后执行，将重新查询流地址`
        )

        this.retryTimer = setTimeout(() => {
          if (!this.isDestroyed) {
            // 重新开始流检测，从查询API开始
            this.startStreamCheck()
          }
        }, this.retryInterval)
      },

      startStreamCheck() {
        this.isLoading = true
        this.streamError = false
        this.isUpgrading = false
        // 注意：不重置 retryCount，保持重试计数
        console.log('开始流检测，当前重试次数:', this.retryCount)
        this.checkAndPlayStream()
      },

      // 通过HTTP请求测试AI流是否可用（检测404）
      async testAIStreamAvailability() {
        if (this.isDestroyed) {
          console.log('🚫 跳过AI流测试 - 组件已销毁')
          return false
        }

        if (this.checkingUrl) {
          console.log('🚫 跳过AI流测试 - 正在检测中')
          return false
        }

        this.checkingUrl = true
        console.log('🔍 开始测试AI流可用性...')

        try {
          // 构建AI流URL
          const aiStreamUrl = `https://srs.3366998.xyz/live/ai/${this.device.sn}.flv`
          console.log('🎯 测试AI流URL:', aiStreamUrl)

          // 使用fetch进行HEAD请求测试流是否存在
          const response = await fetch(aiStreamUrl, {
            method: 'HEAD',
            timeout: 5000, // 5秒超时
          })

          const isAvailable = response.ok && response.status !== 404
          console.log('📊 AI流测试结果:', {
            status: response.status,
            statusText: response.statusText,
            isAvailable: isAvailable
          })

          return isAvailable
        } catch (error) {
          console.log('❌ AI流测试失败:', error.message)
          // 网络错误或超时也认为AI流不可用
          return false
        } finally {
          this.checkingUrl = false
        }
      },

      // 直接播放AI流（无需检测，使用固定URL格式）
      async playAIStreamDirect() {
        console.log('🎯 直接播放AI流（固定URL）')

        // 使用固定的AI流URL格式
        const aiStreamUrl = `https://srs.3366998.xyz/live/ai/${this.device.sn}.flv`

        // 清除AI流冷却时间
        this.aiStreamFailedAt = null

        // 设置AI流状态
        this.aiStreamConnected = true
        this.streamConnected = true
        this.currentStreamUrl = aiStreamUrl
        this.isLoading = false
        this.isReconnecting = false
        this.isUpgrading = false

        // 停止所有检测
        this.stopAllDetection()

        console.log('✅ AI流地址（固定格式）:', this.currentStreamUrl)
        this.createOrUpdatePlayer('AI流')
      },

      // 直接播放普通流（无需检测，使用固定URL格式）
      async playNormalStreamDirect() {
        console.log('📡 直接播放普通流（固定URL）')

        // 使用固定的普通流URL格式
        const normalStreamUrl = `https://srs.3366998.xyz/live/sk/${this.device.sn}.flv`

        // 设置普通流状态
        this.aiStreamConnected = false
        this.streamConnected = true
        this.currentStreamUrl = normalStreamUrl
        this.isLoading = false
        this.isReconnecting = false

        console.log('✅ 普通流地址（固定格式）:', this.currentStreamUrl)
        this.createOrUpdatePlayer('普通流')
      },

      // 检查AI流是否在冷却期
      isAIStreamInCooldown() {
        if (!this.aiStreamFailedAt) return false

        const now = Date.now()
        const timeSinceFailure = now - this.aiStreamFailedAt
        const inCooldown = timeSinceFailure < this.aiStreamCooldown

        if (inCooldown) {
          const remainingTime = Math.ceil(
            (this.aiStreamCooldown - timeSinceFailure) / 1000
          )
          console.log(`⏳ AI流在冷却期，剩余${remainingTime}秒`)
        }

        return inCooldown
      },

      // 主要的流检测和播放逻辑
      async checkAndPlayStream() {
        if (this.isDestroyed) return

        console.log('🚀 开始流检测和播放流程')

        try {
          // 检查AI流是否在冷却期
          if (this.isAIStreamInCooldown()) {
            console.log('⏳ AI流在冷却期，直接播放普通流')
            await this.playNormalStreamDirect()
            return
          }

          // 先直接播放普通流（无需检测）
          console.log('📡 直接播放普通流（无需检测）')
          await this.playNormalStreamDirect()

          // 普通流播放后，启动AI流可用性测试
          this.startAIStreamTest()
        } catch (error) {
          console.error('❌ 流播放过程出错:', error)
          this.scheduleRetry()
        }
      },

      // 启动AI流可用性测试（在播放普通流时使用）
      startAIStreamTest() {
        if (this.isDestroyed || this.aiStreamConnected) {
          return
        }

        console.log('🔍 启动AI流可用性测试定时器')
        this.isUpgrading = true

        this.retryTimer = setTimeout(() => {
          if (this.isDestroyed || this.aiStreamConnected) {
            console.log('🚫 取消AI流测试 - 组件已销毁或AI流已连接')
            this.isUpgrading = false
            return
          }

          console.log('🔍 执行AI流可用性测试')
          this.checkForAIStreamAvailability()
        }, this.retryInterval)
      },

      // 检测AI流可用性（通过HTTP测试404状态）
      async checkForAIStreamAvailability() {
        if (this.isDestroyed) {
          this.isUpgrading = false
          return
        }

        // 如果当前已经是AI流且正在播放，跳过检测
        if (this.aiStreamConnected && this.isPlaying) {
          this.isUpgrading = false
          return
        }

        // 检查AI流是否在冷却期
        if (this.isAIStreamInCooldown()) {
          console.log('⏳ AI流在冷却期，跳过测试')
          this.startAIStreamTest() // 继续等待
          return
        }

        console.log('🔍 测试AI流是否可用...')

        try {
          const isAIStreamAvailable = await this.testAIStreamAvailability()

          if (isAIStreamAvailable) {
            console.log('🎯 AI流可用，切换播放')
            // 清除冷却时间
            this.aiStreamFailedAt = null
            await this.playAIStreamDirect()
          } else {
            console.log('📡 AI流不可用（404），继续使用普通流')
            this.handleAIStreamNotAvailable()
          }
        } catch (error) {
          console.error('❌ AI流测试出错:', error)
          this.handleAIStreamNotAvailable()
        }
      },

      // 处理AI流不可用的情况
      handleAIStreamNotAvailable() {
        // 如果当前是AI流但测试不可用，需要切换到普通流
        if (this.aiStreamConnected) {
          console.log('🔄 AI流不可用（404），切换到普通流（使用固定URL）')
          this.aiStreamConnected = false
          // 直接播放普通流，无需检测
          this.playNormalStreamDirect()
        } else {
          // 当前是普通流，继续测试AI流
          console.log('📡 继续测试AI流可用性')
          this.startAIStreamTest()
        }
      },

      // 停止所有检测
      stopAllDetection() {
        console.log('🛑 停止所有流检测')
        this.stopRetryTimer()
        this.isUpgrading = false
        this.isReconnecting = false
        this.isLoading = false
        this.checkingUrl = false
      },

      // 修改截图按钮事件
      modifyScreenshotButton() {
        const playerElement = document.getElementById(this.playerId)
        if (!playerElement) {
          console.warn('播放器元素未找到，无法修改截图按钮')
          return
        }

        const screenshotBtn = playerElement.querySelector('.easyplayer-screenshot')
        if (screenshotBtn) {
          // 克隆按钮并替换，移除原有事件监听器
          const newBtn = screenshotBtn.cloneNode(true)
          screenshotBtn.parentNode.replaceChild(newBtn, screenshotBtn)

          // 添加自定义点击事件
          newBtn.addEventListener('click', (e) => {
            e.preventDefault()
            e.stopPropagation()
            console.log('🎯 截图按钮被点击 - 执行自定义截图上传')
            this.takeScreenshot()
          })

          console.log('✅ 已修改截图按钮点击事件')
        }
      },

      // 截图上传方法（统一处理）
      async takeScreenshot() {
        if (!this.playerInfo || !this.isPlaying) {
          this.$message.warning('播放器未就绪或未在播放状态')
          return
        }

        if (!this.device || !this.device.sn) {
          this.$message.error('设备信息不完整，无法上传截图')
          return
        }

        // 设置按钮loading状态
        this.screenshotLoading = true

        // 显示加载提示
        const loadingMessage = this.$message({
          message: '正在截图并上传...',
          type: 'info',
          duration: 0, // 不自动关闭
          showClose: false
        })

        try {
          console.log('🎯 开始截图上传...')

          // 生成文件名
          const timestamp = new Date().getTime()
          const filename = `${timestamp}`

          // 使用播放器的screenshot方法直接获取blob
          const screenshotBlob = this.playerInfo.screenshot(filename, 'jpeg', 1.0, 'blob')
          console.log('✅ 获取截图blob成功')

          // 直接上传blob
          await this.uploadScreenshotBlob(screenshotBlob, filename, 'jpeg')

        } catch (error) {
          console.error('❌ 截图失败:', error)
          this.$message.error(`截图失败: ${error.message || error}`)
        } finally {
          // 关闭加载提示和按钮loading状态
          loadingMessage.close()
          this.screenshotLoading = false
        }
      },

      // 直接上传截图blob
      async uploadScreenshotBlob(blob, filename, format = 'jpeg') {
        if (!this.device || !this.device.sn) {
          throw new Error('设备信息不完整，无法上传截图')
        }

        try {
          console.log('🚀 开始上传截图blob...')

          // 生成文件名（如果没有提供）
          const timestamp = new Date().getTime()
          const finalFilename = filename || `screenshot_${this.device.sn}_${timestamp}`

          // 创建FormData进行上传
          const formData = new FormData()
          formData.append('file', blob, `${finalFilename}.${format}`)
          formData.append('bizCode', 'dronePhoto')
          formData.append('bizId', this.device.sn)
          formData.append('folder', folderName)

          // 调用上传API
          const response = await uploadMinio(formData)

          if (response && response.result && response.result.length > 0) {
            this.$message.success('截图上传成功！')
            console.log('📸 截图上传成功:', response.result[0])
            return response.result[0]
          } else {
            console.log('上传响应异常')
          }

        } catch (error) {
          console.error('❌ 截图上传失败:', error)
          this.$message.error(`截图上传失败: ${error.message || error}`)
          throw error
        }
      },

      handleClose() {
        console.log('开始关闭EasyPlayer直播窗口')
        this.isDestroyed = true
        this.showPlayer = false
        this.stopRetryTimer()
        this.destroyPlayer()
        this.resetPlayerState()
        this.$emit('close')
        console.log('EasyPlayer直播窗口关闭完成')
      },
    },
  }
</script>

<style lang="scss" scoped>
  .easyplayer-live-modal {
    ::v-deep .el-dialog {
      border-radius: 8px;
      overflow: hidden;
    }

    ::v-deep .el-dialog__header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 20px 24px;

      .el-dialog__title {
        color: white;
        font-weight: 500;
      }

      .el-dialog__close {
        color: white;
        font-size: 20px;

        &:hover {
          color: #f0f0f0;
        }
      }
    }

    ::v-deep .el-dialog__body {
      padding: 0;
    }

    ::v-deep .el-dialog__footer {
      background: #f8f9fa;
      border-top: 1px solid #e9ecef;
      padding: 16px 24px;
      text-align: center;
    }

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 24px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.08);
      background: white;

      .stream-info {
        h3 {
          margin: 0;
          color: #303133;
          font-size: 18px;
          font-weight: 500;
        }

        .stream-url {
          margin: 5px 0 0 0;
          color: #606266;
          font-size: 12px;
          font-family: monospace;
          word-break: break-all;

          .stream-type {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
              sans-serif;
            font-size: 11px;
            font-weight: bold;
            padding: 2px 6px;
            border-radius: 3px;
            margin-left: 8px;

            &.ai-stream {
              background: #e7f3ff;
              color: #1890ff;
            }

            &.normal-stream {
              background: #f6ffed;
              color: #52c41a;
            }
          }
        }

        .last-played-url {
          margin: 3px 0 0 0;
          color: #909399;
          font-size: 11px;
          font-family: monospace;
          word-break: break-all;
          opacity: 0.8;
        }
      }

      .live-status {
        display: flex;
        align-items: center;
      }
    }

    .player-content {
      position: relative;
      background: #000;
      min-height: 400px;
    }

    .player-wrapper {
      position: relative;
      width: 100%;
      height: 500px;
      background: #000;

      .easy-player-container {
        width: 100%;
        height: 100%;
        background: #000;
      }
    }

    .waiting-container {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 400px;
      background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
      color: white;

      .waiting-content {
        text-align: center;

        .waiting-icon {
          font-size: 48px;
          margin-bottom: 16px;
          opacity: 0.8;
          animation: pulse 2s infinite;
        }

        p {
          font-size: 16px;
          margin: 0;
          opacity: 0.9;
        }
      }
    }

    .dialog-footer {
      .el-button {
        min-width: 120px;
        border-radius: 6px;
        font-weight: 500;
      }
    }
  }

  @keyframes pulse {
    0% {
      opacity: 0.8;
      transform: scale(1);
    }
    50% {
      opacity: 1;
      transform: scale(1.1);
    }
    100% {
      opacity: 0.8;
      transform: scale(1);
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .easyplayer-live-modal {
      ::v-deep .el-dialog {
        width: 95% !important;
        margin: 5vh auto !important;
      }

      .player-wrapper {
        height: 300px;
      }

      .waiting-container {
        height: 300px;
      }

      .modal-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;

        .live-status {
          align-self: flex-end;
        }
      }
    }
  }
</style>
