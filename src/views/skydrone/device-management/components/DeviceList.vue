<template>
  <div class="device-list">
    <div v-if="loading" class="loading-container">
      <el-skeleton animated :rows="8" />
    </div>

    <div v-else-if="filteredDevices.length === 0" class="empty-container">
      <el-empty description="该项目下暂无设备" />
    </div>

    <div v-else class="device-grid">
      <div v-for="device in filteredDevices" :key="device.sn" class="device-column">
        <!-- 飞行器设备卡片 -->
        <el-card class="device-card drone-card" shadow="hover">
          <!-- <div class="device-header">
            <div class="device-icon">
              <i class="el-icon-position" />
            </div>
          </div> -->
          <el-image class="device-image"
            src="https://minio.jxth.com.cn/files/hnsz/c0560a7f-ce75-454b-ad86-36520cf19dfb.png" fit="cover" />
          <div class="device-info">
            <h4 class="device-name">
              {{
              device.nickname ||
              (device.device_model && device.device_model.name) ||
              '飞行器'
              }}
            </h4>
            <p class="device-sn">SN: {{ device.sn }}</p>
            <p class="device-model">
              型号:
              {{ (device.device_model && device.device_model.name) || '未知' }}
            </p>
            <p class="device-type">
              类型:
              {{ (device.device_model && device.device_model.class) || '未知' }}
            </p>
          </div>

          <div class="device-actions">
            <div class="button-group vertical">
              <!-- 直播按钮行 -->
              <div class="button-row">
                <el-button class="action-button half-width" icon="el-icon-video-camera" size="small" type="warning"
                  @click="handleEasyPlayerLiveClick(device)">
                  开始直播
                </el-button>
                <el-button class="action-button half-width" icon="el-icon-video-camera-solid" size="small" type="danger"
                  @click="handleStopStream(device)">
                  停止直播
                </el-button>
              </div>
              <!-- EasyPlayer直播按钮 -->
<!--              <el-button class="action-button" icon="el-icon-video-play" size="small" type="success"-->
<!--                @click="handleEasyPlayerLiveClick(device)">-->
<!--                EasyPlayer直播-->
<!--              </el-button>-->
              <!-- 其他按钮 -->
              <el-button class="action-button" icon="el-icon-video-play" size="small" type="success"
                @click="handlePlaybackClick(device)">
                开始直播2
              </el-button>
              <el-button class="action-button" icon="el-icon-monitor" size="small" type="primary"
                @click="handleCockpitClick(device)">
                虚拟座舱
              </el-button>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'DeviceList',
    props: {
      devices: {
        type: Array,
        default: () => [],
      },
      loading: {
        type: Boolean,
        default: false,
      },
      selectedProject: {
        type: Object,
        default: null,
      },
    },
    computed: {
      filteredDevices() {
        // 只显示 is_org_device 为 true 的设备
        return this.devices.filter((device) => device.is_org_device === true)
      },
    },
    methods: {
      handleLiveClick(device, cameraIndex, deviceType) {
        this.$emit('device-live', {
          device,
          cameraIndex,
          deviceType,
        })
      },

      handleStopStream(device) {
        this.$emit('stop-stream', { device })
      },
      handleCockpitClick(device) {
        this.$emit('device-cockpit', { device })
      },

      handlePlaybackClick(device) {
        this.$emit('device-playback', { device })
      },

      handleEasyPlayerLiveClick(device) {
        this.$emit('easyplayer-live', { device })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .device-list {
    height: 100%;

    .loading-container {
      padding: 20px;
    }

    .empty-container {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 300px;
    }

    .device-grid {
      display: grid !important;
      grid-template-columns: repeat(auto-fill, 280px) !important;
      gap: 20px !important;
      justify-content: start !important;
      align-items: start !important;
      width: 100% !important;

      .device-column {
        width: 280px !important;
        min-width: 280px !important;
        max-width: 280px !important;
        box-sizing: border-box !important;
      }

      .device-card {
        width: 100%;
        height: auto;
        margin-bottom: 0;
        transition: all 0.3s;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        &.drone-card {
          border-left: 4px solid #e6a23c;
        }

        .device-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;

          .device-icon i {
            font-size: 24px;
            color: #e6a23c;
          }
        }

        .device-info {
          margin-bottom: 16px;

          .device-name {
            margin: 0 0 8px 0;
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .device-sn,
          .device-model,
          .device-type,
          .device-mode {
            margin: 4px 0;
            font-size: 12px;
            color: #909399;
          }
        }

        .device-actions {
          text-align: center;
          margin-top: 15px;

          .button-group {
            display: flex;
            flex-direction: column;
            align-items: stretch;
            /* 拉伸子元素以填充容器 */
            gap: 10px;
          }

          .button-row {
            display: flex;
            gap: 8px;
            width: 100%;
          }

          .action-button {
            width: 100%;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;
            margin: 0;

            /* 强制文本居中 */
            span {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 100%;
            }

            i {
              margin-right: 5px;
            }

            &.half-width {
              width: calc(50% - 4px);
            }
          }
        }
      }
    }
  }
</style>
