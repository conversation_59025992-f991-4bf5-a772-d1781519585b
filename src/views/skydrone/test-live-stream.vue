<template>
  <div class="test-live-stream">
    <el-card>
      <div slot="header">
        <span>火山引擎RTC直播测试</span>
      </div>

      <div class="test-section">
        <h3>测试拉流地址</h3>
        <el-input
          v-model="testStreamUrl"
          type="textarea"
          :rows="3"
          placeholder="请输入火山引擎RTC拉流地址"
        />
        <div class="button-group">
          <el-button type="primary" @click="startTest">开始测试</el-button>
          <el-button @click="stopTest">停止测试</el-button>
          <el-button @click="useDefaultUrl">使用默认测试地址</el-button>
          <el-button @click="testRTCConnection" type="warning">测试RTC连接</el-button>
          <el-button @click="showTokenHelp" type="info">获取新Token</el-button>
        </div>

        <div v-if="rtcParams" class="params-display">
          <h4>解析的RTC参数:</h4>
          <pre>{{ JSON.stringify(rtcParams, null, 2) }}</pre>
        </div>
      </div>

      <div v-if="showPlayer" class="player-section">
        <h3>直播播放器</h3>
        <LivePlayer
          :stream-url="currentStreamUrl"
          :default-type="'4'"
          :show-controls="true"
          :auto-play="true"
          video-width="100%"
          video-height="480px"
          @play-success="onPlaySuccess"
          @play-error="onPlayError"
        />
      </div>

      <div class="log-section">
        <h3>测试日志</h3>
        <el-input
          v-model="logText"
          type="textarea"
          :rows="10"
          readonly
          placeholder="测试日志将显示在这里..."
        />
        <el-button @click="clearLog" size="small">清空日志</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import LivePlayer from '@/components/LivePlayer.vue'

export default {
  name: 'TestLiveStream',
  components: {
    LivePlayer
  },
  data() {
    return {
      testStreamUrl: '',
      currentStreamUrl: '',
      showPlayer: false,
      logText: '',
      rtcParams: null,
      defaultUrl: 'app_id=668f8a70f1f998012921dc72&expire_time=1748503459&room_id=1581F7FVC253400DP64H_88-0-0&token=001668f8a70f1f998012921dc72WwBE8PMBQwI4aKMLOGgbADE1ODFGN0ZWQzI1MzQwMERQNjRIXzg4LTAtMCgAd2luZG93XzE1MjY4NzQ5NjMwNTg2MDYwODBfMTc0ODUwMTA1ODQyOAEABACjCzhoIAC2PgGvD3opHdYTOzbPNI4PjICMaxhZ8GmpiRYQWBXk2A%3D%3D&user_id=window_1526874963058606080_1748501058428'
    }
  },
  methods: {
    startTest() {
      if (!this.testStreamUrl.trim()) {
        this.$message.error('请输入拉流地址')
        return
      }

      this.addLog('开始测试直播流...')
      this.addLog('拉流地址: ' + this.testStreamUrl)

      this.currentStreamUrl = this.testStreamUrl
      this.showPlayer = true
    },

    stopTest() {
      this.addLog('停止测试直播流')
      this.showPlayer = false
      this.currentStreamUrl = ''
    },

    useDefaultUrl() {
      this.testStreamUrl = this.defaultUrl
      this.addLog('已设置默认测试地址')
    },

    // 测试RTC连接
    async testRTCConnection() {
      if (!this.testStreamUrl.trim()) {
        this.$message.error('请先输入拉流地址')
        return
      }

      try {
        this.addLog('开始测试RTC连接...')

        // 解析参数
        this.rtcParams = this.parseUrlParams(this.testStreamUrl)
        this.addLog('解析参数: ' + JSON.stringify(this.rtcParams, null, 2))

        // 检查必要参数
        const requiredParams = ['app_id', 'room_id', 'token', 'user_id']
        const missingParams = requiredParams.filter(param => !this.rtcParams[param])

        if (missingParams.length > 0) {
          this.addLog('❌ 缺少必要参数: ' + missingParams.join(', '))
          this.$message.error('缺少必要参数: ' + missingParams.join(', '))
          return
        }

        this.addLog('✅ 参数验证通过')

        // 检查token是否过期
        const expireTime = parseInt(this.rtcParams.expire_time)
        const currentTime = Math.floor(Date.now() / 1000)

        if (expireTime && expireTime < currentTime) {
          const expiredDate = new Date(expireTime * 1000).toLocaleString()
          const timeDiff = Math.floor((currentTime - expireTime) / 60) // 分钟
          this.addLog(`❌ Token已过期！`)
          this.addLog(`   过期时间: ${expiredDate}`)
          this.addLog(`   已过期: ${timeDiff} 分钟`)
          this.addLog(`   建议: 请重新获取拉流地址`)
          this.$message.error('Token已过期，请获取新的拉流地址')
          return
        } else if (expireTime) {
          const validDate = new Date(expireTime * 1000).toLocaleString()
          const remainingTime = Math.floor((expireTime - currentTime) / 60) // 分钟
          this.addLog(`✅ Token有效`)
          this.addLog(`   过期时间: ${validDate}`)
          this.addLog(`   剩余时间: ${remainingTime} 分钟`)
        }

        this.addLog('RTC连接参数检查完成')
        this.$message.success('RTC连接参数检查完成，可以开始测试')

      } catch (error) {
        this.addLog('❌ RTC连接测试失败: ' + error.message)
        this.$message.error('RTC连接测试失败')
      }
    },

    // 解析URL参数
    parseUrlParams(url) {
      const params = {}

      // 处理可能包含协议的URL
      let paramString = url
      if (url.includes('://')) {
        const urlObj = new URL(url)
        paramString = urlObj.search.substring(1)
      } else if (url.includes('?')) {
        paramString = url.split('?')[1]
      }

      // 分割参数
      const urlParts = paramString.split('&')

      urlParts.forEach(part => {
        const [key, value] = part.split('=')
        if (key && value) {
          params[key] = decodeURIComponent(value)
        }
      })

      return params
    },

    onPlaySuccess(playerType) {
      this.addLog(`✅ 播放成功! 播放器类型: ${playerType}`)
      this.$message.success('直播连接成功')
    },

    onPlayError(error) {
      this.addLog(`❌ 播放失败: ${error.message || error}`)
      this.$message.error('直播连接失败')
    },

    addLog(message) {
      const timestamp = new Date().toLocaleTimeString()
      this.logText += `[${timestamp}] ${message}\n`

      // 自动滚动到底部
      this.$nextTick(() => {
        const textarea = this.$el.querySelector('.log-section textarea')
        if (textarea) {
          textarea.scrollTop = textarea.scrollHeight
        }
      })
    },

    clearLog() {
      this.logText = ''
    },

    // 显示获取新Token的帮助信息
    showTokenHelp() {
      this.$alert(
        '要获取新的拉流地址，请按以下步骤操作：\n\n' +
        '1. 访问司空2设备管理页面\n' +
        '2. 选择对应的项目和设备\n' +
        '3. 点击"开始直播"按钮\n' +
        '4. 系统会自动调用后端API获取新的拉流地址\n' +
        '5. 将新的拉流地址复制到此测试页面\n\n' +
        '注意：每个拉流地址都有时效性，通常有效期为几小时。',
        '获取新Token帮助',
        {
          confirmButtonText: '知道了',
          type: 'info'
        }
      )
    }
  },
  mounted() {
    this.addLog('火山引擎RTC直播测试页面已加载')
    this.addLog('请输入拉流地址进行测试')
  }
}
</script>

<style lang="scss" scoped>
.test-live-stream {
  padding: 20px;

  .test-section {
    margin-bottom: 30px;

    h3 {
      margin-bottom: 15px;
      color: #303133;
    }

    .button-group {
      margin-top: 15px;

      .el-button {
        margin-right: 10px;
      }
    }
  }

  .player-section {
    margin-bottom: 30px;

    h3 {
      margin-bottom: 15px;
      color: #303133;
    }
  }

  .log-section {
    h3 {
      margin-bottom: 15px;
      color: #303133;
    }

    .el-button {
      margin-top: 10px;
    }
  }

  .params-display {
    margin-top: 20px;
    padding: 15px;
    background-color: #f5f7fa;
    border-radius: 4px;
    border: 1px solid #e4e7ed;

    h4 {
      margin: 0 0 10px 0;
      color: #303133;
      font-size: 14px;
    }

    pre {
      margin: 0;
      padding: 10px;
      background-color: #fff;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      font-size: 12px;
      color: #606266;
      overflow-x: auto;
    }
  }
}
</style>
