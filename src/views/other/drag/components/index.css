/*画布容器*/
#efContainer {
  position: relative;
  flex: 1;
  overflow: scroll;
}

/*顶部工具栏*/
.vab-tooltar {
  z-index: 3;
  box-sizing: border-box;
}

.jtk-overlay {
  color: #4a4a4a;
  cursor: pointer;
}

/*节点菜单*/
.vab-node-pmenu {
  display: block;
  width: 225px;
  height: 32px;
  padding-left: 5px;
  font-weight: bold;
  line-height: 32px;
  color: #4a4a4a;
  cursor: pointer;
}

.vab-node-menu-li {
  width: 150px;
  padding: 5px 5px 5px 8px;
  margin: 5px 0 5px 0;
  color: #565758;
  border: 1px dashed #e0e3e7;
  border-radius: 2px;
}

.vab-node-menu-li:hover {
  padding-left: 5px;
  /* 设置移动样式*/
  cursor: move;
  background-color: #f0f7ff;
  border: 1px dashed #1879ff;
  border-left: 4px solid #1879ff;
}

.vab-node-menu-ul {
  padding-left: 20px;
  list-style: none;
}

/*节点的最外层容器*/
.vab-node-content {
  position: absolute;
  display: flex;
  width: 170px;
  height: 32px;
  background-color: #fff;
  border: 1px solid #e0e3e7;
  border-radius: 2px;
}

.vab-node-content:hover {
  /* 设置移动样式*/
  cursor: move;
  background-color: #f0f7ff;
  /*box-shadow: #1879FF 0px 0px 12px 0px;*/
  border: 1px dashed #1879ff;
}

/*节点激活样式*/
.vab-node-active {
  background-color: #f0f7ff;
  /*box-shadow: #1879FF 0px 0px 12px 0px;*/
  border: 1px solid #1879ff;
}

/*节点左侧的竖线*/
.vab-node-left {
  width: 4px;
  background-color: #1879ff;
  border-radius: 2px 0 0 2px;
}

/*节点左侧的图标*/
.vab-node-left-ico {
  margin-left: 8px;
  line-height: 32px;
}

.vab-node-left-ico:hover {
  /* 设置拖拽的样式 */
  cursor: crosshair;
}

/*节点显示的文字*/
.vab-node-text {
  width: 100px;
  margin-left: 8px;
  overflow: hidden;
  font-size: 12px;
  line-height: 32px;
  color: #565758;
  text-align: center;
  text-overflow: ellipsis;
  /* 设置超出宽度文本显示方式*/
  white-space: nowrap;
}

/*节点右侧的图标*/
.vab-node-right-ico {
  position: absolute;
  right: 5px;
  line-height: 32px;
  color: #84cf65;
  cursor: default;
}

/*节点的几种状态样式*/
.el-node-state-success {
  position: absolute;
  right: 5px;
  line-height: 32px;
  color: #84cf65;
  cursor: default;
}

.el-node-state-error {
  position: absolute;
  right: 5px;
  line-height: 32px !important;
  color: #f56c6c;
  cursor: default;
}

.el-node-state-warning {
  position: absolute;
  right: 5px;
  line-height: 32px;
  color: #e6a23c;
  cursor: default;
}

.el-node-state-running {
  position: absolute;
  right: 5px;
  line-height: 32px;
  color: #84cf65;
  cursor: default;
}

/* 连线中的label 样式*/
.jtk-overlay.flowLabel:not(.aLabel) {
  padding: 4px 10px;
  color: #565758 !important;
  background-color: white;
  border: 1px solid #e0e3e7;
  border-radius: 2px;
}

.vab-dot {
  background-color: #1879ff;
  border-radius: 2px;
}

.vab-dot-hover {
  background-color: red;
}

.vab-rectangle {
  background-color: #1879ff;
}

.vab-rectangle-hover {
  background-color: red;
}

.vab-drop-hover {
  border: 1px dashed #1879ff;
}
