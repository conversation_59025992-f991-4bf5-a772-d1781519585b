<template>
  <el-dialog v-drag append-to-body center :close-on-click-modal="false" title="详情" top="10vh"
    :visible.sync="dialogDetailVisible" width="1000px">
    <div style="display: flex; max-height: 75vh; overflow-y: scroll">
      <div style="flex: 1">
        <el-descriptions v-loading="formLoading" border class="margin-top" :column="3" size="medium">
          <el-descriptions-item label-style="width: 150px">
            <template slot="label">类型</template>
            {{ formData.type == '1' ? '好建议' : formData.type == '2' ? '金点子' : '急难愁盼要接解决' }}
          </el-descriptions-item>
          <el-descriptions-item label-style="width: 150px">
            <template slot="label">姓名</template>
            {{ formData.name }}
          </el-descriptions-item>
          <el-descriptions-item label-style="width: 150px">
            <template slot="label">单位名称</template>
            {{ formData.departName }}
          </el-descriptions-item>
          <el-descriptions-item label-style="width: 150px">
            <template slot="label">联系方式</template>
            {{ formData.telephone }}
          </el-descriptions-item>
          <el-descriptions-item :span="2" label-style="width: 150px">
            <template slot="label">时间</template>
            {{ formData.time }}
          </el-descriptions-item>
          <el-descriptions-item :span="3" label-style="width: 150px">
            <template slot="label">内容</template>
            {{ formData.content }}
          </el-descriptions-item>
          <el-descriptions-item label-style="width: 150px">
            <template slot="label">备注</template>
            {{ formData.remark }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="danger" @click="dialogDetailVisible = false">
        关闭
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  export default {
    components: {

    },
    props: {

    },
    data() {
      return {
        dialogDetailVisible: false,
        formLoading: false,
        formData: {},
      }
    },
    methods: {
      showDialog(row) {
        this.formData = { ...row }
        this.dialogDetailVisible = true
      },
    },
  }
</script>
<style lang="scss" scoped></style>