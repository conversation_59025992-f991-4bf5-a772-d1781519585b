<template>
  <div class="card-box">
    <el-card shadow="hover">
      <template #header>
        <vab-icon icon="calendar-check-fill" />
        日程安排
      </template>
    </el-card>
  </div>
</template>

<script>
  export default {
    data() {
      return {

      }
    }
  }
</script>

<style lang="scss" scoped>
  ::v-deep  .el-card {
     height: 290px;
     box-sizing: border-box;
     .el-card__header {
       padding: 15px 20px;
       font-size: 16px;
       color: #626b7d;
       i {
         font-size: 18px;
       }
     }
   }
</style>
