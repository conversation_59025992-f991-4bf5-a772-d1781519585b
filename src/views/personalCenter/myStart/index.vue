<template>
  <div
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <el-table
      ref="tableSort"
      v-loading="listLoading"
      border
      :data="list"
      :height="height + 50"
      :size="lineHeight"
      stripe
    >
      <el-table-column align="center" label="序号" width="60">
        <template #default="{ $index }">
          {{ getIndex($index) }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="名称" prop="titleName" />
      <el-table-column align="center" label="申请人" prop="createByName" />
      <el-table-column
        align="center"
        label="申请部门"
        prop="createDepartName"
      />
      <el-table-column
        align="center"
        label="时间"
        prop="createTime"
        width="250"
      />
      <el-table-column align="center" label="是否已读">
        <template #default="{ row }">
          <el-tag v-if="row.isRead === '0'" type="danger">未读</el-tag>
          <el-tag v-else type="success">已读</el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="250">
        <template #default="{ row }">
          <el-button type="primary" @click="viewDetail(row)">查看</el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
  </div>
</template>

<script>
  import { mapGetters } from 'vuex'
  import tableMix from '@/views/mixins/table'
  import { webSocketUrl } from '@/utils/constants'
  import { getToken } from '@/utils/token'
  import {
    getMyInitiateMetering,
    saveWorkflowConfirmation,
  } from '@/api/workflow/workflow-api.js'
  import { genUUID } from '@/utils/th_utils'
  export default {
    mixins: [tableMix],
    computed: {
      ...mapGetters({
        userId: 'user/userId',
        oneDimensionRoutes: 'routes/oneDimensionRoutes',
      }),
    },
    data() {
      return {
        formMinHeight: 0,
        formMaxHeight: 0,
        queryForm: {},
        exportDataList: [], //需要导出的数据
        listLoading: false,
        myToDoPath:
          webSocketUrl + 'ws?AUTHTOKEN=' + getToken() + '&GROUPID=myToDo',
      }
    },
    created() {
      this.connection()
    },
    methods: {
      connection() {
        this.socket = new WebSocket(this.myToDoPath)
        // 监听socket连接
        this.socket.onopen = this.open
        // 监听socket错误信息
        this.socket.onerror = this.error
        // 监听socket消息
        this.socket.onmessage = this.getMessage

        this.socket.onclose = this.close
      },
      open() {
        this.fetchData()
      },
      error() {
        console.log('待办信息获取失败！')
      },
      getMessage() {
        this.fetchData()
      },
      getIndex(index) {
        return (this.pageInfo.curPage - 1) * this.pageInfo.pageSize + index + 1
      },
      async fetchData() {
        this.listLoading = true
        const queryData = {
          curPage: this.pageInfo.curPage,
          pageSize: this.pageInfo.pageSize,
          userId: this.userId,
          toDoType: 'initiate',
        }
        await getMyInitiateMetering(queryData).then((response) => {
          this.listLoading = false
          this.list = response.result.records
          this.pageInfo.total = Number(response.result.total)
        })
      },
      async viewDetail(item) {
        const params = {
          processInstanceId: item.processInstanceId,
          toDoType: item.toDoType,
          taskId: item.taskId,
          isAdd: true,
          id: genUUID(),
        }
        if (item.isRead === '0') {
          await saveWorkflowConfirmation(params).then((res) => {
            if (res.code === 200) {
              this.fetchData()
            }
          })
        }

        const routePath = this.oneDimensionRoutes.find(
          (val) => val.name === item.bizCode
        )
        if (routePath) {
          const path = `${routePath.path}?id=${item.id}&&reach=detail`
          this.$openPage(path)
        } else {
          this.$baseMessage(
            '跳转失败，找不到页面路径。',
            'error',
            'vab-hey-message-error'
          )
        }
      },
    },
  }
</script>

<style lang="scss" scoped>
  $base: '.depart-management';

  #{$base}-container {
    padding: 0 !important;
    background: $base-color-background !important;

    &.vab-fullscreen {
      padding: 20px !important;
    }
  }
</style>
