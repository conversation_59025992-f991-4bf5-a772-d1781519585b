<template>
  <el-dialog
    title="附件"
    :close-on-click-modal="false"
    append-to-body
    :visible.sync="dialogDetailVisible"
    width="1000px"
  >
    <UploadLargeFileFdfsPopup ref="UploadLargeFileFdfsPopup" />
  </el-dialog>
</template>

<script>
import UploadLargeFileFdfsPopup from '@/views/common/UploadLargeFileFdfsPopup.vue'
export default {
  components: {
    UploadLargeFileFdfsPopup,
  },
  data() {
    return {
      dialogDetailVisible: false,
      formloading: false,
    }
  },
  methods: {
    async showDialog(report) {
      this.dialogDetailVisible = true
      this.$nextTick(() => {
        this.$refs.UploadLargeFileFdfsPopup.getParamsData({
          bizId: report.id,
          bizCode: 'fillInVisits',
          isShow: false,
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
