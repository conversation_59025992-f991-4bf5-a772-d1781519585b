<template>
  <el-dialog
    title="日志接收人"
    :close-on-click-modal="false"
    append-to-body
    :visible.sync="dialogDetailVisible"
    width="600px"
    @closed="closeDialog"
  >
    <div class="tab-box" v-loading="formloading" v-if="dialogDetailVisible">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="接收范围" name="receiver"></el-tab-pane>
        <el-tab-pane :label="`已读 ${ifRead}`" name="ifRead"></el-tab-pane>
        <el-tab-pane :label="`未读 ${unRead}`" name="unRead"></el-tab-pane>
      </el-tabs>
      <div class="receive-list">
        <div class="receive-item receive-tips" v-if="this.activeTab === 'receiver'">发送到人 {{ this.receiveList.length }}</div>
        <div class="receive-item" v-for="item in list" :key="item.id">{{ item.userName }}</div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
  import {
    getDataList as getReportIfReadList,
  } from '@/api/administrativeManagement/meetingMinutesApprovalManage-api'
  export default {
    data() {
      return {
        dialogDetailVisible: false,
        formloading: false,
        activeTab: 'receiver',
        receiveList: []
      }
    },
    computed: {
      list() {
        if(this.activeTab === 'ifRead') {
          return this.receiveList.filter(item => item.ifRead === 1)
        } else if(this.activeTab === 'unRead') {
          return this.receiveList.filter(item => item.ifRead === 0)
        } else {
          return this.receiveList
        }
      },
      ifRead() {
        const ifRead = this.receiveList.filter(item => item.ifRead == '1')
        return ifRead.length
      },
      unRead() {
        const unRead = this.receiveList.filter(item => item.ifRead == '0')
        return unRead.length
      }
    },
    methods: {
      async showDialog(report) {
        this.dialogDetailVisible = true
        this.formloading = true
        const { result } = await getReportIfReadList({bizId: report.id})
        this.receiveList = result
        this.formloading = false
      },
      closeDialog() {
        this.activeTab = 'receiver'
      },
    }
  }
</script>

<style lang="scss" scoped>
  ::v-deep .el-dialog__body {
    padding: 0;
  }
  .tab-box {
    padding: 10px 20px;
    .receive-list {
      width: 100%;
      height: 320px;
      overflow-y: scroll;
      .receive-item {
        width: 100%;
        box-sizing: border-box;
        padding: 15px 10px;
        border-bottom: 1px solid #ebeef5;
        &:last-child {
          border-bottom: none;
        }
        &.receive-tips {
          padding: 10px;
          font-size: 12px;
          background-color: rgba(232, 244, 255, .8);
        }
      }
    }
  }
</style>
