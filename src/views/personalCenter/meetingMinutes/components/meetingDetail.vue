<template>
  <el-dialog
    v-drag
    append-to-body
    :before-close="closeBtn"
    center
    :close-on-click-modal="false"
    title="日志详情"
    top="5vh"
    :visible.sync="dialogDetailVisible"
    width="1040px"
  >
    <el-descriptions border class="margin-top" :column="3" size="medium">
      <el-descriptions-item>
        <template slot="label">单号</template>
        {{ formData.serialNumber }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">记录人</template>
        {{ formData.createByName }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">填写日期</template>
        {{ formData.timingTime ? formData.timingTime : formData.reportTime }}
      </el-descriptions-item>
      <el-descriptions-item :span="3">
        <template slot="label">会议主题</template>
         {{ formData.meetingName }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">主持人</template>
        {{ formData.hostName }}
      </el-descriptions-item>
        <el-descriptions-item>
        <template slot="label">会议地点</template>
        {{ formData.meetingAddress }}
      </el-descriptions-item>
        <el-descriptions-item>
        <template slot="label">会议时间</template>
        {{ formData.meetingDate }}
      </el-descriptions-item>
      <el-descriptions-item :span="3">
        <template slot="label">参会人员</template>
         {{ formData.attendeesName }}
      </el-descriptions-item>
      <el-descriptions-item :span="3">
        <template slot="label">会议主要内容</template>
        <div v-html="formatText(formData.content)"></div>
      </el-descriptions-item>
      <el-descriptions-item :span="3">
        <template slot="label">备注</template>
        <div v-html="formatText(formData.remark)"></div>
      </el-descriptions-item>
      <el-descriptions-item :span="3">
        <template slot="label">接收人</template>
        <el-tag
          v-for="item in userName"
          :key="item.id"
          :type="item.ifRead === 0 ? 'danger' : 'success'"
        >
          {{ item.userName }}
        </el-tag>
      </el-descriptions-item>
    </el-descriptions>
    <div slot="footer" class="dialog-footer">
      <el-button type="danger" @click="closeBtn">关闭</el-button>
    </div>
    <UploadLargeFileFdfsPopup ref="UploadLargeFileFdfsPopup" />
  </el-dialog>
</template>

<script>
  import UploadLargeFileFdfsPopup from '@/views/common/UploadLargeFileFdfsPopup.vue'
  import { updateMeetingIfRead } from '@/api/administrativeManagement/meetingMinutesManage-api'
  import { getDataList } from '@/api/administrativeManagement/meetingMinutesApprovalManage-api'
  import { getDictList } from '@/api/system/dict-api'
  export default {
    components: {
      UploadLargeFileFdfsPopup,
    },
    props: {},
    data() {
      return {
        dialogDetailVisible: false,
        formloading: false,
        formData: {},
        receiveUserName: '',
        userName: [],
        labelName: {
          nowJobName: '',
          nextJobName: '',
        },
        reportTypeList: [],
      }
    },
    computed: {},
    created() {
      this.getReportTypeList()
    },
    methods: {
      formatText(text) {
        if (!text) return ''
        return text
          .replace(/ /g, '&nbsp;') // 保留空格
          .replace(/\n/g, '<br>') // 保留换行
      },
      getReportTypeList() {
        getDictList({ dictCode: 'reportJournalType' }).then((res) => {
          const result = res.result[0].children
          this.reportTypeList = result.map((item) => {
            return {
              label: item.dictName,
              value: item.id,
            }
          })
        })
      },
      async showDialog(row) {
        this.formData = { ...row }
        this.getApprovalList()
        this.dialogDetailVisible = true
        this.$nextTick(() => {
          this.$refs.UploadLargeFileFdfsPopup.getParamsData({
            bizId: this.formData.id,
            bizCode: 'meetingMinutesManage',
            isShow: false,
          })
        })
        const name = this.reportTypeList.find(
          (item) => item.value === this.formData.type
        ).label
        this.formData.typeName = name
        if (name == '日报') {
          this.labelName = {
            nowJobName: '今日完成工作',
            nextJobName: '未完成工作',
          }
        } else if (name == '周报') {
          this.labelName = {
            nowJobName: '本周完成工作',
            nextJobName: '下周工作计划',
          }
        } else if (name == '月报') {
          this.labelName = {
            nowJobName: '本月完成工作',
            nextJobName: '下月工作计划',
          }
        }
        if (this.formData.ifRead === 0) {
          const params = {
            ifRead: '1',
            bizId: this.formData.id,
            userId: this.userId,
          }
          await updateMeetingIfRead(params)
          this.$parent.fetchData()
        }
      },
      getApprovalList() {
        getDataList({ bizId: this.formData.id }).then((response) => {
          if (response.result.length > 0) {
            this.userName = response.result
            //this.receiveUserName = userName.join(',')
          }
        })
      },
      closeBtn() {
        this.formloading = false
        this.dialogDetailVisible = false
      },
    },
  }
</script>

<style lang="scss" scoped>
  .type-title {
    font-size: 16px;
    font-weight: bold;
  }
  .el-descriptions-item__label.is-bordered-label {
    width: 200px;
  }
</style>
